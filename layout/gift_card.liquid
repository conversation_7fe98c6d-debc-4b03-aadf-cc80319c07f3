<!doctype html>

<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.accent_color }}">

    <title>{{ shop.name }}</title>

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | img_url: '96x' }}" type="image/png">
    {%- endif -%}

    {% render 'social-meta-tags' %}
    {% render 'css-variables' %}

    {{ content_for_header }}

    <link rel="stylesheet" href="{{ 'theme.css' | asset_url }}">

    <script src="https://cdn.polyfill.io/v3/polyfill.min.js?unknown=polyfill&features=fetch,Element.prototype.closest,Element.prototype.remove,Element.prototype.classList,Array.prototype.includes,Array.prototype.fill,Object.assign,CustomEvent,IntersectionObserver,IntersectionObserverEntry,URL" defer></script>
    <script src="{{ 'libs.min.js' | asset_url }}" defer></script>
    <script src="{{ 'theme.js' | asset_url }}" defer></script>
    <script src="{{ 'custom.js' | asset_url }}" defer></script>
    <script src="{{ 'vendor/qrcode.js' | shopify_asset_url }}"></script>

    <script>
      // This allows to expose several variables to the global scope, to be used in scripts
      window.theme = {
        template: {{ template.name | json }}
      };

      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');

      // We do a quick detection of some features (we could use Modernizr but for so little...)
      (function() {
        document.documentElement.className += ((window.CSS && window.CSS.supports('(position: sticky) or (position: -webkit-sticky)')) ? ' supports-sticky' : ' no-supports-sticky');
        document.documentElement.className += (window.matchMedia('(-moz-touch-enabled: 1), (hover: none)')).matches ? ' no-supports-hover' : ' supports-hover';
      }());
    </script>

    {% render 'microdata-schema' %}
  </head>

  {%- capture classes -%}features--heading-{{ settings.heading_size }}{%- endcapture -%}

  {%- if settings.uppercase_heading -%}
    {%- assign classes = classes | append: ' features--heading-uppercase' -%}
  {%- endif -%}

  {%- if settings.product_show_price_on_hover -%}
    {%- assign classes = classes | append: ' features--show-price-on-hover' -%}
  {%- endif -%}

  {%- if settings.show_page_transition -%}
    {%- assign classes = classes | append: ' features--show-page-transition' -%}
  {%- endif -%}

  {%- if settings.show_button_transition -%}
    {%- assign classes = classes | append: ' features--show-button-transition' -%}
  {%- endif -%}

  {%- if settings.show_image_zooming -%}
    {%- assign classes = classes | append: ' features--show-image-zooming' -%}
  {%- endif -%}

  {%- if settings.show_element_staggering -%}
    {%- assign classes = classes | append: ' features--show-element-staggering' -%}
  {%- endif -%}

  <body class="prestige--v4 {{ classes }} template-gift-card">
    <div class="PageOverlay"></div>
    <span class="LoadingBar"></span>

    <div class="PageContainer">
      <main id="main" role="main">
        {{ content_for_layout }}
      </main>
    </div>
  </body>
</html>