<!doctype html>

<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-KSW6L9M');</script>
<!-- End Google Tag Manager -->
 
    <meta name="facebook-domain-verification" content="ma70cjuf2osm6lo2zy6ylg17hvzezl" />
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.accent_color }}">

    <title>
      {{ page_title }}{% if current_tags %}{% assign meta_tags = current_tags | join: ', ' %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags }}{% endif %}{% if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{% endif %}{% unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless %}
    </title>

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | img_url: '96x' }}" type="image/png">
    {%- endif -%}

    {% render 'social-meta-tags' %}
    {% render 'css-variables' %}

    {{ content_for_header }}

    <link rel="stylesheet" href="{{ 'theme.css' | asset_url }}">
    <link rel="stylesheet" href="{{ 'custom.css' | asset_url }}">
  
      <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){ dataLayer.push(arguments); }
          // Set default consent to 'denied' as a placeholder
          // Determine actual values based on customer's own requirements
          gtag('consent', 'default', {
          'ad_storage': 'denied',
          'ad_user_data': 'denied',
          'ad_personalization': 'denied',
          'analytics_storage': 'denied',
          'wait_for_update': 500
          });
          gtag('set', 'ads_data_redaction', true);
      </script>  

    <script>
      {%- if request.page_type == 'policy' -%}
        document.addEventListener('DOMContentLoaded', function() {
          document.querySelector('.shopify-policy__title').classList.add('PageHeader', 'Heading', 'u-h1');
          document.querySelector('.shopify-policy__body').classList.add('Rte');
        });
      {%- endif -%}

      // This allows to expose several variables to the global scope, to be used in scripts
      window.theme = {
        pageType: {{ request.page_type | json }},
        moneyFormat: {{ shop.money_format | json }},
        moneyWithCurrencyFormat: {{ shop.money_with_currency_format | json }},
        currencyCodeEnabled: {{ settings.currency_code_enabled | json }},
        productImageSize: {{ settings.product_image_size | json }},
        searchMode: {{ settings.search_mode | json }},
        showPageTransition: {{ settings.show_page_transition | json }},
        showElementStaggering: {{ settings.show_element_staggering | json }},
        showImageZooming: {{ settings.show_image_zooming | json }},
        cartData: {{ cart | json }}
      };

      window.routes = {
        rootUrl: {{ routes.root_url | json }},
        rootUrlWithoutSlash: {% if routes.root_url == '/' %}''{% else %}{{ routes.root_url | json }}{% endif %},
        cartUrl: {{ routes.cart_url | json }},
        cartAddUrl: {{ routes.cart_add_url | json }},
        cartChangeUrl: {{ routes.cart_change_url | json }},
        cartUpdateUrl: {{ routes.cart_update_url | json }},
        searchUrl: {{ routes.search_url | json }},
        productRecommendationsUrl: {{ routes.product_recommendations_url | json }}
      };

      window.languages = {
        cartAddNote: {{ 'cart.general.add_note' | t | json }},
        cartEditNote: {{ 'cart.general.edit_note' | t | json }},
        productImageLoadingError: {{ 'product.slideshow.image_loading_error' | t | json }},
        productFormAddToCart: {% if product.template_suffix == 'pre-order' %}{{ 'product.form.pre_order' | t | json }}{% else %}{{ 'product.form.add_to_cart' | t | json }}{% endif %},
        productFormUnavailable: {{ 'product.form.unavailable' | t | json }},
        productFormSoldOut: {{ 'product.form.sold_out' | t | json }},
        shippingEstimatorOneResult: {{ 'cart.shipping_estimator.one_result_title' | t | json }},
        shippingEstimatorMoreResults: {{ 'cart.shipping_estimator.more_results_title' | t | json }},
        shippingEstimatorNoResults: {{ 'cart.shipping_estimator.no_results_title' | t | json }},
        readmoreBtn: {{ 'seo.btn_more' | t | json }},
        readLessBtn: {{ 'seo.btn_less' | t | json }},
      };

      window.product = {
        variant: {{ product.variants | json }},
        cartItems: {{ cart.items | json }},
      }

      window.lazySizesConfig = {
        loadHidden: false,
        hFac: 0.5,
        expFactor: 2,
        ricTimeout: 150,
        lazyClass: 'Image--lazyLoad',
        loadingClass: 'Image--lazyLoading',
        loadedClass: 'Image--lazyLoaded'
      };

      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
      document.documentElement.style.setProperty('--window-height', window.innerHeight + 'px');

      // We do a quick detection of some features (we could use Modernizr but for so little...)
      (function() {
        document.documentElement.className += ((window.CSS && window.CSS.supports('(position: sticky) or (position: -webkit-sticky)')) ? ' supports-sticky' : ' no-supports-sticky');
        document.documentElement.className += (window.matchMedia('(-moz-touch-enabled: 1), (hover: none)')).matches ? ' no-supports-hover' : ' supports-hover';
      }());

      {% if request.page_type == 'captcha' %}
        setTimeout(function() {
          window.scrollTo(0,0);
        }, 250)
      {% endif %}
    </script>

    {%- render 'cookie-information-consent' -%}
 
    <script src="{{ 'lazysizes.min.js' | asset_url }}" async></script>

    {%- if template == 'customers/addresses' -%}
      <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer></script>
      <script src="{{ 'customer_area.js' | shopify_asset_url }}" defer></script>
    {%- endif -%}

    <script src="{{ 'libs.min.js' | asset_url }}" defer></script>
    <script src="{{ 'theme.js' | asset_url }}" defer></script>
    <script src="{{ 'custom.js' | asset_url }}" defer></script>

    <script>
      (function () {
        window.onpageshow = function() {
          if (window.theme.showPageTransition) {
            var pageTransition = document.querySelector('.PageTransition');

            if (pageTransition) {
              pageTransition.style.visibility = 'visible';
              pageTransition.style.opacity = '0';
            }
          }

          // When the page is loaded from the cache, we have to reload the cart content
          document.documentElement.dispatchEvent(new CustomEvent('cart:refresh', {
            bubbles: true
          }));
        };
      })();
    </script>

    {% render 'microdata-schema' %}
    <!--Flickity CSS -->
    <link href="https://unpkg.com/flickity@2/dist/flickity.min.css" rel="stylesheet">
    <!--Flickity JavaScript -->
    <script src="https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js"></script>
    <link href="https://unpkg.com/swiper@7/swiper-bundle.min.css" rel="stylesheet"/>
  </head>

  {%- capture classes -%}features--heading-{{ settings.heading_size }}{%- endcapture -%}

  {%- if settings.uppercase_heading -%}
    {%- assign classes = classes | append: ' features--heading-uppercase' -%}
  {%- endif -%}

  {%- if settings.product_show_price_on_hover -%}
    {%- assign classes = classes | append: ' features--show-price-on-hover' -%}
  {%- endif -%}

  {%- if settings.show_page_transition -%}
    {%- assign classes = classes | append: ' features--show-page-transition' -%}
  {%- endif -%}

  {%- if settings.show_button_transition -%}
    {%- assign classes = classes | append: ' features--show-button-transition' -%}
  {%- endif -%}

  {%- if settings.show_image_zooming -%}
    {%- assign classes = classes | append: ' features--show-image-zooming' -%}
  {%- endif -%}

  {%- if settings.show_element_staggering -%}
    {%- assign classes = classes | append: ' features--show-element-staggering' -%}
  {%- endif -%}

  <body class="prestige--v4 {{ classes }} {% if template.directory %}template-{{ template.directory | handle }}{% endif %} template-{{ template.name | handle }}">
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KSW6L9M"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
    {%- comment -%}Common SVG definitions that we are re-using in several places{%- endcomment -%}
    <svg class="u-visually-hidden">
      <linearGradient id="rating-star-gradient-half">
        <stop offset="50%" stop-color="var(--product-star-rating)" />
        <stop offset="50%" stop-color="var(--text-color-light)" />
      </linearGradient>
    </svg>

    <a class="PageSkipLink u-visually-hidden" href="#main">{{ 'header.navigation.skip_to_content' | t }}</a>
    <span class="LoadingBar"></span>
    <div class="PageOverlay"></div>

    {%- if settings.show_page_transition -%}
      <div class="PageTransition"></div>
    {%- endif -%}

    {% section 'popup' %}
    {% section 'sidebar-menu' %}
    {%- render 'terms' -%}

    {%- if template != 'cart' -%}
      {% render 'cart-drawer' %}
    {%- endif -%}

    <div class="PageContainer">
      {% section 'announcement' %}
      {% section 'header' %}

      <main id="main" role="main">
        {{ content_for_layout }}
      </main>

      {% section 'footer' %}
    </div>

  {% render 'app_wishlist-king' %}
  {% section 'klaviyo-back-in-stock' %}
          
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js" defer="defer"></script>
  <script type="text/javascript" src="//dl7y9fehbdabg.cloudfront.net/js/shopify.js" defer="defer"></script>
  </body>
          
</html>
