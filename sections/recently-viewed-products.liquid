{%- capture flickity_options -%}
{
  "prevNextButtons": true,
  "pageDots": false,
  "wrapAround": false,
  "contain": true,
  "cellAlign": "center",
  "watchCSS": true,
  "dragThreshold": 8,
  "groupCells": true,
  "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
}
{%- endcapture -%}

{%- capture section_settings -%}
{
  "productId": {% if template.name == 'product' %}{{ product.id }}{% else %}null{% endif %}
}
{%- endcapture -%}

<section class="Section Section--spacingNormal" data-section-id="{{ section.id }}" data-section-type="recently-viewed-products" data-section-settings='{{ section_settings }}'>
  {%- if section.settings.title != blank -%}
    <header class="SectionHeader SectionHeader--center">
      <div class="Container">
        <h3 class="SectionHeader__Heading Heading u-h3">{{ section.settings.title | escape }}</h3>
      </div>
    </header>
  {%- endif -%}

  {%- if template.name == 'search' -%}
    <div class="ProductListWrapper">
      <div class="ProductList ProductList--carousel Carousel" data-flickity-config='{{ flickity_options }}'>
        {%- assign parsed_terms = search.terms | split: ' OR ' -%}

        {%- for parsed_term in parsed_terms -%}
          {%- assign id = parsed_term | split: 'id:' | last | times: 1 -%}

          {%- liquid
            assign search_results = null | sort
            for result in search.results
              unless result.tags contains 'hide_product'
                assign search_result = result | sort
                assign search_results = search_results | concat: search_result
              endunless
            endfor
          -%}


          {%- for product in search_results -%}

            {%- if product.id == id -%}
              <div class="Carousel__Cell">
                {% render 'product-item', product: product, show_product_info: section.settings.show_product_info, show_vendor: section.settings.show_vendor, show_color_swatch: section.settings.show_color_swatch, show_labels: true, collection_handle: collection_handle, show_form: true %}
              </div>
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}
        {%- endfor -%}
      </div>
    </div>
  {%- endif -%}
</section>

{% schema %}
{
  "name": "Recently viewed products",
  "class": "shopify-section--bordered shopify-section--hidden",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Recently viewed"
    },
    {
      "type": "checkbox",
      "id": "show_product_info",
      "label": "Show product info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_color_swatch",
      "label": "Show color swatch",
      "info": "Some colors appear white? [Learn more](http://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch).",
      "default": false
    }
  ],
  "presets": [
    {
      "category": "Product",
      "name": "Recently viewed products"
    }
  ]
}
{% endschema %}