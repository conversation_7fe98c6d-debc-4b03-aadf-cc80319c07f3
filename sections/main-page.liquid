<div class="Container">
  <header class="PageHeader">
    <div class="SectionHeader SectionHeader--center">
      <h1 class="SectionHeader__Heading Heading u-h1">{{ page.title }}</h1>
    </div>
  </header>

  <div class="PageContent {% unless section.settings.show_large %}PageContent--narrow{% endunless %} Rte">
    {{ page.content }}
  </div>
</div>

{% schema %}
{
  "name": "Page",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_large",
      "label": "Large width",
      "default": false
    }
  ]
}
{% endschema %}