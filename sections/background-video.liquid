{%- capture section_settings -%}
{
  "videoType": {{ section.settings.video_url.type | json }},
  "videoId": {{ section.settings.video_url.id | json }},
  "requestHost": {{ request.host | json }}
}
{%- endcapture -%}

<section class="Section" id="section-{{ section.id }}" data-section-id="{{ section.id }}" data-section-type="background-video" data-section-settings='{{ section_settings }}'>
  <div class="ImageHero {% if section.settings.section_size != 'normal' %}ImageHero--{{ section.settings.section_size }}{% endif %}">
    <div class="ImageHero__VideoHolder"></div>
  </div>
</section>

{% schema %}
{
  "name": "Background video",
  "settings": [
    {
      "type": "paragraph",
      "content": "Background videos are automatically muted to allow autoplay. If you require audio, use the \"Featured Video\" section."
    },
    {
      "type": "select",
      "id": "section_size",
      "label": "Section size",
      "options": [
        {
          "value": "preserveRatio",
          "label": "Preserve original ratio"
        },
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "preserveRatio"
    },
    {
      "type": "video_url",
      "accept": ["youtube", "vimeo"],
      "id": "video_url",
      "label": "Video URL",
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
    }
  ],
  "presets": [
    {
      "category": "Video",
      "name": "Background video",
      "settings": {}
    }
  ]
}
{% endschema %}