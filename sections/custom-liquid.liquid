<section class="Section Section--spacingNormal" id="section-{{ section.id }}">
  <div class="Container">
    {%- if section.settings.subheading != blank or section.settings.title != blank -%}
      <header class="SectionHeader SectionHeader--center">
        {%- if section.settings.subheading != blank -%}
          <h3 class="SectionHeader__SubHeading Heading u-h6">{{ section.settings.subheading | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h2 class="SectionHeader__Heading Heading u-h1">{{ section.settings.title | escape }}</h2>
        {%- endif -%}
      </header>
    {%- endif -%}

    <div class="Liquid">
      {{ section.settings.liquid }}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Custom Liquid",
  "class": "shopify-section--bordered",
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Sub-heading",
      "default": "Sub-heading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Your Liquid"
    },
    {
      "type": "liquid",
      "id": "liquid",
      "label": "Liquid",
      "info": "Add app snippets or other Liquid code to create advanced customizations.",
      "default": "<p>Write your own custom Liquid content.</p>"
    }
  ],
  "presets": [
    {
      "category": "Advanced",
      "name": "Custom Liquid",
      "settings": {}
    }
  ]
}
{% endschema %}