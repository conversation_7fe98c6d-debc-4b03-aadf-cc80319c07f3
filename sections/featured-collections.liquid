{%- if section.settings.layout_mode == 'carousel' -%}
  {%- capture flickity_options -%}
  {
    "prevNextButtons": true,
    "pageDots": false,
    "wrapAround": false,
    "contain": true,
    "cellAlign": "center",
    "watchCSS": true,
    "dragThreshold": 8,
    "groupCells": true,
    "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
  }
  {%- endcapture -%}
{%- endif -%}

{%- capture section_settings -%}
{
  "layout": {{ section.settings.layout_mode | json }}
}
{%- endcapture -%}

<section class="Section Section--spacingNormal" data-section-id="{{ section.id }}" data-section-type="featured-collections" data-settings='{{ section_settings }}'>
  <header class="SectionHeader SectionHeader--center">
    <div class="Container">
    {%- if section.settings.title != blank -%}
      <h3 class="SectionHeader__SubHeading Heading u-h6">{{ section.settings.title | escape }}</h3>
    {%- endif -%}

    {%- if section.blocks.size > 1 -%}
      <div class="SectionHeader__TabList TabList" role="tablist">
        {%- for block in section.blocks -%}
          {%- assign collection = collections[block.settings.collection] -%}

          <button class="Heading u-h1 TabList__Item {% if forloop.first %}is-active{% endif %}" data-action="toggle-tab" aria-controls="block-{{ block.id }}" aria-selected="{% if forloop.first %}true{% else %}false{% endif %}" role="tab">
            {%- if collection != empty -%}
              {{- block.settings.title | default: collection.title -}}
            {%- else -%}
              {{- 'home_page.onboarding.collection_title' | t -}}
            {%- endif -%}
          </button>
        {%- endfor -%}
      </div>
    {%- else -%}
      {%- assign collection = collections[section.blocks.first.settings.collection] -%}

      {%- if collection != empty -%}
        <h2 class="SectionHeader__Heading Heading u-h1">{{ section.blocks.first.settings.title | default: collection.title }}</h2>
      {%- else -%}
        <h2 class="SectionHeader__Heading Heading u-h1">{{ 'home_page.onboarding.collection_title' | t }}</h2>
      {%- endif -%}
    {%- endif -%}
    </div>
  </header>

  {%- for block in section.blocks -%}
    {%- assign collection = collections[block.settings.collection] -%}

    <div class="TabPanel" id="block-{{ block.id }}" aria-hidden="{% if forloop.first %}false{% else %}true{% endif %}" role="tabpanel" {{ block.shopify_attributes }}>
      <div class="ProductListWrapper">
        {%- if collection != empty -%}
          {%- assign item_count = block.settings.grid_items_count -%}

          {%- if collection.products_count < block.settings.grid_items_count -%}
            {%- assign item_count = collection.products_count -%}
          {%- endif -%}

          {%- if section.settings.layout_mode == 'grid' -%}
            <div class="ProductList ProductList--grid ProductList--removeMargin Grid" data-mobile-count="{{ section.settings.grid_mobile_items_per_row }}" data-desktop-count="{{ section.settings.grid_desktop_items_per_row }}">
              {%- for product in collection.products limit: block.settings.grid_items_count -%}
                <div class="Grid__Cell 1/{{ section.settings.grid_mobile_items_per_row }}--phone 1/2--tablet 1/{{ section.settings.grid_desktop_items_per_row }}--lap-and-up">
                  {%- render 'product-item', product: product, show_product_info: section.settings.show_product_info, show_vendor: section.settings.show_vendor, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                </div>
              {%- endfor -%}
            </div>
          {%- else -%}
            <div class="ProductList ProductList--carousel Carousel" data-flickity-config='{{ flickity_options }}'>
              {%- for product in collection.products limit: block.settings.grid_items_count -%}
                <div class="Carousel__Cell">
                  {%- render 'product-item', product: product, show_product_info: section.settings.show_product_info, show_vendor: section.settings.show_vendor, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                </div>
              {%- endfor -%}
            </div>
          {%- endif -%}
        {%- else -%}
          {%- comment -%}If collection is not set, we use placeholder content for all three layout mode{%- endcomment -%}

          {%- capture products_placeholder -%}
            {%- for i in (1..block.settings.grid_items_count) -%}
              <div class="{% if section.settings.layout_mode == 'grid' %}Grid__Cell 1/{{ section.settings.grid_mobile_items_per_row }}--phone 1/2--tablet-and-up 1/{{ section.settings.grid_desktop_items_per_row }}--lap-and-up{% else %}Carousel__Cell{% endif %}">
                {% capture placeholder_index %}{% cycle 1, 2, 3, 4, 5, 6 %}{% endcapture %}
                {%- render 'product-item-placeholder', placeholder_index: placeholder_index -%}
              </div>
            {%- endfor -%}
          {%- endcapture -%}

          {%- if section.settings.layout_mode == 'grid' -%}
            <div class="ProductList ProductList--grid ProductList--removeMargin Grid" data-mobile-count="{{ section.settings.grid_mobile_items_per_row }}" data-desktop-count="{{ section.settings.grid_desktop_items_per_row }}">
              {{ products_placeholder }}
            </div>
          {%- else -%}
            <div class="ProductList ProductList--carousel Carousel" data-flickity-config='{{ flickity_options }}'>
              {{ products_placeholder }}
            </div>
          {%- endif -%}
        {%- endif -%}
      </div>

      {%- if block.settings.button_text != blank -%}
        <div class="Container">
          <div class="SectionFooter">
            <a href="{{ collection.url }}" class="Button Button--primary">{{ block.settings.button_text | escape }}</a>
          </div>
        </div>
      {%- endif -%}
    </div>
  {%- endfor -%}
</section>

{%- capture hack -%}
{%- comment -%}This is just an ugly hack to make those variables appear as part of the theme editor in the General Settings{%- endcomment -%}
{% if section.settings.layout_mode == 'grid' %}{{ settings.product_list_horizontal_spacing }},{{ settings.product_list_vertical_spacing }}{% endif %}
{%- endcapture -%}

{% schema %}
{
  "name": "Featured collections",
  "class": "shopify-section--bordered",
  "max_blocks": 2,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Sub-heading",
      "default": "Featured collection"
    },
    {
      "type": "select",
      "id": "layout_mode",
      "label": "Layout",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "carousel",
          "label": "Carousel"
        }
      ],
      "default": "grid"
    },
    {
      "type": "checkbox",
      "id": "show_product_info",
      "label": "Show product info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_color_swatch",
      "label": "Show color swatch",
      "info": "Some colors appear white? [Learn more](http://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch).",
      "default": false
    },
    {
      "type": "header",
      "content": "Grid",
      "info": "Those settings apply only if the layout mode is set to grid."
    },
    {
      "type": "select",
      "id": "grid_mobile_items_per_row",
      "label": "Products per row (mobile)",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "2"
    },
    {
      "type": "range",
      "min": 2,
      "max": 4,
      "id": "grid_desktop_items_per_row",
      "label": "Products per row (desktop)",
      "default": 4
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "info": "If none is set, collection title is used."
        },
        {
          "type": "range",
          "id": "grid_items_count",
          "min": 2,
          "max": 50,
          "step": 1,
          "label": "Products to show",
          "default": 8
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text",
          "default": "View all products"
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Collection",
      "name": "Featured collections",
      "settings": {},
      "blocks": [
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}