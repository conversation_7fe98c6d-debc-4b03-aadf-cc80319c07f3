{%- if section.settings.enable_bar -%}
    {%- unless section.settings.home_page_only and template != 'index' -%}
        <section id="section-{{ section.id }}" data-section-id="{{ section.id }}">
            <div class="AnnouncementBar">
                {% if section.settings.link != blank %}
                    <a href="{{ section.settings.link }}" class="AnnouncementBar__Wrapper">
                    {% else %}
                        <div class="AnnouncementBar__Wrapper">
                        {% endif %}
                        <div class="AnnouncementBar__Content Heading">
                            {% for block in section.blocks %}
                                <div class="AnnouncementBar__item">
                                    {%- if block.settings.link and section.settings.link == blank -%}
                                        <a href="{{ block.settings.link }}">{{ block.settings.content | escape }}</a>
                                    {%- else -%}
                                        <p>{{ block.settings.content | escape }}</p>
                                    {%- endif -%}
                                    {% if block.settings.img != blank %}
                                        <img class="Image--lazyLoad AnnouncementBar__Image" data-src="{{ block.settings.img | img_url: "x20" }}" alt="">
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                        {% if section.settings.link != blank %}
                        </a>
                {% else %}
                    </div>
                {% endif %}
            </div>
        </section>

        <style>
            #section-{{ section.id }} {
                background: {{ section.settings.background }};
                color: {{ section.settings.text_color }};
            }
        </style>

        <script>
            var node = document.getElementById('section-{{ section.id }}')
            const slider = node.querySelector('.AnnouncementBar__Content');

            const flkty = new Flickity(slider, {
                groupCells: 1,
                autoPlay: true,
                watchCSS: true,
                contain: true,
                prevNextButtons: false,
                pageDots: false,
                wrapAround: true
            });

            document.documentElement.style.setProperty('--announcement-bar-height', document.getElementById('shopify-section-announcement').offsetHeight + 'px');

        </script>
    {%- endunless -%}
{%- endif -%}

<style>

</style>

{% schema %}
{
  "name": "Announcement bar",
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_bar",
      "label": "Enable bar",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "home_page_only",
      "label": "Home page only",
      "default": false
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Link"
    }
  ],
  "blocks": [
    {
      "type": "announcement",
      "name": "Announcement",
      "limit":4,
      "settings": [
        {
          "type": "text",
          "id": "content",
          "label": "Text",
          "default": "Announce something here"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Icon"
        }
      ]
    }
  ]
}
{% endschema %}