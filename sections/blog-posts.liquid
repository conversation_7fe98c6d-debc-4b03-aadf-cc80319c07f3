<section class="Section Section--spacingNormal" id="section-{{ section.id }}" data-section-type="article-list" data-section-id="{{ section.id }}">
  <div class="Container">
    {%- if section.settings.subheading != blank or section.settings.title != blank -%}
      <header class="SectionHeader SectionHeader--center">
        {%- if section.settings.subheading != blank -%}
          <h3 class="SectionHeader__SubHeading Heading u-h6">{{ section.settings.subheading | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h2 class="SectionHeader__Heading Heading u-h1">{{ section.settings.title | escape }}</h2>
        {%- endif -%}
      </header>
    {%- endif -%}

    {%- assign show_placeholders = true -%}

    <div class="ArticleListWrapper">
      <div class="ArticleList Grid Grid--m Grid--center">
        {%- assign block = section.blocks.first -%}

        {%- case block.type -%}
          {%- when 'article' -%}
            {%- for i in (1..3) -%}
              {%- assign article_option = 'article_' | append: i -%}
              {%- assign article = articles[block.settings[article_option]] -%}

              {%- unless article == empty -%}
                <div class="Grid__Cell 1/2--tablet 1/3--lap-and-up {% if forloop.index == 3 %}hidden-tablet{% endif %}">
                  {%- render 'article-item', block: block, article: article -%}
                </div>

                {%- assign show_placeholders = false -%}
              {%- endunless -%}
            {%- endfor -%}

          {%- when 'blog' -%}
            {%- assign blog = blogs[block.settings.blog] -%}

            {%- for article in blog.articles limit: 3 -%}
              <div class="Grid__Cell 1/2--tablet 1/3--lap-and-up {% if forloop.index == 3 %}hidden-tablet{% endif %}">
                {%- render 'article-item', block: block, article: article -%}
              </div>

              {%- assign show_placeholders = false -%}
            {%- endfor -%}
        {%- endcase -%}

        {%- if show_placeholders -%}
          {%- for i in (1..3) -%}
            <div class="Grid__Cell 1/2--tablet 1/3--lap-and-up {% if forloop.index == 3 %}hidden-tablet{% endif %}">
              <article class="ArticleItem">
                {%- capture placeholder -%}{% cycle 'lifestyle-1', 'lifestyle-2' %}{%- endcapture -%}

                <div class="ArticleItem__ImageWrapper AspectRatio" style="--aspect-ratio: 1.7">
                  {{ placeholder | placeholder_svg_tag: 'ArticleItem__Image PlaceholderBackground PlaceholderSvg--dark' }}
                </div>

                <div class="ArticleItem__Content">
                  {%- if section.settings.show_category -%}
                    <span class="ArticleItem__Category Heading u-h6 Text--subdued">{{ 'home_page.onboarding.article_category' | t }}</span>
                  {%- endif -%}

                  <h2 class="ArticleItem__Title Heading u-h2">
                    <a href="#">{{ 'home_page.onboarding.article_name' | t }}</a>
                  </h2>

                  <p class="ArticleItem__Excerpt">{{ 'home_page.onboarding.article_excerpt' | t }}</p>

                  <a href="#" class="ArticleItem__Link Link Link--underline">{{ 'blog.article.read_more' | t }}</a>
                </div>
              </article>
            </div>
          {%- endfor -%}
        {%- endif -%}
      </div>
    </div>

    {%- if section.settings.button_text != blank -%}
      <div class="SectionFooter">
        <a href="{{ section.settings.button_link }}" class="Button Button--primary">{{ section.settings.button_text | escape }}</a>
      </div>
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Blog posts",
  "class": "shopify-section--bordered",
  "max_blocks": 1,
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Sub-heading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Blog posts"
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "label": "Show article category",
      "info": "The first article's tag is used as the main category.",
      "default": false
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "View all articles"
    }
  ],
  "blocks": [
    {
      "type": "article",
      "name": "Article",
      "settings": [
        {
          "type": "article",
          "id": "article_1",
          "label": "Article 1"
        },
        {
          "type": "article",
          "id": "article_2",
          "label": "Article 2"
        },
        {
          "type": "article",
          "id": "article_3",
          "label": "Article 3"
        }
      ]
    },
    {
      "type": "blog",
      "name": "Blog",
      "settings": [
        {
          "type": "blog",
          "id": "blog",
          "label": "Blog"
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Blog",
      "name": "Blog posts",
      "settings": {},
      "blocks": [
        {
          "type": "blog",
          "settings": {
            "blog": "news"
          }
        }
      ]
    }
  ]
}
{% endschema %}