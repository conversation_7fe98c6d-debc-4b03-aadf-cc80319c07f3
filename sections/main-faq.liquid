{%- assign has_section_separator = false -%}

{%- for block in section.blocks -%}
  {%- if block.type == 'separator' -%}
    {%- assign has_section_separator = true -%}
    {%- break -%}
  {%- endif -%}
{%- endfor -%}

{%- capture faq_content -%}
  <div class="Faq">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'separator' -%}
          <h1 class="Faq__Section Heading u-h1" {{ block.shopify_attributes }}>
            <span class="Anchor" id="block-{{ block.id }}"></span>
            {{- block.settings.title | escape -}}
          </h1>

        {%- when 'question' -%}
          {%- if block.settings.title != blank and block.settings.answer != blank -%}
            <div class="Faq__Item {% if section.blocks[forloop.index].type == 'separator' or forloop.last %}Faq__Item--lastOfSection{% endif %}" aria-expanded="false" {{ block.shopify_attributes }}>
              <span class="Faq__Icon">{% render 'icon' with 'select-arrow-right' %}</span>

              <div class="Faq__ItemWrapper">
                <button class="Faq__Question">{{ block.settings.title | escape }}</button>

                <div class="Faq__AnswerWrapper" aria-hidden="true">
                  <div class="Faq__Answer Rte">
                    {{ block.settings.answer }}
                  </div>
                </div>
              </div>
            </div>
          {%- endif -%}
      {%- endcase -%}
    {%- endfor -%}
  </div>
{%- endcapture -%}

<section data-section-type="faq" data-section-id="{{ section.id }}">
  <div class="Container">
    <div class="PageContent {% unless has_section_separator %}PageContent--narrow{% endunless %}">
      {%- if has_section_separator -%}
        <div class="PageLayout">
          <div class="PageLayout__Section PageLayout__Section--secondary PageLayout__Section--sticky hidden-phone">
            <ol class="FaqSummary">
              {%- assign faq_separator_count = 0 -%}

              {%- for block in section.blocks -%}
                {%- if block.type == 'separator' -%}
                  <li class="FaqSummary__Item {% if faq_separator_count == 0 %}is-active{% endif %}">
                    <a href="#block-{{ block.id }}" class="FaqSummary__Link" data-offset="80">
                      <span class="FaqSummary__LinkLabel">{{ block.settings.title | escape }}</span>
                    </a>
                  </li>

                  {%- assign faq_separator_count = faq_separator_count | plus: 1 -%}
                {%- endif -%}
              {%- endfor -%}
            </ol>
          </div>

          <div class="PageLayout__Section">
            {{- faq_content -}}
          </div>
        </div>
      {%- else -%}
        {{- faq_content -}}
      {%- endif -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "FAQ",
  "settings": [],
  "blocks": [
    {
      "type": "separator",
      "name": "Section separator",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Default"
        }
      ]
    },
    {
      "type": "question",
      "name": "Question",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Question"
        },
        {
          "type": "richtext",
          "id": "answer",
          "label": "Answer"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "separator",
        "settings": {
          "title": "Shipping"
        }
      },
      {
        "type": "question",
        "settings": {
          "title": "Do you ship overseas?",
          "answer": "<p>Yes, we ship all over the world. Shipping costs will apply, and will be added at checkout. We run discounts and promotions all year, so stay tuned for exclusive deals.</p>"
        }
      },
      {
        "type": "question",
        "settings": {
          "title": "How long will it take to get my order?",
          "answer": "<p>It depends on where you are. Orders processed here will take 5-7 business days to arrive. Overseas deliveries can take anywhere from 7-16 days. Delivery details will be provided in your confirmation email.</p>"
        }
      },
      {
        "type": "question",
        "settings": {
          "title": "What shipping carriers do you use?",
          "answer": "<p>We use all major carriers, and local courier partners. You’ll be asked to select a delivery method during checkout.</p>"
        }
      },
      {
        "type": "separator",
        "settings": {
          "title": "Product"
        }
      },
      {
        "type": "question",
        "settings": {
          "title": "Can I return my product?",
          "answer": "<p>We always aim for make sure our customers love our products, but if you do need to return an order, we’re happy to help. Just email us directly and we’ll take you through the process.</p>"
        }
      },
      {
        "type": "question",
        "settings": {
          "title": "Can I get my product personalized?",
          "answer": "<p>It depends on the creator and the product. All options are outlined on the product page, so look out for customization options there.</p>"
        }
      },
      {
        "type": "separator",
        "settings": {
          "title": "Other"
        }
      },
      {
        "type": "question",
        "settings": {
          "title": "Any question?",
          "answer": "<p>You can contact us through our contact page! We will be happy to assist you.</p>"
        }
      }
    ]
  }
}
{% endschema %}