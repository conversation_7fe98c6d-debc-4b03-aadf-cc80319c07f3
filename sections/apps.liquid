<div class="{% if section.settings.include_margins %}Container{% endif %}">
  {%- for block in section.blocks -%}
    {%- render block -%}
  {%- endfor -%}
</div>

{% schema %}
{
  "name": "Apps",
  "settings": [
    {
      "type": "checkbox",
      "id": "include_margins",
      "label": "Include margins",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "Apps"
    }
  ]
}
{% endschema %}
