{%- capture flickity_options -%}
{
  "prevNextButtons": false,
  "setGallerySize": {% if section.settings.show_fullscreen %}false{% else %}true{% endif %},
  "adaptiveHeight": {% if section.settings.show_fullscreen %}false{% else %}true{% endif %},
  "wrapAround": true,
  "dragThreshold": 15,
  "pauseAutoPlayOnHover": false,
  "autoPlay": {% if section.settings.autoplay %}{{ section.settings.cycle_speed | times: 1000 }}{% else %}false{% endif %},
  "pageDots": {% if section.blocks.size > 1 %}true{% else %}false{% endif %}
}
{%- endcapture -%}

<section id="section-{{ section.id }}" data-section-id="{{ section.id }}" data-section-type="slideshow">
  <div class="Slideshow {% if section.settings.show_fullscreen %}Slideshow--fullscreen{% endif %}">
    <div class="Slideshow__Carousel {% if section.settings.show_arrow %}Slideshow__Carousel--withScrollButton{% endif %} Carousel Carousel--fadeIn {% if section.settings.show_fullscreen %}Carousel--fixed{% endif %} Carousel--insideDots"
         data-flickity-config='{{ flickity_options }}'>
      {%- for block in section.blocks -%}
        {%- capture slide_content -%}
          {%- comment -%}
            Implementation note: this is an art driven image selection so it would have been better to be able to use the "picture" tag instead. However,
            as the mobile and desktop image could have different aspect ratio, the image allocation space would not have work properly without explicitly
            distinguishing the two images, hence the two containers.
          {%- endcomment -%}

          {%- assign mobile_image = block.settings.mobile_image | default: block.settings.image -%}

          {%- if mobile_image -%}
            <div class="Slideshow__ImageContainer {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% unless section.settings.show_fullscreen %}AspectRatio{% endunless %} hidden-tablet-and-up"
                 style="{% unless section.settings.show_fullscreen %}--aspect-ratio: {{ mobile_image.aspect_ratio }};{% endunless %} background-image: url({{ mobile_image | img_url: '1x1', format: 'jpg' }})">
                <img class="Slideshow__Image Image--lazyLoad"
                     src="{{ mobile_image | img_url: '1x1' }}"
                     data-src="{{ mobile_image | img_url: 'x800' }}"
                     alt="{{ mobile_image.alt | escape }}">

                <noscript>
                  <img class="Slideshow__Image" src="{{ mobile_image | img_url: 'x800' }}" alt="{{ mobile_image.alt | escape }}">
                </noscript>
            </div>
          {%- endif -%}

          {%- if block.settings.image -%}
            <div class="Slideshow__ImageContainer {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% unless section.settings.show_fullscreen %}AspectRatio AspectRatio--withFallback{% endunless %} hidden-phone"
                 style="{% unless section.settings.show_fullscreen %}padding-bottom: {{ 100.0 | divided_by: block.settings.image.aspect_ratio }}%; --aspect-ratio: {{ block.settings.image.aspect_ratio }};{% endunless %} background-image: url({{ block.settings.image | img_url: '1x1', format: 'jpg' }})">
              {% assign image_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' %}

              <img class="Slideshow__Image Image--lazyLoad hide-no-js"
                   data-src="{{ image_url }}"
                   data-optimumx="1.2"
                   data-widths="[400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000, 2200]"
                   data-sizes="auto"
                   alt="{{ block.settings.image.alt | escape }}">

              <noscript>
                <img class="Slideshow__Image" src="{{ block.settings.image | img_url: '1000x' }}" alt="{{ block.settings.image.alt | escape }}">
              </noscript>
            </div>
          {%- else -%}
            <div class="Slideshow__ImageContainer {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% if section.settings.show_fullscreen %}PlaceholderBackground{% endif %} PlaceholderSvg--dark">
              {%- capture placeholder -%}{% cycle 'lifestyle-1', 'lifestyle-2' %}{%- endcapture -%}
              {{ placeholder | placeholder_svg_tag: 'Slideshow__Image PlaceholderBackground__Svg' }}
            </div>
          {%- endif -%}

          {%- capture slideshow_content -%}
            {%- if block.settings.subheading != blank -%}
              <h3 class="SectionHeader__SubHeading Heading u-h6">{{ block.settings.subheading | escape }}</h3>
            {%- endif -%}

            {%- if block.settings.title != blank -%}
              <h2 class="SectionHeader__Heading SectionHeader__Heading--emphasize Heading u-h1">{{ block.settings.title | escape }}</h2>
            {%- endif -%}

            {%- if block.settings.button_1_text != blank and block.settings.button_2_text != blank -%}
              {%- assign has_two_buttons = true -%}
            {%- else -%}
              {%- assign has_two_buttons = false -%}
            {%- endif -%}

            {%- if block.settings.button_1_text != blank or block.settings.button_2_text != blank -%}
              <div class="SectionHeader__ButtonWrapper">
                <div class="ButtonGroup ButtonGroup--spacingSmall {% if has_two_buttons %}ButtonGroup--sameSize{% endif %}">
                  {%- if block.settings.button_1_text != blank -%}
                    <a href="{{ block.settings.button_1_link }}" class="ButtonGroup__Item Button">{{ block.settings.button_1_text | escape }}</a>
                  {%- endif -%}

                  {%- if block.settings.button_2_text != blank -%}
                    <a href="{{ block.settings.button_2_link }}" class="ButtonGroup__Item Button">{{ block.settings.button_2_text | escape }}</a>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}
          {%- endcapture -%}

          {%- if slideshow_content != blank -%}
            <div class="Slideshow__Content Slideshow__Content--{{ block.settings.content_position }}">
              <header class="SectionHeader">
                {{ slideshow_content }}
              </header>
            </div>
          {%- endif -%}
        {%- endcapture -%}

        {%- comment -%}This code allows to cover a common use case where merchant would like to have a clickable image{%- endcomment -%}

        {%- if block.settings.button_1_text == blank and block.settings.button_1_link != blank -%}
          <a href="{{ block.settings.button_1_link }}" id="Slide{{ block.id }}" class="Slideshow__Slide Carousel__Cell {% if forloop.first %}is-selected{% endif %}" {% if forloop.first %}style="visibility: visible"{% endif %} data-slide-index="{{ forloop.index0 }}" {{ block.shopify_attributes }}>
            {{- slide_content -}}
          </a>
        {%- else -%}
          <div id="Slide{{ block.id }}" class="Slideshow__Slide Carousel__Cell {% if forloop.first %}is-selected{% endif %}" {% if forloop.first %}style="visibility: visible"{% endif %} data-slide-index="{{ forloop.index0 }}" {{ block.shopify_attributes }}>
            {{- slide_content -}}
          </div>
        {%- endif -%}
      {%- endfor -%}
    </div>

    {%- if section.settings.show_arrow -%}
      <button data-href="#section-{{ section.id }}-end" class="Slideshow__ScrollButton RoundButton RoundButton--medium" aria-label="{{ 'general.accessibility.scroll_to_content' | t }}" data-animate-bottom>
        {%- render 'icon' with 'arrow-bottom' -%}
      </button>
    {%- endif -%}
  </div>

  <span id="section-{{ section.id }}-end" class="Anchor"></span>
</section>

<style>
  #section-{{ section.id }} .Heading,
   #section-{{ section.id }} .flickity-page-dots {
    color: {{ section.settings.text_color }};
  }

  #section-{{ section.id }} .Button {
    color: {{ section.settings.button_color }};
    border-color: {{ section.settings.button_background }};
  }

  #section-{{ section.id }} .Button::before {
    background-color: {{ section.settings.button_background }};
  }

  {%- if settings.show_button_transition -%}
    @media (-moz-touch-enabled: 0), (hover: hover) {
      #section-{{ section.id }} .Button:hover {
        color: {{ section.settings.button_background }};
      }
    }
  {%- endif -%}
</style>

{% schema %}
{
  "name": "Slideshow",
  "class": "shopify-section--slideshow",
  "max_blocks": 5,
  "settings": [
    {
      "type": "checkbox",
      "id": "show_fullscreen",
      "label": "Show full-screen images",
      "info": "If enabled, images will be resized to fit the entire screen.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "Show bottom arrow",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Auto rotate between slides",
      "default": true
    },
    {
      "type": "range",
      "id": "cycle_speed",
      "min": 3,
      "max": 8,
      "step": 1,
      "unit": "sec",
      "label": "Change slides every",
      "default": 5
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button text",
      "default": "#363636"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#ffffff"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Slide",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1800 x 800px .jpg recommended"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "750 x 1100px .jpg recommended. If none is set, desktop image will be cropped."
        },
        {
          "type": "checkbox",
          "id": "apply_overlay",
          "label": "Apply overlay on image",
          "info": "This can improve text visibility.",
          "default": true
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content position",
          "options": [
            {
              "value": "middleLeft",
              "label": "Middle left"
            },
            {
              "value": "middleCenter",
              "label": "Middle center"
            },
            {
              "value": "middleRight",
              "label": "Middle right"
            },
            {
              "value": "bottomLeft",
              "label": "Bottom left"
            },
            {
              "value": "bottomCenter",
              "label": "Bottom center"
            },
            {
              "value": "bottomRight",
              "label": "Bottom right"
            }
          ],
          "default": "bottomLeft"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Sub-heading",
          "default": "Slide title"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Tell your story"
        },
        {
          "type": "header",
          "content": "Button 1"
        },
        {
          "type": "text",
          "id": "button_1_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_1_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Button 2"
        },
        {
          "type": "text",
          "id": "button_2_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_2_link",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Image",
      "name": "Slideshow",
      "settings": {},
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}