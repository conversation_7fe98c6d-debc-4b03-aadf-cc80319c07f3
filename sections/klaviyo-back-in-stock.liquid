<script src="https://a.klaviyo.com/media/js/onsite/onsite.js"></script>
<script>
  var klaviyo = klaviyo || [];
  klaviyo.init({
    account: "{{ section.settings.klaviyo_id }}",
    list: "TvcR2d",
    platform: "shopify",
    exclude_on_tags: '{{ section.settings.includeTags }}'
  });
  klaviyo.enable("backinstock", {
    trigger: {
      product_page_text: "{{ 'klaviyo.backInStock.trigger.text' | t }}",
      product_page_class: "Button Button--primary Button--full Btn__Klaviyo",
      product_page_text_align: "center",
      product_page_margin: "24px 0 0 0",
      alternate_anchor: "AddToCart",
      replace_anchor: false
    },
    modal: {
      headline: "{product_name}",
      body_content: "{{ 'klaviyo.backInStock.modal.body_content' | t }}",
      email_field_label: "{{ 'klaviyo.backInStock.modal.email_field_label' | t }}",
      newsletter_subscribe_label: "{{ 'klaviyo.backInStock.modal.newsletter_subscribe_label' | t }}",
      subscribe_checked: false,
      button_label: "{{ 'klaviyo.backInStock.modal.button_label' | t }}",
      subscription_success_label: "{{ 'klaviyo.backInStock.modal.subscription_success_label' | t }}",
      footer_content: "{{ 'klaviyo.backInStock.modal.footer_content' | t }}",
      additional_styles: "@import url('https://fonts.googleapis.com/css?family=Helvetica+Neue');",
      drop_background_color: "#000",
      background_color: "#fff",
      text_color: "#222",
      button_text_color: "#fff",
      button_background_color: "#ac936d",
      close_label: "Luk",
      close_button_color: "#ccc",
      error_background_color: "#fcd6d7",
      error_text_color: "#C72E2F",
      success_background_color: "#d3efcd",
      success_text_color: "#1B9500"
    }
  });
</script>

{% schema %}
{
  "name": "Klaviyo - Back in stock",
  "settings": [
    {
      "type": "text",
      "id": "klaviyo_id",
      "label": "Klaviyo Public API key / Site ID"
    },
    {
      "type": "text",
      "id": "includeTags",
      "label": "Exclude tags",
      "default": "_back-in-stock",
      "info": "Add tags, that will allow back in stock for products. The tags should be separated by commas"
    }
  ],
  "presets": [
    {
      "category": "Klaviyo",
      "name": "Klaviyo - Back in stock"
    }
  ]
}
{% endschema %}
