{%- capture background_image_url -%}
  {%- if section.settings.background_image -%}
    {{- section.settings.background_image | img_url: '1500x', format: 'pjpg' -}}
  {%- else -%}
    {{- 'password.jpg' | asset_img_url: '1500x', format: 'pjpg' -}}
  {%- endif -%}
{%- endcapture -%}

<div class="FlexboxIeFix">
<div class="Password" style="background-image: url({{ background_image_url }})">
  <header class="Password__Header">
    <a href="{{ routes.root_url }}" class="Password__Logo">
      {%- if section.settings.logo != blank -%}
        {%- capture image_size -%}{{ section.settings.logo_max_width }}x{%- endcapture -%}

        <img class="Password__LogoImage"
             src="{{ section.settings.logo | img_url: image_size }}"
             srcset="{{ section.settings.logo | img_url: image_size }} 1x, {{ section.settings.logo | img_url: image_size, scale: 2 }} 2x"
             width="{{ section.settings.logo_max_width }}"
             alt="{{ section.settings.logo.alt | default: shop.name | escape }}">
      {%- else -%}
        <span class="Heading u-h4">{{- shop.name -}}</span>
      {%- endif -%}
    </a>

    <button class="Password__LockAction Heading u-h8" data-action="open-modal" aria-controls="modal-password">
      <span class="hidden-phone">{{ 'general.password.enter_password' | t }}</span> {% render 'icon' with 'lock' %}
    </button>
  </header>

  <div class="Password__Content">
    <div class="Password__Newsletter Password__Card">
      {%- if section.settings.heading != blank -%}
        <h1 class="Password__Heading Heading u-h2">{{ section.settings.heading | escape }}</h1>
      {%- endif -%}

      {%- if shop.password_message != blank -%}
        <div class="Password__Message Rte">{{ shop.password_message }}</div>
      {%- endif -%}

      {%- if section.settings.show_newsletter_signup -%}
        {%- form 'customer', class: 'Password__Form Form Form--spacingTight Form--small' -%}
          {%- if form.posted_successfully? -%}
            <p class="Alert Alert--success">{{ 'general.password.newsletter_success' | t }}</p>
          {%- else -%}
            <input type="hidden" name="contact[tags]" value="prospect, password page">

            <div class="Form__Group">
              <input type="email" class="Form__Input" name="contact[email]" placeholder="{{ 'general.password.newsletter_input' | t }}" aria-label="{{ 'general.password.newsletter_input' | t }}">
              <button type="submit" class="Button Button--primary Button--small">{{ 'general.password.newsletter_submit' | t }}</button>
            </div>
          {%- endif -%}
        {%- endform -%}
      {%- endif -%}
    </div>

    {%- if section.settings.show_social_sharing -%}
      <div class="Password__Social">
        <span class="Heading u-h5">{{ section.settings.social_message | escape }}</span>

        <div class="Password__ShareButtons ShareButtons">
          <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ shop.url }}" target="_blank" rel="noopener">{%- render 'icon' with 'facebook' -%}</a>
          <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?url={{ shop.url }}" target="_blank" rel="noopener">{%- render 'icon' with 'twitter' -%}</a>
          <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ shop.url }}" target="_blank" rel="noopener">{%- render 'icon' with 'pinterest' -%}</a>
        </div>
      </div>
    {%- endif -%}
  </div>

  <footer class="Password__Footer">
    {%- capture shopify_link -%}
      <a href="//www.shopify.com" target="_blank" title="Create your own online store with Shopify" rel="nofollow">
        {%- render 'icon' with 'shopify-logo' -%}
      </a>
    {%- endcapture -%}

    <span class="Password__PoweredBy Heading u-h8">{{ 'general.password.powered_by_html' | t: shopify_link: shopify_link }}</span>
    <span class="Password__AdminLink Heading u-h8">{{ 'general.password.login_title' | t }} <a href="/admin" class="Link Link--underline">{{ 'general.password.login_link' | t }}</a></span>
  </footer>
  </div>
</div>

<div id="modal-password" class="Password__Modal Modal Modal--fullScreen" aria-hidden="true" role="dialog" data-scrollable>
  <button class="Modal__Close Modal__Close--outside" data-action="close-modal">{%- render 'icon' with 'close' -%}</button>

  <div class="Modal__Content">
    <div class="Password__Content">
      <div class="Password__Card">
        <h1 class="Password__Heading Heading u-h2">{{ 'general.password.enter_password' | t }}</h1>

        {%- form 'storefront_password', class: 'Password__Form Form Form--spacingTight Form--small', id: 'modal-password' -%}
          {%- if form.errors -%}
            <p class="Alert Alert--error">{{ form.errors.messages['form'] }}</p>
          {%- endif -%}

          <div class="Form__Group">
            <input type="password" class="Form__Input" name="password" placeholder="{{ 'general.password.password_input' | t }}" aria-label="{{ 'general.password.password_input' | t }}" autofocus>
            <button type="submit" name="commit" class="Button Button--primary Button--small">{{ 'general.password.password_submit' | t }}</button>
          </div>
        {%- endform -%}
      </div>
    </div>
  </div>
</div>

<style>
  .Password__Header .Heading,
  .Password__Footer .Heading {
    color: {{ section.settings.text_color }};
  }

  {%- if section.settings.logo -%}
    @media screen and (max-width: 640px) {
      .Password__LogoImage {
        max-width: {{ section.settings.mobile_logo_max_width }}px;
      }
    }
  {%- endif -%}
</style>

{% schema %}
{
  "name": "Password page",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "Logo image",
      "info": "200 x 60px .png recommended"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 50,
      "max": 350,
      "step": 5,
      "unit": "px",
      "label": "Logo image width",
      "default": 140
    },
    {
      "type": "range",
      "id": "mobile_logo_max_width",
      "min": 50,
      "max": 200,
      "step": 5,
      "unit": "px",
      "label": "Mobile logo image width",
      "default": 90
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "Custom background image",
      "info": "1500 x 1500px .jpg recommended"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Opening Soon"
    },
    {
      "type": "header",
      "content": "Newsletter signup"
    },
    {
      "type": "checkbox",
      "id": "show_newsletter_signup",
      "label": "Show newsletter signup",
      "default": true
    },
    {
      "type": "header",
      "content": "Social sharing"
    },
    {
      "type": "checkbox",
      "id": "show_social_sharing",
      "label": "Show social sharing",
      "default": true
    },
    {
      "type": "text",
      "id": "social_message",
      "label": "Social message",
      "default": "Spread the word"
    }
  ]
}
{% endschema %}