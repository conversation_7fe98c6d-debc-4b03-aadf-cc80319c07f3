<div class="brands">

  <ul class="brandsList">
    {%- assign brandListItems = section.blocks | where: "type", "brand_list_item" -%}
    {%- for block in brandListItems -%}
        <a class="brandsListItem" href="{{ block.settings.brand_List_link_item }}">{{ block.settings.brand_link_name }}</a>
    {%- endfor -%}
  </ul>

  <div class="brandsBlocks Container Container--narrow">
    {%- assign brands = section.blocks | where: "type", "brand" -%}
    {%- for block in brands -%}
        <a class="brandBlocks__item" href="{{ block.settings.brand_link }}">
          {%- assign image_url = block.settings.image | img_url: '1x1', format: 'pjpg' | replace: '_1x1.', '_{width}x.' -%}
          {%- capture supported_sizes -%}{%- render 'image-size', sizes: '200,400,600,700,800,1000,1200', image: block.settings.image -%}{%- endcapture -%}
          <div class="brandBlocks__item__image-wrapper">
            <img class="brandBlocks__item__image Image--lazyLoad"
                 src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                 data-src="{{ image_url }}"
                 data-widths="[{{ supported_sizes }}]"
                 data-sizes="auto"
                 height="150"
                 width="100%"
                 alt="{{ block.settings.image.alt | escape }}">
          </div>
        </a>
    {%- endfor -%}
  </div>

</div>


{% schema %}
{
  "name": "Brands",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Brands title"
    }
  ],
  "blocks": [
    {
      "name": "Brand image block",
      "type": "brand",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "url",
          "id": "brand_link",
          "label": "Link to the brand"
        },
        {
          "type": "text",
          "id": "brand_name",
          "label": "Brand name"
        }
      ]
    },
    {
      "name": "Brand Link list item",
      "type": "brand_list_item",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Image"
        },
        {
          "type": "url",
          "id": "brand_List_link_item",
          "label": "Link to the brand item"
        },
        {
          "type": "text",
          "id": "brand_link_name",
          "label": "Brand name in link list"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Brands",
      "settings": {
        "title": "Brands"
      },
      "blocks": [
        {
          "type": "brand"
        },
        {
          "type": "brand_list_item"
        }
      ]
    }
  ]
}
{% endschema %}
