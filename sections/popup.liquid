{%- if section.settings.show_popup -%}
  {%- unless section.settings.show_only_on_index and template != 'index' -%}
    {%- unless section.settings.show_only_for_visitors and customer -%}
      {% capture section_settings %}
        {
          "apparitionDelay": {{ section.settings.apparition_delay | json }},
          "showOnlyOnce": {{ section.settings.show_only_once | json }}
        }
      {% endcapture %}

      <aside class="NewsletterPopup" data-section-id="{{ section.id }}" data-section-type="newsletter-popup" data-section-settings='{{ section_settings }}' aria-hidden="true">
        <button class="NewsletterPopup__Close" data-action="close-popup" aria-label="{{ 'general.popup.close' | t }}">{% render 'icon' with 'close' %}</button>

        {%- if section.settings.title != blank -%}
          <h2 class="NewsletterPopup__Heading Heading u-h2">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.content != blank -%}
          <div class="NewsletterPopup__Content">
            {{ section.settings.content }}
          </div>
        {%- endif -%}

        {%- if section.settings.show_newsletter -%}
          {%- form 'customer', id: 'newsletter-popup', class: 'NewsletterPopup__Form' -%}
            {%- if form.posted_successfully? -%}
              <p class="Form__Alert Alert Alert--success">{{ 'general.popup.success' | t }}</p>
            {%- else -%}
              <input type="hidden" name="contact[tags]" value="newsletter">

              <input type="email" name="contact[email]" class="Form__Input" required="required" aria-label="{{ 'general.popup.email_placeholder' | t }}" placeholder="{{ 'general.popup.email_placeholder' | t }}">
              <button class="Form__Submit Button Button--primary Button--full" type="submit">{{ 'general.popup.submit' | t }}</button>
            {%- endif -%}
          {%- endform -%}
        {%- endif -%}
      </aside>
    {%- endunless -%}
  {%- endunless -%}
{%- endif -%}

{% schema %}
{
  "name": "Popup",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_popup",
      "label": "Enable",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_only_on_index",
      "label": "Show only on home page",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_only_for_visitors",
      "label": "Show only for visitors",
      "info": "Customers who have created an account on your shop won't see it.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_only_once",
      "label": "Show only once per customer",
      "default": true
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Popup"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>You can use this popup to display some useful information to your customers.</p>"
    },
    {
      "type": "checkbox",
      "id": "show_newsletter",
      "label": "Show newsletter form",
      "default": true
    },
    {
      "type": "range",
      "id": "apparition_delay",
      "min": 0,
      "max": 15,
      "step": 1,
      "unit": "sec",
      "label": "Delay until the popup appears",
      "default": 5
    }
  ]
}
{% endschema %}