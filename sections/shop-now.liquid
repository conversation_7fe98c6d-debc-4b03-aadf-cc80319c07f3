{%- capture flickity_config -%}
{
  "prevNextButtons": true,
  "pageDots": false,
  "wrapAround": true,
  "cellAlign": "left",
  "groupCells": true,
  "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
}
{%- endcapture -%}

<section class="Section Section--spacingLarge" data-section-id="{{ section.id }}" data-section-type="shop-now">
  <div class="Container Container--narrow">
    {%- capture product_panel -%}
      <div class="Panel Panel--flush Panel--withArrows">
        {%- if section.settings.title != blank -%}
          <h2 class="Panel__Title Heading u-h2">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        <div class="Panel__Content">
          {%- assign collection = section.settings.collection -%}

          <div class="ProductList ProductList--shopNow" data-desktop-count="{% if section.blocks.size > 0 %}2{% else %}3{% endif %}" data-flickity-config='{{ flickity_config }}'>
            {%- if collection != blank -%}
              {%- for product in collection.products limit: section.settings.grid_items_count -%}
                <div class="Carousel__Cell">
                  {%- render 'product-item', product: product, show_product_info: section.settings.show_product_info, show_vendor: section.settings.show_vendor, show_color_swatch: section.settings.show_color_swatch, show_labels: false -%}
                </div>
              {%- endfor -%}
            {%- else -%}
              {%- for i in (1..4) -%}
                <div class="Carousel__Cell">
                  {%- render 'product-item-placeholder', placeholder_index: i -%}
                </div>
              {%- endfor -%}
            {%- endif -%}
          </div>
        </div>
      </div>
    {%- endcapture -%}

    {%- if section.blocks.size > 0 -%}
      <div class="ShopNowGrid Grid Grid--m">
        <div class="Grid__Cell 2/3--lap-and-up">
          {{ product_panel }}
        </div>

        <div class="Grid__Cell 1/3--lap-and-up">
          <div class="FeaturedQuote">
            {%- if section.blocks.first.settings.content != blank -%}
              <div class="FeaturedQuote__Content">
                {{ section.blocks.first.settings.content }}
              </div>
            {%- endif -%}

            {%- if section.blocks.first.settings.author != blank -%}
              <p class="FeaturedQuote__Author">{{ section.blocks.first.settings.author }}</p>
            {%- endif -%}
          </div>
        </div>
      </div>
    {%- else -%}
      {{ product_panel }}
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Shop now",
  "max_blocks": 1,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Shop now"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "range",
      "id": "grid_items_count",
      "min": 3,
      "max": 30,
      "step": 3,
      "label": "Products to show",
      "default": 6
    },
    {
      "type": "checkbox",
      "id": "show_product_info",
      "label": "Show product info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_color_swatch",
      "label": "Show color swatch",
      "info": "Some colors appear white? [Learn more](http://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch).",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "quote",
      "name": "Quote",
      "settings": [
        {
          "type": "richtext",
          "id": "content",
          "label": "Text",
          "default": "<p>You can use this element to add a quote, content...</p>"
        },
        {
          "type": "text",
          "id": "author",
          "label": "Author"
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Product",
      "name": "Shop now",
      "blocks": [
        {
          "type": "quote"
        }
      ]
    }
  ]
}
{% endschema %}