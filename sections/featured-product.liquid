{%- assign product = all_products[section.settings.product] -%}

{%- assign meta_block = section.blocks | where: 'type', 'product_meta' | first -%}
{%- assign inventory_block = section.blocks | where: 'type', 'inventory' | first -%}
{%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

{%- capture section_settings -%}
{
  "enableHistoryState": false,
  "usePlaceholder": {% if product == empty %}true{% else %}false{% endif %},
  "templateSuffix": {{ product.template_suffix | json }},
  "showInventoryQuantity": {% if inventory_block != blank %}true{% else %}false{% endif %},
  "showSku": {{ meta_block.settings.show_sku | json }},
  "inventoryQuantityThreshold": {{ inventory_block.settings.inventory_quantity_threshold | default: 0 }},
  "showPriceInButton": {{ buy_buttons_block.settings.show_price_in_button | json }},
  "showPaymentButton": {{ buy_buttons_block.settings.show_payment_button | json }},
  "useAjaxCart": {% if settings.cart_type == 'drawer' %}true{% else %}false{% endif %}
}
{%- endcapture -%}

<section class="Section Section--spacingNormal" data-section-id="{{ section.id }}" data-section-type="featured-product" data-section-settings='{{ section_settings }}'>
  <div class="Container">
    {%- if section.settings.subheading != blank or section.settings.title != blank -%}
      <header class="SectionHeader SectionHeader--center {% unless section.settings.show_headings_on_mobile %}hidden-phone{% endunless %}">
        {%- if section.settings.subheading != blank -%}
          <h3 class="SectionHeader__SubHeading Heading u-h6">{{ section.settings.subheading | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h2 class="SectionHeader__Heading Heading u-h1">{{ section.settings.title | escape }}</h2>
        {%- endif -%}
      </header>
    {%- endif -%}

    <div class="FeaturedProduct {% if section.settings.show_description == false or product.description == blank %}FeaturedProduct--center{% endif %}">
      {%- if product != empty -%}
        {%- comment -%}
        --------------------------------------------------------------------------------------------------------------------
        PRODUCT GALLERY
        --------------------------------------------------------------------------------------------------------------------
        {%- endcomment -%}

        {%- if product.media.size > 0 -%}
          <a href="{{ product.url }}" class="FeaturedProduct__Gallery">
            {%- capture supported_sizes -%}{%- render 'image-size', sizes: '200,300,400,600,700,800,900,1000', image: product.featured_media -%}{%- endcapture -%}

            {%- assign media_aspect_ratio = product.featured_media.aspect_ratio | default: product.featured_media.preview_image.aspect_ratio -%}
            <div class="AspectRatio" style="max-width: {{ product.featured_media.preview_image.width }}px; --aspect-ratio: {{ media_aspect_ratio }}">
              {% assign image_url = product.featured_media | img_url: '1x1' | replace: '_1x1.', '_{width}x.' %}

              <img class="Image--lazyLoad Image--fadeIn" data-src="{{ image_url }}" data-widths="[{{ supported_sizes }}]" data-sizes="auto" alt="{{ product.featured_media.alt | escape }}">
              <span class="Image__Loader"></span>

              <noscript>
                <img src="{{ product.featured_media | img_url: '600x' }}" alt="{{ product.featured_media.alt | escape }}">
              </noscript>
            </div>
          </a>
        {%- endif -%}

        <div class="FeaturedProduct__Info">
          {%- assign has_ouputted_variant_selector = false -%}

          {%- form 'product', product, class: 'ProductForm' -%}
            {%- render 'product-data', product: product -%}

            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when '@app' -%}
                  {%- render block -%}

                {%- when 'product_meta' -%}
                  {%- render 'product-meta', form: form, block: block, product: product -%}

                {%- when 'description' -%}
                  {%- if product.description != blank -%}
                    <div class="ProductMeta__Description" {{ block.shopify_attributes }}>
                      <div class="Rte">
                        {{- product.description | replace: 'data-section-type', 'data-section-type-placeholder' -}}
                      </div>
                    </div>
                  {%- endif -%}

                {%- when 'share_buttons' -%}
                  <div class="ProductMeta__ShareButtons hidden-pocket" {{ block.shopify_attributes }}>
                    <span class="ProductMeta__ShareTitle Heading Text--subdued u-h7">{{ 'product.form.share' | t }}</span>

                    {%- assign share_url = shop.url | append: product.url -%}
                    {%- assign twitter_text = product.title -%}
                    {%- assign pinterest_description = product.description | strip_html | truncatewords: 15 | url_param_escape -%}
                    {%- assign pinterest_image = product.featured_image | img_url: 'large' | prepend: 'https:' -%}

                    <div class="ProductMeta__ShareList Text--subdued">
                      <a class="ProductMeta__ShareItem" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener" aria-label="Facebook">{%- render 'icon' with 'facebook' -%}</a>
                      <a class="ProductMeta__ShareItem" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener" aria-label="Twitter">{%- render 'icon' with 'twitter' -%}</a>
                      <a class="ProductMeta__ShareItem" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener" aria-label="Pinterest">{%- render 'icon' with 'pinterest' -%}</a>
                    </div>
                  </div>

                {%- when 'variant_selector' -%}
                  {%- assign has_ouputted_variant_selector = true -%}
                  {%- render 'product-form', block: block, product: product -%}

                {%- when 'quantity_selector' -%}
                  <div class="ProductForm__QuantitySelector" {{ block.shopify_attributes }}>
                    {%- if block.settings.show_label -%}
                      <span class="ProductForm__Label">{{ 'product.form.quantity' | t }}:</span>
                    {%- endif -%}

                    <div class="QuantitySelector QuantitySelector--large">
                      {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

                      <button type="button" class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% render 'icon' with 'minus' %}</button>
                      <input type="text" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" name="quantity" value="1" aria-label="{{ 'product.form.quantity' | t }}">
                      <button type="button" class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% render 'icon' with 'plus' %}</button>
                    </div>
                  </div>

                {%- when 'inventory' -%}
                  {%- assign hide_inventory_quantity_by_default = false -%}

                  {%- if product.selected_or_first_available_variant.inventory_management == blank or product.selected_or_first_available_variant.inventory_quantity <= 0 -%}
                    {%- assign hide_inventory_quantity_by_default = true -%}
                  {%- endif -%}

                  {%- if block.settings.inventory_quantity_threshold != 0 and product.selected_or_first_available_variant.inventory_quantity > block.settings.inventory_quantity_threshold -%}
                    {%- assign hide_inventory_quantity_by_default = true -%}
                  {%- endif -%}

                  <p class="ProductForm__Inventory Text--subdued" {% if hide_inventory_quantity_by_default %}style="display: none"{% endif %} {{ block.shopify_attributes }}>
                    {%- if block.settings.inventory_quantity_threshold == 0 -%}
                      {{- 'product.form.inventory_quantity_count' | t: count: product.selected_or_first_available_variant.inventory_quantity -}}
                    {%- else -%}
                      {{- 'product.form.low_inventory_quantity_count' | t: count: product.selected_or_first_available_variant.inventory_quantity -}}
                    {%- endif -%}
                  </p>

                {%- when 'buy_buttons' -%}
                  {%- if block.settings.show_payment_button == false or product.selling_plan_groups.size > 0 -%}
                    {%- assign use_primary_button = true -%}
                  {%- else -%}
                    {%- assign use_primary_button = false -%}
                  {%- endif -%}

                  <div class="ProductForm__BuyButtons" {{ block.shopify_attributes }}>
                    {%- if block.settings.show_payment_button and product.selected_or_first_available_variant.available == false -%}
                      <style>
                        #shopify-section-{{ section.id }} .shopify-payment-button {
                          display: none;
                        }
                      </style>
                    {%- endif -%}

                    <button type="submit" data-use-primary-button="{% if use_primary_button %}true{% else %}false{% endif %}" class="ProductForm__AddToCart Button {% if product.selected_or_first_available_variant.available and use_primary_button %}Button--primary{% else %}Button--secondary{% endif %} Button--full" {% if product.selected_or_first_available_variant.available %}data-action="add-to-cart"{% else %}disabled="disabled"{% endif %}>
                      {%- if product.selected_or_first_available_variant.available -%}
                        <span>{% if product.template_suffix == 'pre-order' %}{{ 'product.form.pre_order' | t }}{% else %}{{ 'product.form.add_to_cart' | t }}{% endif %}</span>

                        {%- if block.settings.show_price_in_button -%}
                          <span class="Button__SeparatorDot"></span>
                          <span>{{ product.selected_or_first_available_variant.price | money }}</span>
                        {%- endif -%}
                      {%- else -%}
                        {{- 'product.form.sold_out' | t -}}
                      {%- endif -%}
                    </button>

                    {%- if block.settings.show_payment_button and product.template_suffix != 'pre-order' -%}
                      {{ form | payment_button }}
                    {%- endif -%}
                  </div>

                {%- when 'store_pickup' -%}
                  {%- comment -%}The availability container will be added automatically if there is store pickup available{%- endcomment -%}
                  <div class="ProductMeta__StoreAvailabilityContainer" {{ block.shopify_attributes }}></div>

              {%- endcase -%}
            {%- endfor -%}

            {%- unless has_ouputted_variant_selector -%}
              {%- comment -%}If for any reason the merchant has removed the variant selector block, we still output the ID here{%- endcomment -%}
              <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
            {%- endunless -%}
          {%- endform -%}

          <div class="FeaturedProduct__ViewWrapper">
            <a href="{{ product.url }}" class="Link Link--underline">{{ 'home_page.featured_product.view_product' | t }}</a>
          </div>
        </div>
      {%- else -%}
        {%- comment -%}
        --------------------------------------------------------------------------------------------------------------------
        PLACEHOLDER WHEN NO PRODUCT IS SELECTED
        --------------------------------------------------------------------------------------------------------------------
        {%- endcomment -%}

        <div class="FeaturedProduct__Gallery">
          {{- 'product-1' | placeholder_svg_tag -}}
        </div>

        <div class="FeaturedProduct__Info">
          <div class="ProductForm">
            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'product_meta' -%}
                  <div class="ProductMeta">
                    {%- if block.settings.show_vendor -%}
                      <h2 class="ProductMeta__Vendor Heading u-h6">{{ 'home_page.onboarding.vendor_title' | t }}</h2>
                    {%- endif -%}

                    <h1 class="ProductMeta__Title Heading u-h2">{{ 'home_page.onboarding.product_title' | t }}</h1>

                    <div class="ProductMeta__PriceList Heading">
                      <span class="ProductMeta__Price Price Text--subdued u-h4">{{ 4500 | money }}</span>
                    </div>
                  </div>

                {%- when 'description' -%}
                  <div class="ProductMeta__Description" {{ block.shopify_attributes }}>
                    <div class="Rte">
                      {{ 'home_page.onboarding.product_description' | t }}
                    </div>
                  </div>

                {%- when 'share_buttons' -%}
                  <div class="ProductMeta__ShareButtons hidden-pocket" {{ block.shopify_attributes }}>
                    <span class="ProductMeta__ShareTitle Heading Text--subdued u-h7">{{ 'product.form.share' | t }}</span>

                    <div class="ProductMeta__ShareList Text--subdued">
                      <a class="ProductMeta__ShareItem" href="#" target="_blank" rel="noopener" aria-label="Facebook">{%- render 'icon' with 'facebook' -%}</a>
                      <a class="ProductMeta__ShareItem" href="#" target="_blank" rel="noopener" aria-label="Twitter">{%- render 'icon' with 'twitter' -%}</a>
                      <a class="ProductMeta__ShareItem" href="#" target="_blank" rel="noopener" aria-label="Pinterest">{%- render 'icon' with 'pinterest' -%}</a>
                    </div>
                  </div>

                {%- when 'quantity_selector' -%}
                  <div class="ProductForm__QuantitySelector" {{ block.shopify_attributes }}>
                    {%- if block.settings.show_label -%}
                      <span class="ProductForm__Label">{{ 'product.form.quantity' | t }}:</span>
                    {%- endif -%}

                    <div class="QuantitySelector QuantitySelector--large">
                      {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

                      <button type="button" class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% render 'icon' with 'minus' %}</button>
                      <input type="text" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" name="quantity" value="1" aria-label="{{ 'product.form.quantity' | t }}">
                      <button type="button" class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% render 'icon' with 'plus' %}</button>
                    </div>
                  </div>

                {%- when 'inventory' -%}
                  <p class="ProductForm__Inventory Text--subdued" {{ block.shopify_attributes }}>
                    {{- 'product.form.inventory_quantity_count' | t: count: 4 -}}
                  </p>

                {%- when 'buy_buttons' -%}
                  <div class="ProductForm__BuyButtons" {{ block.shopify_attributes }}>
                    <button type="submit" data-use-primary-button="true" class="ProductForm__AddToCart Button Button--primary Button--full">{{- 'product.form.add_to_cart' | t -}}</button>
                  </div>

                {%- when 'text' -%}
                  {%- if block.settings.text != blank -%}
                    <div class="ProductMeta__Text" {{ block.shopify_attributes }}>
                      {{- block.settings.text -}}
                    </div>
                  {%- endif -%}
              </div>
            {%- endcase -%}
          {%- endfor -%}

          <div class="FeaturedProduct__ViewWrapper">
            <a href="#" class="Link Link--underline">{{ 'home_page.featured_product.view_product' | t }}</a>
          </div>
        </div>
      {%- endif -%}
    </div>
  </div>

  {%- comment -%}
  --------------------------------------------------------------------------------------------------------------------
  OFF SCREEN ELEMENTS
  --------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {{- product_popovers -}}
  {{- product_modals -}}
</section>

{% schema %}
{
  "name": "Featured product",
  "class": "shopify-section--bordered",
  "blocks": [
    {
      "type": "product_meta",
      "name": "Product meta",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_vendor",
          "label": "Show vendor",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_sku",
          "label": "Show SKU",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_product_rating",
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_taxes_included",
          "label": "Show price taxes notice",
          "default": false
        }
      ]
    },
    {
      "type": "variant_selector",
      "name": "Variant selector",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "dropdown"
        },
        {
          "type": "checkbox",
          "id": "show_color_swatch",
          "label": "Show color swatch",
          "default": false,
          "info": "Some colors appear white? [Learn more](http://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch)."
        },
        {
          "type": "checkbox",
          "id": "show_color_carousel",
          "label": "Show color carousel",
          "info": "A pop-up selector with variant images for choosing colors. Only enables when color swatch is disabled.",
          "default": false
        },
        {
          "type": "page",
          "id": "size_chart",
          "label": "Size chart",
          "info": "Show along option named Size."
        }
      ]
    },
    {
      "type": "share_buttons",
      "name": "Share buttons",
      "limit": 1
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "inventory_quantity_threshold",
          "label": "Inventory quantity threshold",
          "info": "Only show inventory quantity if below threshold. Choose 0 to always show.",
          "min": 0,
          "max": 50,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_price_in_button",
          "label": "Show price in add to cart button",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        }
      ]
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_label",
          "label": "Show label",
          "default": false
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        }
      ]
    },
    {
      "type": "store_pickup",
      "name": "Local pickup availability",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Show customers where they can pick up the product. [Learn more](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup#show-pickup-availability-to-your-customers)"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Sub-heading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Featured product"
    },
    {
      "type": "checkbox",
      "id": "show_headings_on_mobile",
      "label": "Show headings on mobile",
      "default": true
    },
    {
      "type": "product",
      "id": "product",
      "label": "Product"
    }
  ],
  "presets": [
    {
      "category": "Product",
      "name": "Featured product",
      "blocks": [
        {
          "type": "product_meta",
          "settings": {}
        },
        {
          "type": "share_buttons",
          "settings": {}
        },
        {
          "type": "variant_selector",
          "settings": {}
        },
        {
          "type": "buy_buttons",
          "settings": {}
        }
      ]
    }
  ]
}
{% endschema %}