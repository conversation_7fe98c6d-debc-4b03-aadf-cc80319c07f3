{%- capture flickity_options -%}
{
  "prevNextButtons": false,
  "wrapAround": true,
  "dragThreshold": 16,
  "pageDots": {% if section.blocks.size > 1 %}true{% else %}false{% endif %},
  "autoPlay": {% if section.settings.autoplay %}{{ section.settings.cycle_speed | times: 1000 }}{% else %}false{% endif %}
}
{%- endcapture -%}

{%- assign should_show_nav = false -%}

{%- for block in section.blocks -%}
  {%- if block.settings.logo -%}
    {%- assign should_show_nav = true -%}
    {%- break -%}
  {%- endif -%}
{%- endfor -%}

<section class="Section Section--spacingNormal" id="section-{{ section.id }}" data-section-id="{{ section.id }}" data-section-type="testimonials">
  <div class="TestimonialList {% if should_show_nav %}TestimonialList--withNav{% endif %} Carousel Carousel--fadeIn" data-flickity-config='{{ flickity_options }}'>
    {%- for block in section.blocks -%}
      <div id="block-{{ block.id }}" class="Carousel__Cell Testimonial {% if forloop.first %}is-selected{% endif %}" {{ block.shopify_attributes }} data-slide-index="{{ forloop.index0 }}">
        <div class="Container">
          {%- if block.settings.quote != blank -%}
            <div class="Testimonial__Content">
              {{ block.settings.quote }}
            </div>
          {%- endif -%}

          {%- if block.settings.logo != blank -%}
            <img class="Testimonial__Logo hidden-lap-and-up" src="{{ block.settings.logo | img_url: '300x' }}" alt="{{ block.settings.logo.alt | escape }}">
          {%- endif -%}
        </div>
      </div>
    {%- endfor -%}
  </div>

  {%- if should_show_nav -%}
    <div class="TestimonialNav hidden-pocket">
      {%- for block in section.blocks -%}
        {%- if block.settings.logo != blank -%}
          <img class="TestimonialNav__Item {% if forloop.first %}is-selected{% endif %}" src="{{ block.settings.logo | img_url: '300x' }}" alt="{{ block.settings.logo.alt | escape }}" data-index="{{ forloop.index0 }}">
        {%- endif -%}
      {%- endfor -%}
    </div>
  {%- endif -%}
</section>

<style>
  #section-{{ section.id }} {
    color: {{ section.settings.text_color }};
    background: {{ section.settings.background }};
  }

  #section-{{ section.id }} .flickity-page-dots .dot:not(.is-selected) {
    border-color: {{section.settings.background | color_mix: section.settings.text_color, 85}};
  }
</style>

{% schema %}
{
  "name": "Testimonials",
  "class": "shopify-section--bordered",
  "max_blocks": 6,
  "settings": [
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Auto rotate between testimonials",
      "default": true
    },
    {
      "type": "range",
      "id": "cycle_speed",
      "min": 3,
      "max": 8,
      "step": 1,
      "unit": "sec",
      "label": "Change testimonials every",
      "default": 5
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "#eaeaea"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#1c1b1b"
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo",
          "info": "340 x 80px .jpg recommended"
        },
        {
          "type": "richtext",
          "id": "quote",
          "label": "Quote",
          "default": "<p>Share what your customers are saying about your products, your company...</p>",
          "info": "For best results, keep the word count consistent in each quote."
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Text",
      "name": "Testimonials",
      "settings": {},
      "blocks": [
        {
          "type": "testimonial",
          "settings": {}
        },
        {
          "type": "testimonial",
          "settings": {}
        },
        {
          "type": "testimonial",
          "settings": {}
        }
      ]
    }
  ]
}
{% endschema %}