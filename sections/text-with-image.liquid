<section class="Section {% unless section.settings.image %}Section--spacingNormal{% endunless %}">
  {%- capture section_content -%}
    <div class="FeatureText__ContentWrapper">
      <div class="FeatureText__Content">
        {%- if section.settings.subheading != blank or section.settings.title != blank or section.settings.content != blank -%}
          <header class="SectionHeader">
            {%- if section.settings.subheading != blank -%}
              <h3 class="SectionHeader__SubHeading Heading u-h6">{{ section.settings.subheading | escape }}</h3>
            {%- endif -%}

            {%- if section.settings.title != blank -%}
              <h2 class="SectionHeader__Heading Heading u-h1">{{ section.settings.title | escape }}</h2>
            {%- endif -%}

            {%- if section.settings.content != blank -%}
              <div class="SectionHeader__Description Rte">
                {{ section.settings.content }}
              </div>
            {%- endif -%}

            {%- if section.settings.link_text != blank -%}
              <a href="{{ section.settings.link_url }}" class="Link Link--underline">{{ section.settings.link_text | escape }}</a>
            {%- endif -%}
          </header>
        {%- endif -%}
      </div>
    </div>
  {%- endcapture -%}

  {%- capture section_image -%}
    {%- if section.settings.image != blank -%}
      <div class="FeatureText__ImageWrapper">
        {%- capture supported_sizes -%}{%- render 'image-size', sizes: '400,600,700,800,900,1000,1200', image: section.settings.image -%}{%- endcapture -%}

        <div class="AspectRatio" style="max-width: {{ section.settings.image.width }}px; --aspect-ratio: {{ section.settings.image.aspect_ratio }}">
          {%- assign image_url = section.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
          <img class="Image--lazyLoad Image--slide{{ section.settings.image_position | capitalize }}" data-src="{{ image_url }}" data-expand="-200" data-widths="[{{ supported_sizes }}]" data-sizes="auto" alt="{{ section.settings.image.alt | escape }}">

          <noscript>
            <img src="{{ section.settings.image | img_url: '800x' }}" alt="{{ section.settings.image.alt | escape }}">
          </noscript>
        </div>
      </div>
    {%- endif -%}
  {%- endcapture -%}

  <div class="FeatureText {% if section.settings.image != blank %}FeatureText--withImage FeatureText--image{{ section.settings.image_position | capitalize }}{% endif %}">
    {%- if section.settings.image_position == 'left' -%}
      {{- section_image -}}
      {{- section_content -}}
    {%- else -%}
      {{- section_content -}}
      {{- section_image -}}
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Rich text with image",
  "class": "shopify-section--bordered",
  "settings": [
    {
      "type": "text",
      "id": "subheading",
      "label": "Sub-heading",
      "default": "Sub-heading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Title"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1200 x 1200px .png recommended"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Image position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "right"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Add your own custom content to give more information about your store, availability details...</p>"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "Link text"
    },
    {
      "type": "url",
      "id": "link_url",
      "label": "Link URL"
    }
  ],
  "presets": [
    {
      "category": "Text",
      "name": "Rich text with image",
      "settings": {}
    }
  ]
}
{% endschema %}