<div class="store-availability-container" data-section-id="{{ section.id }}" data-section-type="store-availability">
  {%- assign pick_up_availabilities = product_variant.store_availabilities | where: 'pick_up_enabled', true -%}

  {%- if pick_up_availabilities.size > 0 -%}
    <div class="store-availability-information">
      {%- assign closest_location = pick_up_availabilities.first -%}

      {%- if closest_location.available -%}
        {%- render 'icon', icon: 'store-availability-in-stock' -%}
      {%- else -%}
        {%- render 'icon', icon: 'store-availability-out-of-stock' -%}
      {%- endif -%}

      <div class="store-availability-information-container">
        {%- if closest_location.available -%}
          <p class="store-availability-information__title">
            {{- 'store_availability.general.pick_up_available_at_html' | t: location_name: closest_location.location.name -}}
          </p>

          <p class="store-availability-information__stock">{{ closest_location.pick_up_time }}</p>

          <p class="store-availability-information__stores">
            <button type="button" class="store-availability-information__link Link Link--underline" data-action="open-drawer" data-drawer-id="StoreAvailabilityModal-{{ product_variant.id }}">
              {%- if pick_up_availabilities.size == 1 -%}
                {{- 'store_availability.general.view_store_info' | t -}}
              {%- else -%}
                {{- 'store_availability.general.check_other_stores' | t -}}
              {%- endif -%}
            </button>
          </p>
        {%- else -%}
          <p class="store-availability-information__title">
            {{- 'store_availability.general.pick_up_unavailable_at_html' | t: location_name: closest_location.location.name -}}
          </p>
          {%- if pick_up_availabilities.size > 1 -%}
            <p class="store-availability-information__stores">
              <button type="button" class="store-availability-information__link" data-action="open-drawer" data-drawer-id="StoreAvailabilityModal-{{ product_variant.id }}">
                {{- 'store_availability.general.check_other_stores' | t -}}
              </button>
            </p>
          {%- endif -%}
        {%- endif -%}
      </div>
    </div>

    <div class="store-availabilities-modal Drawer Drawer--fromRight" aria-hidden="true" tabindex="-1" id="StoreAvailabilityModal-{{ product_variant.id }}">
      <div class="store-availabilities-modal__header Drawer__Header Drawer__Header--bordered Drawer__Header--flexible Drawer__Container">
          <div class="store-availabilities-modal__product-information">
            <h2 class="store-availabilities-modal__product-title Drawer__Title Heading u-h4"></h2>
            <p class="store-availabilities-modal__variant-title">{{ product_variant.title }}</p>
          </div>

          <button class="store-availabilities-modal__close Drawer__Close Icon-Wrapper--clickable" data-action="close-drawer" data-drawer-id="StoreAvailabilityModal-{{ product_variant.id }}" aria-label="{{ 'general.popup.close' | t }}">
            {%- render 'icon' with 'close' -%}
          </button>
        </div>

        <div class="store-availabilities-list Drawer__Content">
          <div class="Drawer__Main" data-scrollable>
            <div class="Drawer__Container">
              {%- for availability in pick_up_availabilities -%}
                <div class="store-availability-list__item">
                  <p class="store-availability-list__location"><strong>{{ availability.location.name }}</strong></p>

                  <div class="store-availability-list__item-info">
                    <div class="store-availability-list__stock">
                      {%- if availability.available -%}
                        {%- render 'icon', icon: 'store-availability-in-stock' -%} {{ 'store_availability.general.pick_up_available' | t }}, {{ availability.pick_up_time | downcase }}
                      {%- else -%}
                        {%- render 'icon', icon: 'store-availability-out-of-stock' -%} {{ 'store_availability.general.pick_up_currently_unavailable' | t }}
                      {%- endif -%}
                    </div>

                    <div class="store-availability-list__contact">
                      {%- assign address = availability.location.address -%}
                      {{- address | format_address -}}

                      {%- if address.phone.size > 0 -%}
                        {{ address.phone }}
                      {%- endif -%}
                    </div>
                  </div>
                </div>
              {%- endfor -%}
            </div>
          </div>
        </div>
      </div>
    </div>
  {%- endif -%}
</div>

{% schema %}
{
  "name": {},
  "settings": []
}
{% endschema %}
