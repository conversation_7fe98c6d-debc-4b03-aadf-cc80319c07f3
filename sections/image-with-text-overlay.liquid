<section id="section-{{ section.id }}">
  {%- capture mobile_size -%}750x{% if section.settings.image.height >= 960 %}960{% else %}{{ section.settings.image.height }}{% endif %}{%- endcapture -%}

  <div class="FlexboxIeFix">
    <div class="ImageHero {% if section.settings.section_size != 'normal' %}ImageHero--{{ section.settings.section_size }}{% endif %}" {% if section.settings.image %}style="background: url({{ section.settings.image | img_url: '1x1' }})"{% endif %}>
      <div class="ImageHero__ImageWrapper">
        <div class="ImageHero__Image {% if section.settings.overlay_opacity > 0 %}ImageHero__ImageWrapper--hasOverlay{% endif %} Image--lazyLoad Image--zoomOut"
             data-optimumx="1.4"
             data-expand="-150"
             {% if section.settings.image %}data-bgset="{{ section.settings.image | img_url: mobile_size, crop: 'center' }} 750w, {{ section.settings.image | img_url: '1000x' }} 1000w, {{ section.settings.image | img_url: '1500x' }} 1500w"{% endif %}>
        </div>

        <noscript>
          <div class="ImageHero__Image" style="background-image: url({{ section.settings.image | img_url: '1000x' }})"></div>
        </noscript>

        {%- unless section.settings.image != blank -%}
          <div class="PlaceholderBackground PlaceholderBackground--Dark">
            {{ 'lifestyle-1' | placeholder_svg_tag: 'PlaceholderBackground__Svg' }}
          </div>
        {%- endunless -%}
      </div>

      {%- if section.settings.subheading != blank or section.settings.title != blank or section.settings.content != blank -%}
        <div class="ImageHero__ContentOverlay">
          {%- if section.settings.subheading != blank or section.settings.title != blank or section.settings.content != blank -%}
            <header class="SectionHeader">
              {%- if section.settings.subheading != blank -%}
                <h3 class="SectionHeader__SubHeading Heading u-h6">{{ section.settings.subheading | escape }}</h3>
              {%- endif -%}

              {%- if section.settings.title != blank -%}
                <h2 class="SectionHeader__Heading Heading u-h1">{{ section.settings.title | escape }}</h2>
              {%- endif -%}

              {%- if section.settings.content != blank -%}
                <div class="SectionHeader__Description">
                  {{ section.settings.content }}
                </div>
              {%- endif -%}
            </header>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </div>
</section>

<style>
  #section-{{ section.id }},
  #section-{{ section.id }} .Heading {
    color: {{ section.settings.text_color }};
  }

  #section-{{ section.id }} .ImageHero__ImageWrapper--hasOverlay::before {
    {%- assign overlay_opacity = section.settings.overlay_opacity | divided_by: 100.0 -%}
    background-color: {{ section.settings.overlay_color | color_modify: 'alpha', overlay_opacity }};
  }
</style>

{% schema %}
{
  "name": "Image with text overlay",
  "settings": [
    {
      "type": "select",
      "id": "section_size",
      "label": "Section size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "large"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image",
      "info": "1500 x 800px .jpg recommended"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 30
    },
    {
      "type": "text",
      "id": "subheading",
      "label": "Sub-heading"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Tell your story"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Text",
      "default": "<p>Add your own custom content to give more information about your store, availability details...</p>"
    }
  ],
  "presets": [
    {
      "category": "Image",
      "name": "Image with text (overlay)",
      "settings": {}
    }
  ]
}
{% endschema %}