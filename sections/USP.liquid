<div class="USP" style="background-color: {{ section.settings.usp_background }};">
    {% for block in section.blocks %}
        <div class="USP__block" style="color: {{ section.settings.usp_text_color }};">
            <div class="USP__block__image">
              {% if block.settings.uspSvg != blank %}
                <img src="{{ block.settings.uspSvg  }}" alt="{{ block.settings.image.alt }}" width="50px">
                {% else %}
                <img src="{{ block.settings.image | image_url: width: 50 }}" alt="{{ block.settings.image.alt }}" />
              {% endif %}
              </div>
            <p class="USP__block__maintext">{{block.settings.usp_maintext}} </p>
            <p class="USP__block__subtext">{{block.settings.usp_subtext}} </p>
        </div>

    {% endfor %}
</div>

{% schema %}
{
  "name": "USP",
  "max_blocks": 4,
  "settings": [
    {
        "type":"color",
        "id": "usp_background",
        "label":"Background color of USP section",
        "default":"#ffffff"
    },
    {
        "type":"color",
        "id": "usp_text_color",
        "label":"Text color in USP section",
        "default":"#9d9d9d"
    }
  ],
  "blocks": [
     {
       "name": "USP",
       "type": "USP",
       "settings": [
         {
           "type": "image_picker",
           "id": "image",
           "label": "Image"
         },
         {
          "type": "text",
          "id": "uspSvg",
          "label": "USP svg",
          "info": "Copy svg image link from shopify files"
        },
         {
            "type": "text",
            "id": "usp_maintext",
            "label": "Text for usp"
          },
          {
            "type": "text",
            "id": "usp_subtext",
            "label": "Subtext for usp"
          }
       ]
     }
   ],
   "presets": [
    {
      "category": "USP",
      "name": "USP list",
      "settings": {},
      "blocks": [
        {
          "type": "USP"
        }
      ]
    }
  ]
}
{% endschema %}
