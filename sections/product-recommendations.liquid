{%- assign use_recommendations = true -%}
{%- assign hand_picked_count = 0 -%}

{%- for block in section.blocks -%}
  {%- if block.settings.product != blank -%}
    {%- assign use_recommendations = false -%}
    {%- assign hand_picked_count = hand_picked_count | plus: 1 -%}
  {%- endif -%}
{%- endfor -%}

{%- capture section_settings -%}
{
  "productId": {{ product.id | json }},
  "useRecommendations": {{ use_recommendations | json }},
  "recommendationsCount": 4
}
{%- endcapture -%}

{%- capture flickity_options -%}
{
  "prevNextButtons": true,
  "pageDots": false,
  "wrapAround": false,
  "contain": true,
  "cellAlign": "center",
  "watchCSS": true,
  "dragThreshold": 8,
  "groupCells": true,
  "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
}
{%- endcapture -%}

<section class="Section Section--spacingNormal" data-section-id="{{ section.id }}" data-section-type="product-recommendations" data-section-settings='{{ section_settings }}'>
  {%- if section.settings.heading != blank -%}
    <header class="SectionHeader SectionHeader--center">
      <div class="Container">
        <h3 class="SectionHeader__Heading Heading u-h3">{{ section.settings.heading | escape }}</h3>
      </div>
    </header>
  {%- endif -%}

  <div class="ProductRecommendations">
    {%- if hand_picked_count > 0 or recommendations.performed and recommendations.products.size > 0 -%}
      <div class="ProductListWrapper">
        <div class="ProductList ProductList--carousel Carousel" data-flickity-config='{{ flickity_options }}'>
          {%- if hand_picked_count > 0 -%}
            {%- for block in section.blocks -%}
              {%- assign product = block.settings.product -%}

              {%- if product != blank -%}
                <div class="Carousel__Cell">
                  {% render 'product-item', product: product, show_product_info: section.settings.show_product_info, show_vendor: section.settings.show_vendor, show_color_swatch: section.settings.show_color_swatch, show_labels: true %}
                </div>
              {%- endif -%}
            {%- endfor -%}
          {%- else -%}
            {%- for product in recommendations.products -%}
              <div class="Carousel__Cell">
                {% render 'product-item', product: product, show_product_info: section.settings.show_product_info, show_vendor: section.settings.show_vendor, show_color_swatch: section.settings.show_color_swatch, show_labels: true %}
              </div>
            {%- endfor -%}
          {%- endif -%}
        </div>
      </div>
    {%- else -%}
      {%- comment -%}Hide the section if there is no recommendations to show{%- endcomment -%}
      <style>
        #shopify-section-{{ section.id }} {
          display: none;
        }
      </style>
    {%- endif -%}
  </div>
</section>

{% schema %}
{
  "name": "Product recommendations",
  "class": "shopify-section--bordered",
  "templates": ["product"],
  "max_blocks": 10,
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "paragraph",
          "content": "Dynamic recommendations are not shown if products are explicitly selected."
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "You may also like"
    },
    {
      "type": "checkbox",
      "id": "show_product_info",
      "label": "Show product info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_color_swatch",
      "label": "Show color swatch",
      "info": "Some colors appear white? [Learn more](http://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch).",
      "default": false
    }
  ],
  "presets": [
    {
      "category": "Product",
      "name": "Product recommendations"
    }
  ]
}
{% endschema %}