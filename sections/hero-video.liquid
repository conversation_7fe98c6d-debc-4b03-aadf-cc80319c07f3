{% capture videoContent %}
    <div class="heroVideo__CTA Slideshow__Content Slideshow__Content--{{ section.settings.content_position }}">
        {% if section.settings.title != blank %}<h2 class="SectionHeader__Heading SectionHeader__Heading--emphasize Heading u-h1">{{ section.settings.title }}</h2>{% endif %}
        {% if section.settings.subheading != blank %}<h4 class="SectionHeader__Heading SectionHeader__Heading--emphasize Heading u-h4" >{{ section.settings.subheading }}</h4>{% endif %}

        {% if section.settings.button_text !=blank %}
            <div class="ButtonGroup">
                <a class="CtaButton Button" href="{{ section.settings.button_link }}" >{{ section.settings.button_text }} </a> 
            </div>
        {% endif %}
    </div>
{% endcapture %}

<!-- For Desktop -->
<!-- calc() is based on the header and announcement bar -->
<div class="heroVideoContainer {% if section.settings.enable_mobile_video %} heroVideoContainer--desktop{% endif %} " style="height: calc({{section.settings.height}}vh - 144.5px);">
    <video class="heroVideo"  preload loop autoplay muted playsinline>
        <source src="{{section.settings.video_url}}" type="video/mp4">
        <p>Your browser doesn't support HTML5 video. Here is a <a href="{{section.settings.video_url}}">link to the video</a> instead.</p>
    </video>
    {{videoContent}}
</div>

{% if section.settings.enable_mobile_video %}
<!-- Mobile Container -->
<!-- calc() is based on the header and announcement bar -->
<div class="heroVideoContainer {% if section.settings.enable_mobile_video %} heroVideoContainer--mobile{% endif %} " style="height:calc({{section.settings.mobile_height}}vh - 104.26px);">
    <video class="heroVideo"  preload loop autoplay muted playsinline>
        <source src="{{section.settings.mobile_video_url}}" type="video/mp4">
        <p>Your browser doesn't support HTML5 video. Here is a <a href="{{section.settings.mobile_video_url}}">link to the video</a> instead.</p>
    </video>
    {{videoContent}}
</div>

{% endif %}

{% schema %}
{
  "name": "Hero video",
  "settings": [
  {
    "type":"select",
    "id":"height",
    "label": "Height of hero container",
    "options":[
      {
        "value": "100",
        "label": "Fullscreen"
      },
      {
        "value": "50",
        "label": "Half screen"
      },
      {
        "value": "80",
        "label": "80% of screen"
      }
    ],
    "default": "100"
  },
  {
      "type":"checkbox",
      "id":"enable_mobile_video",
      "label": "Show mobile video",
      "default":false
  },
  {
    "type":"select",
    "id":"mobile_height",
    "label": "Height of mobile hero container",
    "options":[
      {
        "value": "100",
        "label": "Fullscreen"
      },
      {
        "value": "50",
        "label": "Half screen"
      },
      {
        "value": "80",
        "label": "80% of screen"
      }
    ],
    "default": "100"
  },
    {
      "type": "text",
      "id": "video_url",
      "label": "Video URL",
      "info":"Copy url from files"
    },
    {
        "type": "text",
        "id": "mobile_video_url",
        "label": "Mobile Video URL",
        "info":"Copy url from files"
    },
    {
        "type": "select",
        "id": "content_position",
        "label": "Content position",
        "options": [
          {
            "value": "middleLeft",
            "label": "Middle left"
          },
          {
            "value": "middleCenter",
            "label": "Middle center"
          },
          {
            "value": "middleRight",
            "label": "Middle right"
          },
          {
            "value": "bottomLeft",
            "label": "Bottom left"
          },
          {
            "value": "bottomCenter",
            "label": "Bottom center"
          },
          {
            "value": "bottomRight",
            "label": "Bottom right"
          }
        ],
        "default": "bottomLeft"
      },
      {
        "type": "text",
        "id": "subheading",
        "label": "Sub-heading",
        "default": "Slide title"
      },
      {
        "type": "text",
        "id": "title",
        "label": "Heading",
        "default": "Tell your story"
      },
      {
        "type": "header",
        "content": "Button"
      },
      {
        "type": "text",
        "id": "button_text",
        "label": "Text"
      },
      {
        "type": "url",
        "id": "button_link",
        "label": "Link"
      }
  ],
  "presets": [
    {
      "category": "Video",
      "name": "Hero video",
      "settings": {}
    }
  ]
}
{% endschema %}