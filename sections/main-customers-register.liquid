<section data-section-id="reset-password" data-section-type="reset-password">
  <div class="Container">
    <div class="PageContent PageContent--fitScreen PageContent--extraNarrow">
      {%- form 'create_customer', name: 'create_customer', class: 'Form Form--spacingTight', id: 'create_customer' -%}
        <header class="Form__Header">
          <h1 class="Form__Title Heading u-h1">{{ 'customer.register.title' | t }}</h1>
          <p class="Form__Legend">{{ 'customer.register.description' | t }}</p>
        </header>

        {%- if form.errors -%}
          <div class="Form__Alert Alert Alert--error">
            <ul class="Alert__ErrorList">
              {%- for field in form.errors -%}
                {%- if field == 'form' -%}
                  <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                {%- else -%}
                  <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                {%- endif -%}
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        {%- if request.locale.iso_code == 'ja' -%}
          <div class="Form__Item">
            <input type="text" class="Form__Input" name="customer[last_name]" aria-label="{{ 'customer.register.last_name' | t }}" placeholder="{{ 'customer.register.last_name' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.register.last_name' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="customer[first_name]" aria-label="{{ 'customer.register.first_name' | t }}" placeholder="{{ 'customer.register.first_name' | t }}" autofocus>
            <label class="Form__FloatingLabel">{{ 'customer.register.first_name' | t }}</label>
          </div>
        {%- else -%}
          <div class="Form__Item">
            <input type="text" class="Form__Input" name="customer[first_name]" aria-label="{{ 'customer.register.first_name' | t }}" placeholder="{{ 'customer.register.first_name' | t }}" autofocus>
            <label class="Form__FloatingLabel">{{ 'customer.register.first_name' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="customer[last_name]" aria-label="{{ 'customer.register.last_name' | t }}" placeholder="{{ 'customer.register.last_name' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.register.last_name' | t }}</label>
          </div>
        {%- endif -%}

        <div class="Form__Item">
          <input type="email" class="Form__Input" name="customer[email]" aria-label="{{ 'customer.register.email' | t }}" placeholder="{{ 'customer.register.email' | t }}" required="required">
          <label class="Form__FloatingLabel">{{ 'customer.register.email' | t }}</label>
        </div>
        <div class="Form__Item">
          <input type="tel" class="Form__Input" name="customer[note][phone]" aria-label="{{ 'customer.register.phone' | t }}" placeholder="{{ 'customer.register.phone' | t }}" required="required">
          <label class="Form__FloatingLabel">{{ 'customer.register.phone' | t }}</label>
        </div>
        

        <div class="Form__Item">
          <input type="password" class="Form__Input" name="customer[password]" aria-label="{{ 'customer.register.password' | t }}" placeholder="{{ 'customer.register.password' | t }}" required="required">
          <label class="Form__FloatingLabel">{{ 'customer.register.password' | t }}</label>
        </div>
        
        <div class="Form__Item">
          <input type="hidden" name="customer[accepts_marketing]" value="false" />
      
          <input type="checkbox" name="customer[accepts_marketing]" />
          <label for="accepts-marketing">Tilmeld mig email og SMS Markedsføring fra Kundeklubben</label>
        </div>

        <button type="submit" class="Form__Submit Button Button--primary Button--full">{{ 'customer.register.submit' | t }}</button>
      {%- endform -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Customer register"
}
{% endschema %}