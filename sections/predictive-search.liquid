
{%- liquid
  assign search_results = null | sort

  for result in search.results
    unless result.tags contains 'hide_product'
      assign search_result = result | sort
      assign search_results = search_results | concat: search_result
    endunless
  endfor

-%}

<div class="Segment">
  <div class="Segment__Title Segment__Title--flexed">
    {%- if search.types contains 'product' -%}
      <span class="Heading Text--subdued u-h7">{{ 'search.general.results_count' | t: count: search_results.size }}</span>
    {%- else -%}
      {%- if search.types contains 'article' and search.types contains 'page' -%}
        <span class="Heading Text--subdued u-h7">{{ 'search.general.pages_and_articles' | t }}</span>
        {%- elsif search.types contains 'article' -%}
        <span class="Heading Text--subdued u-h7">{{ 'search.general.articles' | t }}</span>
      {%- else -%}
        <span class="Heading Text--subdued u-h7">{{ 'search.general.pages' | t }}</span>
      {%- endif -%}
    {%- endif -%}

    {%- if search_results.size > 0 -%}
      <a class="Heading Link Link--secondary u-h7" href="{{ routes.search_url }}?q={{ search.terms }}&type={{ search.types | join: ',' }}">{{ 'search.general.view_all' | t }}</a>
    {%- endif -%}
  </div>

  <div class="Segment__Content">
    {%- if search_results.size == 0 -%}
      <p>{{ 'search.general.no_results' | t }}</p>
    {%- else -%}
      {%- if search.types contains 'product' -%}
        {%- if settings.search_mode == 'product' -%}
          {%- assign results_count = 4 -%}
        {%- else -%}
          {%- assign results_count = 3 -%}
        {%- endif -%}

        <div class="Grid Grid--xl">
          {%- for result in search_results limit: results_count -%}
            {%- comment -%}The inline onclick is a bit hacky, but it's easier on mobile to have the full line clickable{%- endcomment -%}
            <div class="Grid__Cell 1/{{ results_count }}--tablet-and-up" onclick="window.location.href = '{{ result.url }}'">
              {%- render 'product-item', product: result, show_labels: false, show_product_info: true, show_vendor: false -%}
            </div>
          {%- endfor -%}
        </div>
      {%- else -%}
        <ul class="Linklist">
          {%- for result in search_results limit: 6 -%}
            <li class="Linklist__Item">
              <a href="{{ result.url }}" class="Link Link--secondary">{{ result.title }}</a>
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}
    {%- endif -%}
  </div>
</div>

{% schema %}
{
  "name": "Predictive search"
}
{% endschema %}