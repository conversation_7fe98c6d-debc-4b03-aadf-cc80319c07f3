<div class="collage">
    {% for block in section.blocks %}
        <div class="collageImage">
          {%- capture supported_sizes -%}{%- render 'image-size', sizes: '200,300,400,500,600,700,800,850,900,950,1000,1200', image: block.settings.image -%}{%- endcapture -%}
          {%- assign image_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

          <img class="Image--lazyLoad Image--fadeIn"
               data-src="{{ image_url }}"
               data-widths="[{{ supported_sizes }}]"
               data-sizes="auto"
               alt="{{ product.featured_media.alt | escape }}"
               data-media-id="{{ product.featured_media.id }}">

            <div class="collageContent">
              <span class="collageContent__subtext" style="color: {{ block.settings.color_text_second }};" {% if block.settings.disable_mobile_text %} class="hideOnMobile" {% endif %}>
                {{- block.settings.subtext -}}
              </span>

              <span class="collageContent__text" style="color: {{ block.settings.color_text }};" {% if block.settings.disable_mobile_text %} class="hideOnMobile" {% endif %}>
                {{- block.settings.text -}}
              </span>

              {% if block.settings.cta_text != blank %}
                <a class="collageContent__CtaBtn Button Button--primary" href="/collections/{{block.settings.collection}}" target="_self">{{block.settings.cta_text}}</a>
              {% endif %}
            </div>
          </div>
    {% endfor %}
</div>

{% schema %}
{
  "name": "Collage",
  "max_blocks": 4,
  "settings": [
    {
        "type": "text",
        "id": "title",
        "label": "title"
    }
  ],
  "blocks": [
     {
       "name": "Image Block",
       "type": "text",
       "settings": [
         {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
         },
         {
           "type": "image_picker",
           "id": "image",
           "label": "Collage Image"
         },
         {
          "type": "text",
          "id": "cta_text",
          "label": "Text for cta button"
         },
         {
            "type": "text",
            "id": "text",
            "label": "Text"
         },
         {
          "type": "text",
          "id": "subtext",
          "label": "Subtext"
          },
         {
            "type": "color",
            "id": "color_text",
            "label": "color for text",
            "default": "#fff"
         },
         {
          "type": "color",
          "id": "color_text_second",
          "label": "color for subtext",
          "default": "#90794E"
          },
         {
            "type": "checkbox",
            "id": "disable_mobile_text",
            "label": "Disable text on mobile",
            "default": true
         }
       ]
     }
   ],
  "presets": [
     {
       "name": "collage",
       "settings": {
        },
       "blocks": [
         {
           "type": "text"
         }
       ]
     }
   ]
}
{% endschema %}
