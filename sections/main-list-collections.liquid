{%- assign collection_count = 0 -%}

{%- if section.settings.collections_to_show == 'all' -%}
  {%- for collection in collections -%}
    {%- unless collection.handle == 'frontpage' -%}
      {%- assign collection_count = collection_count | plus: 1 -%}
    {%- endunless -%}
  {%- endfor -%}
{%- else -%}
  {%- assign collection_count = section.blocks.size -%}
{%- endif -%}

{%- if collection_count == 0 -%}
  <div class="EmptyState">
    <div class="container">
      <h3 class="EmptyState__Title Heading u-h5">{{ 'collection_list.general.empty_title' | t }}</h3>
      <a href="{{ routes.root_url }}" class="EmptyState__Action Button Button--primary">{{ 'collection_list.general.empty_button' | t }}</a>
    </div>
  </div>
{%- else -%}
  <section id="section-{{ section.id }}" data-section-id="{{ section.id }}">
    <header class="PageHeader">
      <div class="SectionHeader SectionHeader--center">
        <h1 class="SectionHeader__Heading Heading u-h1">
          {%- if template == 'list-collections' -%}
            {{- 'collection_list.general.title' | t -}}
          {%- else -%}
            {{- page.title -}}
          {%- endif -%}
        </h1>

        {%- if request.page_type == 'page' and page.content != blank -%}
          <div class="SectionHeader__Description">
            {{- page.content -}}
          </div>
        {%- endif -%}
      </div>
    </header>

    {%- paginate collections by 48 -%}
      <div class="CollectionList CollectionList--grid {% if section.settings.add_spacing %}CollectionList--spaced{% endif %}">
        {%- if linklist.handle != blank -%}
          {%- for link in linklist.links -%}
            {%- if link.type != 'collection_link' -%}
              {%- continue -%}
            {%- endif -%}

            {%- render 'collection-item', collection: link.object, apply_overlay: section.settings.apply_overlay -%}
          {%- endfor -%}
        {%- else -%}
          {%- if section.settings.collections_to_show == 'all' -%}
            {%- for collection in collections -%}
              {%- render 'collection-item', collection: collection, apply_overlay: section.settings.apply_overlay -%}
            {%- endfor -%}
          {%- else -%}
            {%- for block in section.blocks -%}
              {%- assign collection = collections[block.settings.collection] -%}
              {%- render 'collection-item', collection: collection, apply_overlay: section.settings.apply_overlay, block: block -%}
            {%- endfor -%}
          {%- endif -%}
        {%- endif -%}
      </div>

      {%- if section.settings.collections_to_show == 'all' -%}
        {%- render 'pagination', paginate: paginate, tight: true -%}
      {%- endif -%}
    {%- endpaginate -%}
  </section>
{%- endif -%}

<style>
  #section-{{ section.id }} .CollectionItem .Heading {
    color: {{ section.settings.text_color }};
  }

  #section-{{ section.id }} .CollectionItem__Link {
    color: {{ section.settings.button_color }};
    border-color: {{ section.settings.text_color }};
  }

  #section-{{ section.id }} .CollectionItem__Link::before {
    background-color: {{ section.settings.text_color }};
  }

  @media (-moz-touch-enabled: 0), (hover: hover) {
    #section-{{ section.id }} .CollectionItem__Link:hover {
      color: {{ section.settings.text_color }};
    }
  }
</style>

{% schema %}
{
  "name": "List collections page",
  "settings": [
    {
      "type": "paragraph",
      "content": "All of your collections are listed by default. To customize your list, choose 'Selected' and add collections."
    },
    {
      "type": "radio",
      "id": "collections_to_show",
      "label": "Collections to show",
      "options": [
        {
          "value": "all",
          "label": "All"
        },
        {
          "value": "selected",
          "label": "Selected"
        }
      ],
      "default": "all"
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "Image size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "normal"
    },
    {
      "type": "checkbox",
      "id": "add_spacing",
      "label": "Add spacing",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "expand_collection",
      "label": "Expand collections to fill row",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "apply_overlay",
      "label": "Apply overlay on image",
      "info": "This can improve text visibility.",
      "default": true
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button text",
      "default": "#363636"
    }
  ],
  "blocks": [
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1120 x 1200px .jpg recommended"
        },
        {
          "type": "select",
          "id": "image_alignment",
          "label": "Image alignment",
          "options": [
            {
              "value": "top left",
              "label": "Top left"
            },
            {
              "value": "top center",
              "label": "Top center"
            },
            {
              "value": "top right",
              "label": "Top right"
            },
            {
              "value": "center left",
              "label": "Middle left"
            },
            {
              "value": "center center",
              "label": "Middle center"
            },
            {
              "value": "center right",
              "label": "Middle right"
            },
            {
              "value": "bottom left",
              "label": "Bottom left"
            },
            {
              "value": "bottom center",
              "label": "Bottom center"
            },
            {
              "value": "bottom right",
              "label": "Bottom right"
            }
          ],
          "default": "center center"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content position",
          "options": [
            {
              "value": "middleLeft",
              "label": "Middle left"
            },
            {
              "value": "middleRight",
              "label": "Middle right"
            },
            {
              "value": "middleCenter",
              "label": "Middle center"
            },
            {
              "value": "bottomLeft",
              "label": "Bottom left"
            },
            {
              "value": "bottomRight",
              "label": "Bottom right"
            },
            {
              "value": "bottomCenter",
              "label": "Bottom center"
            }
          ],
          "default": "bottomLeft"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Sub-heading",
          "default": "Sub-heading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button text",
          "default": "View products"
        }
      ]
    }
  ]
}
{% endschema %}