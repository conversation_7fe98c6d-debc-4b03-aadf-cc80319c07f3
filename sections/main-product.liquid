{%- assign meta_block = section.blocks | where: 'type', 'product_meta' | first -%}
{%- assign share_buttons_block = section.blocks | where: 'type', 'share_buttons' | first -%}
{%- assign inventory_block = section.blocks | where: 'type', 'inventory' | first -%}
{%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

{%- capture section_settings -%}
{
  "enableHistoryState": true,
  "templateSuffix": {{ product.template_suffix | json }},
  "showInventoryQuantity": {% if inventory_block != blank %}true{% else %}false{% endif %},
  "showSku": {{ meta_block.settings.show_sku | json }},
  "stackProductImages": {{ section.settings.stack_images | json }},
  "showThumbnails": {{ section.settings.show_thumbnails | json }},
  "enableVideoLooping": {{ section.settings.enable_video_looping | json }},
  "inventoryQuantityThreshold": {{ inventory_block.settings.inventory_quantity_threshold | default: 0 }},
  "showPriceInButton": {{ buy_buttons_block.settings.show_price_in_button | json }},
  "enableImageZoom": {{ section.settings.enable_image_zoom | json }},
  "showPaymentButton": {{ buy_buttons_block.settings.show_payment_button | json }},
  "useAjaxCart": {% if settings.cart_type == 'drawer' %}true{% else %}false{% endif %}
}
{%- endcapture -%}

<script>
  // To power the recently viewed products section, we save the ID of the product inside the local storage
  (() => {
    let items = JSON.parse(localStorage.getItem('recentlyViewedProducts') || '[]');

    // We check if the current product already exists, and if it does not, we add it at the start
    if (!items.includes({{ product.id }})) {
      items.unshift({{ product.id }});
    }

    // Then, we save the current product into the local storage, by keeping only the 8 most recent
    try {
      localStorage.setItem('recentlyViewedProducts', JSON.stringify(items.slice(0, 8)));
    } catch (error) {
      // Do nothing, this may happen in Safari in incognito mode
    }
  })();
</script>

<section class="Product Product--{{ section.settings.image_size }}" data-section-id="{{ section.id }}" data-section-type="product" data-section-settings='{{ section_settings }}'>
  <div class="Product__Wrapper">
    {%- capture action_list_items -%}
      {%- if section.settings.enable_image_zoom -%}
        <div class="Product__ActionItem hidden-lap-and-up">
          <button class="RoundButton RoundButton--small RoundButton--flat" aria-label="{{ 'product.slideshow.zoom' | t | escape }}" data-action="open-product-zoom">{% render 'icon' with 'plus' %}</button>
        </div>
      {%- endif -%}

      {%- if share_buttons_block != blank -%}
        <div class="Product__ActionItem hidden-lap-and-up">
          <button class="RoundButton RoundButton--small RoundButton--flat" data-action="toggle-social-share" data-animate-bottom aria-expanded="false">
            <span class="RoundButton__PrimaryState">{% render 'icon' with 'share' %}</span>
            <span class="RoundButton__SecondaryState">{% render 'icon' with 'close' %}</span>
          </button>

          {%- assign share_url = shop.url | append: product.url -%}
          {%- assign twitter_text = product.title -%}
          {%- assign pinterest_description = product.description | strip_html | truncatewords: 15 | url_param_escape -%}
          {%- assign pinterest_image = product.featured_media | img_url: '1024x' | prepend: 'https:' -%}

          <div class="Product__ShareList" aria-hidden="true">
            <a class="Product__ShareItem" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- render 'icon' with 'facebook' -%} Facebook</a>
            <a class="Product__ShareItem" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- render 'icon' with 'pinterest' -%} Pinterest</a>
            <a class="Product__ShareItem" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- render 'icon' with 'twitter' -%} Twitter</a>
          </div>
        </div>
      {%- endif -%}
    {%- endcapture -%}



    {%- comment -%}
    --------------------------------------------------------------------------------------------------------------------
    PRODUCT GALLERY
    --------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}

    {%- assign initial_media_id = product.featured_media.id -%}
    {%- assign initial_media_index = 0 -%}
    {%- assign media_count = 0 -%}

    {%- capture slideshow_media -%}
      {%- for media in product.media -%}
        {%- if product.selected_variant and media.id == product.selected_variant.featured_media.id -%}
          {%- assign initial_media_index = media_count -%}
          {%- assign initial_media_id = media.id -%}
        {%- endif -%}

        {%- capture supported_sizes -%}{%- render 'image-size', sizes: '200,400,600,700,800,900,1000,1200,1400,1600', image: media -%}{%- endcapture -%}

        {%- case media.media_type -%}
          {%- when 'image' -%}
            <div id="Media{{ media.id }}" tabindex="0" class="Product__SlideItem Product__SlideItem--image Carousel__Cell {% if initial_media_id == media.id %}is-selected{% endif %}" data-media-type="image" data-media-id="{{ media.id }}" data-media-position="{{ media.position }}" data-image-media-position="{% increment image_position %}">
              <div class="AspectRatio AspectRatio--withFallback" style="padding-bottom: {{ 100.0 | divided_by: media.aspect_ratio }}%; --aspect-ratio: {{ media.aspect_ratio }};">
                {% assign image_url = media | img_url: '1x1' | replace: '_1x1.', '_{width}x.' %}

                <img class="Image--lazyLoad Image--fadeIn" data-src="{{ image_url }}" data-widths="[{{ supported_sizes }}]" data-sizes="auto" data-expand="-100" alt="{{ media.alt | escape }}" data-max-width="{{ media.width }}" data-max-height="{{ media.height }}" data-original-src="{{ media | img_url: 'master' }}">
                <span class="Image__Loader"></span>

                <noscript>
                  <img src="{{ media | img_url: '800x' }}" alt="{{ media.alt | escape }}">
                </noscript>
              </div>
            </div>

          {%- when 'external_video' -%}
            <div id="Media{{ media.id }}" tabindex="-1" class="Product__SlideItem Product__SlideItem--video Carousel__Cell {% if initial_media_id == media.id %}is-selected{% endif %}" data-media-type="external_video" data-media-id="{{ media.id }}" data-media-position="{{ media.position }}" data-video-host="{{ media.host | escape }}" data-video-id="{{ media.external_id | escape }}">
              <div class="Image--lazyLoad Image--fadeIn" data-expand="-100">
                <div class="VideoWrapper">
                  {{- media | external_video_tag: image_size: '1024x' -}}
                </div>
              </div>
            </div>

          {%- when 'video' -%}
            <div id="Media{{ media.id }}" tabindex="-1" class="Product__SlideItem Product__SlideItem--video Carousel__Cell {% if initial_media_id == media.id %}is-selected{% endif %}" data-media-type="video" data-media-id="{{ media.id }}" data-media-position="{{ media.position }}">
              <div class="Image--lazyLoad Image--fadeIn" data-expand="-100">
                <div class="VideoWrapper VideoWrapper--native" style="padding-bottom: {{ 100.0 | divided_by: media.aspect_ratio }}%">
                  {{- media | video_tag: image_size: '1024x', controls: true -}}
                </div>
              </div>
            </div>

          {%- when 'model' -%}
            <div id="Media{{ media.id }}" tabindex="-1" class="Product__SlideItem Product__SlideItem--model Carousel__Cell {% if initial_media_id == media.id %}is-selected{% endif %}" data-media-type="model" data-media-id="{{ media.id }}" data-media-position="{{ media.position }}">
              <div class="Image--lazyLoad Image--fadeIn" data-expand="-100">
                <div class="ModelWrapper">
                  {{- media | model_viewer_tag: image_size: '1024x', reveal: 'interaction', toggleable: true -}}
                </div>
              </div>
            </div>
        {%- endcase -%}

        {%- assign media_count = media_count | plus: 1 -%}
      {%- endfor -%}
    {%- endcapture -%}

    {%- if product.media.size > 0 -%}
      <div class="Product__Gallery {% if section.settings.stack_images %}Product__Gallery--stack{% endif %} {% if section.settings.show_thumbnails and media_count > 1 %}Product__Gallery--withThumbnails{% else %}Product__Gallery--withDots{% endif %}">
        <span id="ProductGallery" class="Anchor"></span>

        {%- if action_list_items != blank -%}
          <div class="Product__ActionList hidden-lap-and-up {% if product.media[initial_media_index].media_type != 'image' %}is-hidden{% endif %}">
            {{ action_list_items }}
          </div>
        {%- endif -%}

        {%- capture slideshow_nav -%}
          {%- if media_count > 1 -%}
            {%- if section.settings.show_thumbnails -%}
              <div class="Product__SlideshowNav Product__SlideshowNav--thumbnails">
                <div class="Product__SlideshowNavScroller">
                  {%- for media in product.media -%}
                    {%- capture nav_item_badge -%}
                      {%- case media.media_type -%}
                        {%- when 'model' -%}
                          <span class="Product__SlideshowNavBadge">{% render 'icon', icon: 'media-model-badge' %}</span>

                        {%- when 'video' or 'external_video' -%}
                          <span class="Product__SlideshowNavBadge">{% render 'icon', icon: 'media-video-badge' %}</span>
                      {%- endcase -%}
                    {%- endcapture -%}

                    {%- if section.settings.stack_images -%}
                      <a href="#Media{{ media.id }}" data-offset="-25" data-focus-on-click data-media-id="{{ media.id }}" class="Product__SlideshowNavImage AspectRatio {% if forloop.first %}is-selected{% endif %}" style="--aspect-ratio: {{ media.preview_image.aspect_ratio }}">
                        <img src="{{ media | img_url: '160x' }}" alt="{{ media.alt | escape }}">
                        {{- nav_item_badge -}}
                      </a>
                    {%- else -%}
                      <a href="{{ media | img_url: '1024x' }}" data-media-id="{{ media.id }}" class="Product__SlideshowNavImage AspectRatio {% if media.id == initial_media_id %}is-selected{% endif %}" style="--aspect-ratio: {{ media.preview_image.aspect_ratio }}">
                        <img src="{{ media | img_url: '160x' }}" alt="{{ media.alt | escape }}">
                        {{- nav_item_badge -}}
                      </a>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              </div>
            {%- endif -%}

            {%- if section.settings.stack_images -%}
              <div class="Product__SlideshowNav Product__SlideshowNav--dots">
                <div class="Product__SlideshowNavScroller">
                  {%- for media in product.media -%}
                    <a href="#Media{{ media.id }}" data-offset="-25" data-focus-on-click class="Product__SlideshowNavDot {% if forloop.first %}is-selected{% endif %}"></a>
                  {%- endfor -%}
                </div>
              </div>
            {%- endif -%}
          {%- endif -%}
        {%- endcapture -%}

        {%- if section.settings.stack_images -%}
          {{- slideshow_nav -}}
        {%- endif -%}

        {%- capture flickity_options -%}
        {
          "prevNextButtons": false,
          "pageDots": false,
          "adaptiveHeight": true,
          "watchCSS": true,
          "dragThreshold": 8,
          "initialIndex": {{ initial_media_index }},
          "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
        }
        {%- endcapture -%}

        <div class="Product__Slideshow {% if section.settings.enable_image_zoom %}Product__Slideshow--zoomable{% endif %} Carousel" data-flickity-config='{{ flickity_options }}'>
          {{ slideshow_media }}
        </div>

        {%- comment -%}Add the "view in your space" button, which allows to show the product in customer's environment (if the device supports it){%- endcomment -%}
        {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}

        {%- if first_3d_model -%}
          <button class="Product__ViewInSpace Button Button--full" data-shopify-xr data-shopify-model3d-id="{{ first_3d_model.id }}" data-shopify-model3d-default-id="{{ first_3d_model.id }}" data-shopify-title="{{ product.title | escape }}" data-shopify-xr-hidden>
            {%- render 'icon', icon: 'media-view-in-space' -%} {{- 'product.general.view_in_space' | t -}}
          </button>
        {%- endif -%}

        {%- if media_count > 1 -%}
          {%- comment -%}
            IMPLEMENTATION NOTE: The reason we create ourselves our own UI instead of relying of the built-in feature of Flickity is that,
            since the media API, Shopify requires to add arrows next to the dot. Unfortunately this layout is not
            achievable easily using Flickity options only, so we have to run our own
          {%- endcomment -%}

          <div class="Product__SlideshowMobileNav {% if section.settings.stack_images or section.settings.show_thumbnails %}hidden-lap-and-up{% endif %}">
            <button class="Product__SlideshowNavArrow Product__SlideshowNavArrow--previous" type="button" data-direction="previous" aria-label="{{ 'general.accessibility.previous' | t }}">
              {% render 'icon' with 'media-arrow-left' %}
            </button>

            <div class="flickity-page-dots">
              {%- for i in (1..media_count) -%}
                <button type="button" aria-label="{{ 'product.slideshow.go_to_image' | t: i: i | escape }}" class="dot {% if forloop.index0 == initial_media_index %}is-selected{% endif %}" data-index="{{ forloop.index0 }}"></button>
              {%- endfor -%}
            </div>

            <button class="Product__SlideshowNavArrow Product__SlideshowNavArrow--next" type="button" data-direction="next" aria-label="{{ 'general.accessibility.next' | t }}">
              {% render 'icon' with 'media-arrow-right' %}
            </button>
          </div>
        {%- endif -%}

        {%- unless section.settings.stack_images -%}
          {{- slideshow_nav -}}
        {%- endunless -%}
      </div>
    {%- endif -%}

    {%- capture product_aside -%}
      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      PRODUCT TABS
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- render 'product-tabs' -%}

      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      PRODUCT WEAR IT WITH
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}

      {%- assign buy_it_with_block = section.blocks | where: 'type', 'buy_it_with' | first -%}

      {%- if buy_it_with_block != blank and buy_it_with_block.settings.product != blank -%}
        {%- assign associated_product = buy_it_with_block.settings.product -%}

        <div class="Section Section--spacingNormal">
          <header class="SectionHeader SectionHeader--center">
            <h3 class="SectionHeader__Heading Heading u-h4">{{ buy_it_with_block.settings.title | escape }}</h3>
          </header>

          {% render 'product-item', product: associated_product, use_horizontal: true, show_labels: false, show_product_info: true, show_vendor: false, show_price_on_hover: false %}
        </div>
      {%- endif -%}
    {%- endcapture -%}

    <div class="Product__InfoWrapper">
      <div class="Product__Info {% if media_count == 0 %}Product__Info--noGallery{% endif %}">
        <div class="Container">
          {%- assign has_ouputted_variant_selector = false -%}

          {%- form 'product', product, class: 'ProductForm' -%}
            {%- render 'product-data', product: product -%}

            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when '@app' -%}
                  {%- render block -%}

              {%- when 'linked' -%}
                {%- render 'linked-products' -%}

                {%- when 'countdown' -%}
                  {%- render 'countdownClock' -%}

                {%- when 'product_meta' -%}
                  {%- render 'product-meta', form: form, block: block, product: product -%}

                {%- when 'description' -%}
                  {%- if product.description != blank -%}
                    <div class="ProductMeta__Description" {{ block.shopify_attributes }}>
                      <div class="Rte">
                        {{- product.description | replace: 'data-section-type', 'data-section-type-placeholder' -}}
                      </div>
                    </div>
                  {%- endif -%}

                {%- when 'share_buttons' -%}
                  <div class="ProductMeta__ShareButtons hidden-pocket" {{ block.shopify_attributes }}>
                    <span class="ProductMeta__ShareTitle Heading Text--subdued u-h7">{{ 'product.form.share' | t }}</span>

                    {%- assign share_url = shop.url | append: product.url -%}
                    {%- assign twitter_text = product.title -%}
                    {%- assign pinterest_description = product.description | strip_html | truncatewords: 15 | url_param_escape -%}
                    {%- assign pinterest_image = product.featured_image | img_url: 'large' | prepend: 'https:' -%}

                    <div class="ProductMeta__ShareList Text--subdued">
                      <a class="ProductMeta__ShareItem" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener" aria-label="Facebook">{%- render 'icon' with 'facebook' -%}</a>
                      <a class="ProductMeta__ShareItem" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener" aria-label="Twitter">{%- render 'icon' with 'twitter' -%}</a>
                      <a class="ProductMeta__ShareItem" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener" aria-label="Pinterest">{%- render 'icon' with 'pinterest' -%}</a>
                    </div>
                  </div>

                {%- when 'variant_selector' -%}
                  {%- assign has_ouputted_variant_selector = true -%}
                  {%- render 'product-form', block: block, product: product -%}

                {%- when 'quantity_selector' -%}
                  <div class="ProductForm__QuantitySelector" {{ block.shopify_attributes }}>
                    {%- if block.settings.show_label -%}
                      <span class="ProductForm__Label">{{ 'product.form.quantity' | t }}:</span>
                    {%- endif -%}

                    <div class="QuantitySelector QuantitySelector--large">
                      {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

                      <button type="button" class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% render 'icon' with 'minus' %}</button>
                      <input type="text" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" name="quantity" value="1" aria-label="{{ 'product.form.quantity' | t }}">
                      <button type="button" class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% render 'icon' with 'plus' %}</button>
                    </div>
                  </div>

                {%- when 'inventory' -%}
                  {%- assign hide_inventory_quantity_by_default = false -%}

                  {%- if product.selected_or_first_available_variant.inventory_management == blank or product.selected_or_first_available_variant.inventory_quantity <= 0 -%}
                    {%- assign hide_inventory_quantity_by_default = true -%}
                  {%- endif -%}

                  {%- if block.settings.inventory_quantity_threshold != 0 and product.selected_or_first_available_variant.inventory_quantity > block.settings.inventory_quantity_threshold -%}
                    {%- assign hide_inventory_quantity_by_default = true -%}
                  {%- endif -%}

                  <p class="ProductForm__Inventory Text--subdued" {% if hide_inventory_quantity_by_default %}style="display: none"{% endif %} {{ block.shopify_attributes }}>
                    {%- if block.settings.inventory_quantity_threshold == 0 -%}
                      {{- 'product.form.inventory_quantity_count' | t: count: product.selected_or_first_available_variant.inventory_quantity -}}
                    {%- else -%}
                      {{- 'product.form.low_inventory_quantity_count' | t: count: product.selected_or_first_available_variant.inventory_quantity -}}
                    {%- endif -%}
                  </p>

                {%- when 'buy_buttons' -%}
                  {%- if block.settings.show_payment_button == false or product.selling_plan_groups.size > 0 -%}
                    {%- assign use_primary_button = true -%}
                  {%- else -%}
                    {%- assign use_primary_button = false -%}
                  {%- endif -%}

                  <div id="AddToCart" class="ProductForm__BuyButtons" {{ block.shopify_attributes }}>
                    {%- if block.settings.show_payment_button and product.selected_or_first_available_variant.available == false -%}
                      <style>
                        #shopify-section-{{ section.id }} .shopify-payment-button {
                          display: none;
                        }
                      </style>
                    {%- endif -%}


                    <div class="button-wk-wrap">

                      <div class="custom-button-wk">
                        <!-- include 'wishlist-button' with '{{ product.id }}' -->
                      </div>

                      <button type="submit" data-use-primary-button="{% if use_primary_button %}true{% else %}false{% endif %}" class="ProductForm__AddToCart Button {% if product.selected_or_first_available_variant.available and use_primary_button %}Button--primary{% else %}Button--secondary{% endif %} Button--full" {% if product.selected_or_first_available_variant.available %}data-action="add-to-cart"{% else %}disabled="disabled"{% endif %}>
                        {%- if product.selected_or_first_available_variant.available -%}
                          <span>{% if product.template_suffix == 'pre-order' %}{{ 'product.form.pre_order' | t }}{% else %}{{ 'product.form.add_to_cart' | t }}{% endif %}</span>

                          {%- if block.settings.show_price_in_button -%}
                            <span class="Button__SeparatorDot"></span>
                            <span>{{ product.selected_or_first_available_variant.price | money }}</span>
                          {%- endif -%}
                        {%- else -%}
                          {{- 'product.form.sold_out' | t -}}
                        {%- endif -%}
                      </button>
                    </div>

                    {%- if block.settings.show_payment_button and product.template_suffix != 'pre-order' -%}
                      {{ form | payment_button }}
                    {%- endif -%}
                  </div>

                  {%- comment -%}
                  <a class="klaviyo-bis-trigger Button Button--primary Button--full Btn__Klaviyo" {%- if product.selected_variant.available -%}style="display:none;"{%- endif -%} href="#">
                    <span>{{ 'klaviyo.backInStock.trigger.text' | t }}</span>
                  </a>
                  {%- endcomment -%}

                {%- when 'store_pickup' -%}
                  {%- comment -%}The availability container will be added automatically if there is store pickup available{%- endcomment -%}
                  <div class="ProductMeta__StoreAvailabilityContainer" {{ block.shopify_attributes }}></div>

                {%- when 'text' -%}
                  {%- if block.settings.text != blank -%}
                    <div class="ProductMeta__Text" {{ block.shopify_attributes }}>
                      {{- block.settings.text -}}
                    </div>
                  {%- endif -%}

                {%- when 'liquid' -%}
                  {%- if block.settings.liquid != blank -%}
                    <div class="ProductMeta__Text" {{ block.shopify_attributes }}>
                      {{ block.settings.liquid }}
                    </div>
                  {%- endif -%}

              {%- endcase -%}
            {%- endfor -%}

            {%- unless has_ouputted_variant_selector -%}
              {%- comment -%}If for any reason the merchant has removed the variant selector block, we still output the ID here{%- endcomment -%}
              <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
            {%- endunless -%}
          {%- endform -%}

          {%- if section.settings.stack_images and product_aside != blank and media_count > 0 -%}
            <div class="Product__QuickNav hidden-pocket">
              <div class="Product__QuickNavWrapper">
                <a href="#ProductAside" class="Heading Link Link--secondary u-h7">{{ 'product.form.view_info' | t }} {% render 'icon' with 'select-arrow-right' %}</a>
                <a href="#ProductGallery" class="Heading Link Link--secondary u-h7">{{ 'product.form.view_images' | t }} {% render 'icon' with 'select-arrow-right' %}</a>
              </div>
            </div>
          {%- endif -%}
        </div>
      </div>
    </div>

    {%- if product_aside != blank -%}
      <div class="Product__Aside">
        <span id="ProductAside" class="Anchor"></span>
        {{- product_aside -}}
      </div>
    {%- endif -%}
  </div>
</section>

{%- comment -%}
--------------------------------------------------------------------------------------------------------------------
PHOTO SWIPE

This is the root container for the zoom functionality. Must not be removed, as order element is important.
--------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

{%- if section.settings.enable_image_zoom -%}
  <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
    <!-- Background of PhotoSwipe -->
    <div class="pswp__bg"></div>

    <!-- Slides wrapper with overflow:hidden. -->
    <div class="pswp__scroll-wrap">

      <!-- Container that holds slides. Do not remove as content is dynamically added -->
      <div class="pswp__container">
        <div class="pswp__item"></div>
        <div class="pswp__item"></div>
        <div class="pswp__item"></div>
      </div>

      <!-- Main UI bar -->
      <div class="pswp__ui pswp__ui--hidden">
        <button class="pswp__button pswp__button--prev RoundButton" data-animate-left title="{{ 'product.slideshow.previous' | t }}">{% render 'icon' with 'arrow-left' %}</button>
        <button class="pswp__button pswp__button--close RoundButton RoundButton--large" data-animate-bottom title="{{ 'product.slideshow.close' | t }}">{% render 'icon' with 'close' %}</button>
        <button class="pswp__button pswp__button--next RoundButton" data-animate-right title="{{ 'product.slideshow.next' | t }}">{% render 'icon' with 'arrow-right' %}</button>
      </div>
    </div>
  </div>
{%- endif -%}

<script>
  window.ShopifyXR=window.ShopifyXR||function(){(ShopifyXR.q=ShopifyXR.q||[]).push(arguments)};
  ShopifyXR('addModels', {{ product.media | where: 'media_type', 'model' | json }});
</script>

{% schema %}
{
  "name": "Product page",
  "class": "shopify-section--bordered",
  "blocks": [
    {
      "type": "linked",
      "name": "Linked products",
      "limit": 1
    },
    {
      "type": "countdown",
      "name": "Countdown",
      "limit": 1,
      "settings": [
        {
          "type": "color",
          "id": "countdown_text_color",
          "label": "Text Color",
          "default": "#000"
        },
        {
          "type":"header",
          "content":"Countdowns",
          "info": "If one of the fields is left empty, it will ignore that day and instead countdown to the next day with a set countdown"
        },
        {
          "type": "text",
          "id": "countdown_monday",
          "label": "Monday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        },
        {
          "type": "text",
          "id": "countdown_tuesday",
          "label": "Tuesday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        },
        {
          "type": "text",
          "id": "countdown_wednesday",
          "label": "Wednesday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        },
        {
          "type": "text",
          "id": "countdown_thursday",
          "label": "Thursday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        },
        {
          "type": "text",
          "id": "countdown_friday",
          "label": "Friday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        },
        {
          "type": "text",
          "id": "countdown_saturday",
          "label": "Saturday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        },
        {
          "type": "text",
          "id": "countdown_sunday",
          "label": "Sunday",
          "info": "Only accepts this format; hh:mm",
          "default": "14:00"
        }
      ]
    },
    {
      "type": "product_meta",
      "name": "Product meta",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_vendor",
          "label": "Show vendor",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_sku",
          "label": "Show SKU",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_product_rating",
          "label": "Show product rating",
          "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_taxes_included",
          "label": "Show price taxes notice",
          "default": false
        }
      ]
    },
    {
      "type": "variant_selector",
      "name": "Variant selector",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "dropdown"
        },
        {
          "type": "checkbox",
          "id": "show_color_swatch",
          "label": "Show color swatch",
          "default": false,
          "info": "Some colors appear white? [Learn more](http://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch)."
        },
        {
          "type": "checkbox",
          "id": "show_color_carousel",
          "label": "Show color carousel",
          "info": "A pop-up selector with variant images for choosing colors. Only enables when color swatch is disabled.",
          "default": false
        },
        {
          "type": "page",
          "id": "size_chart",
          "label": "Size chart",
          "info": "Show along option named Size."
        }
      ]
    },
    {
      "type": "share_buttons",
      "name": "Share buttons",
      "limit": 1
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "inventory_quantity_threshold",
          "label": "Inventory quantity threshold",
          "info": "Only show inventory quantity if below threshold. Choose 0 to always show.",
          "min": 0,
          "max": 50,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_price_in_button",
          "label": "Show price in add to cart button",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        }
      ]
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "paragraph",
          "content": "This Liquid code will show on the right part of the product page."
        },
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_label",
          "label": "Show label",
          "default": false
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "paragraph",
          "content": "This text will show on the right part of the product page."
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        }
      ]
    },
    {
      "type": "store_pickup",
      "name": "Local pickup availability",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Show customers where they can pick up the product. [Learn more](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup#show-pickup-availability-to-your-customers)"
        }
      ]
    },
    {
      "type": "@app"
    },
    {
      "name": "Content",
      "type": "content",
      "settings": [
        {
          "type": "paragraph",
          "content": "This content will show on the left part, below the product images."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
        {
          "type": "page",
          "id": "page",
          "label": "Content from page",
          "info": "If specified, takes precedence over inline content."
        }
      ]
    },
    {
      "name": "Reviews",
      "type": "reviews",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To show reviews, install [Shopify Product Reviews](https://apps.shopify.com/product-reviews). This content will show on the left part, below the product images. "
        }
      ]
    },
    {
      "name": "Buy it with",
      "type": "buy_it_with",
      "limit": 1,
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Buy it with"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "Size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fill",
          "label": "Fill screen"
        }
      ],
      "default": "large"
    },
    {
      "type": "checkbox",
      "id": "stack_images",
      "label": "Stack images on desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_thumbnails",
      "label": "Show thumbnails on desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable zoom",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": false
    }
  ]
}
{% endschema %}
