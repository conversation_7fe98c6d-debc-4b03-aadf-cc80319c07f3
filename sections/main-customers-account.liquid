<div class="Container Container--narrow">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when '@app' -%}
        {%- render block -%}

      {%- when 'header' -%}
        <header class="PageHeader" {{ block.shopify_attributes }}>
          <a href="{{ routes.account_logout_url }}" class="PageHeader__Back Heading Text--subdued Link Link--primary u-h7">{{ 'customer.account.logout' | t }}</a>

          <div class="SectionHeader">
            <h1 class="SectionHeader__Heading Heading u-h1">{{ 'customer.account.title' | t }}</h1>
            <p class="SectionHeader__Description">{{ 'customer.account.welcome' | t: first_name: customer.first_name }}</p>
          </div>
        </header>
        <div class="PageLayout PageLayout--breakLap bonus_info">
          {%- comment -%}
          --------------------------------------------------------------------------------------------------------------------
          SMSKlub.dk code for showing bonus
          --------------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}
          <div class="PageLayout__Section">
            <div class="Segment">
              <h2 class="Segment__Title Heading u-h7">Kundeklub bonus</h2>

              <div class="Segment__Content">
                    <p>Du har opsparet: <span class="bonus_amount"></span> Kr.</p>
                    <div class="Segment__ButtonWrapper">
                      <a href="#" class="bonus_link Button Button--primary">Brug din bonus</a>
                    </div>
              </div>
            </div>
          </div>
        </div>
      {%- when 'order_list' -%}
        <div class="PageLayout PageLayout--breakLap">
          {%- comment -%}
          --------------------------------------------------------------------------------------------------------------------
          ORDER HISTORY
          --------------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}

          <div class="PageLayout__Section">
            {%- if customer.orders.size == 0 -%}
              <div class="Segment">
                <h2 class="Segment__Title Heading u-h7">{{ 'customer.account.no_orders_title' | t }}</h2>

                <div class="Segment__Content">
                  <p>{{ 'customer.account.no_orders_content' | t }}</p>
                </div>
              </div>
            {%- else -%}
              {%- paginate customer.orders by 25 -%}
                <div class="TableWrapper">
                  <table class="AccountTable Table Table--large">
                    <thead class="Text--subdued">
                    <tr>
                      <th>{{ 'customer.orders.order_number' | t }}</th>
                      <th>{{ 'customer.orders.date' | t }}</th>
                      <th>{{ 'customer.orders.payment_status' | t }}</th>
                      <th>{{ 'customer.orders.fulfillment_status' | t }}</th>
                      <th class="Text--alignRight">{{ 'customer.orders.total' | t }}</th>
                    </tr>
                    </thead>

                    <tbody class="Heading u-h7">
                    {%- for order in customer.orders -%}
                      <tr>
                        <td><a href="{{ order.customer_url }}" class="Link Link--underline">{{ order.name }}</a></td>
                        <td>{{ order.created_at | date: format: 'basic' }}</td>
                        <td>{{ order.financial_status_label }}</td>
                        <td>{{ order.fulfillment_status_label }}</td>
                        <td class="Text--alignRight">{{ order.total_price | money }}</td>
                      </tr>
                    {%- endfor -%}
                    </tbody>
                  </table>
                </div>

                {%- render 'pagination', paginate: paginate -%}
              {%- endpaginate -%}
            {%- endif -%}
          </div>

          {%- comment -%}
          --------------------------------------------------------------------------------------------------------------------
          ADDRESS
          --------------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}

          <div class="PageSpacingWrapper PageLayout__Section PageLayout__Section--secondary">
            <div class="Segment">
              {%- if customer.addresses_count == 0 -%}
                <h2 class="Segment__Title Heading u-h7">{{ 'customer.account.no_addresses_title' | t }}</h2>

                <div class="Segment__Content">
                  <p>{{ 'customer.account.no_addresses_content' | t }}</p>

                  <div class="Segment__ButtonWrapper">
                    <a href="{{ routes.account_addresses_url }}" class="Button Button--primary">{{ 'customer.account.manage_addresses' | t }}</a>
                  </div>
                </div>
              {%- else -%}
                <h2 class="Segment__Title Heading u-h7">{{ 'customer.account.default_address' | t }}</h2>

                <div class="Segment__Content">
                  {{ customer.default_address | format_address | replace: '<p>', '<p class="AccountAddress"><span>' | replace_first: '<br>', '</span><br>' }}

                  <div class="Segment__ButtonWrapper">
                    <a href="{{ routes.account_addresses_url }}" class="Button Button--primary">{{ 'customer.account.edit_addresses' | t }}</a>
                  </div>
                </div>
              {%- endif -%}
            </div>
          </div>
        </div>
    {%- endcase -%}
  {%- endfor -%}
</div>

{% schema %}
{
  "name": "Customer account",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "header",
      "name": "Welcome info",
      "limit": 1
    },
    {
      "type": "order_list",
      "name": "Order list",
      "limit": 1
    }
  ]
}
{% endschema %}