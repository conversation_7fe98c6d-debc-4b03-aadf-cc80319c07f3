{%- assign s = section.settings -%}
{%- assign id = all_products[s.product_handle].selected_or_first_available_variant.id -%}
{%- assign title = i.product.title -%}
{%- assign productId = i.product.id -%}
{%- assign props = i.properties | join -%}

<div data-component="cartWrappingCheckbox" data-index="{{ forloop.index }}" data-wrapper-id="{{ id }}" data-product-title="{{ title }}" data-product-id="{{ productId }}">
    <label class="cart-page__wrapping flex align-center" data-label for="product-wrapping-{{ forloop.index0 }}">
        <span class="cart-page__wrapping--checkbox{% if props contains '_wrap_link' %} checked{% endif %}" data-checkbox></span>
        {{ s.wrapping_title }}
    </label>
    <input data-input id="product-wrapping-{{ forloop.index0 }}" name="wrapping" type="checkbox">
</div>