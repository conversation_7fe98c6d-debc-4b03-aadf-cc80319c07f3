{%- assign for_drawer = false -%}

{%- if section.id == 'mini-cart' -%}
  {%- assign for_drawer = true -%}
{%- endif -%}

<div class="Cart__ItemList">
  {%- unless for_drawer -%}
    <div class="Cart__Head hidden-phone">
      <span class="Cart__HeadItem Heading Text--subdued u-h7">{{ 'cart.items.product' | t }}</span>
      <span class="Cart__HeadItem"></span>
      <span class="Cart__HeadItem Heading Text--subdued u-h7" style="text-align: center">{{ 'cart.items.quantity' | t }}</span>
      <span class="Cart__HeadItem Heading Text--subdued u-h7" style="text-align: right">{{ 'cart.items.total' | t }}</span>
    </div>
  {%- endunless -%}

  {%- for line_item in cart.items -%}
    {%- if for_drawer -%}
      <div class="CartItemWrapper">
    {%- endif -%}

    <div class="CartItem">
      <div class="CartItem__ImageWrapper AspectRatio">
        <div class="AspectRatio" style="--aspect-ratio: {{ line_item.image.aspect_ratio }}">
          <img class="CartItem__Image" src="{{ line_item.image | img_url: '240x' }}" alt="{{ line_item.image.alt | escape }}">
        </div>
      </div>

      <div class="CartItem__Info">
        <h2 class="CartItem__Title Heading">
          <a href="{{ line_item.url }}">{{ line_item.product.title }}</a>
        </h2>

        <div class="CartItem__Meta Heading Text--subdued">
          {%- unless line_item.product.has_only_default_variant -%}
            <p class="CartItem__Variant">{{ line_item.variant.title }}</p>
          {%- endunless -%}

          {%- if line_item.selling_plan_allocation -%}
            <p class="CartItem__PlanAllocation">{{ line_item.selling_plan_allocation.selling_plan.name }}</p>
          {%- endif -%}

          {%- if line_item.properties != empty -%}
            <ul class="CartItem__PropertyList">
              {%- for property in line_item.properties -%}
                {%- assign first_character_in_key = property.first | truncate: 1, '' -%}

                {%- if property.last == blank or first_character_in_key == '_' -%}
                  {%- continue -%}
                {%- endif -%}

                <li class="CartItem__Property">{{ property.first }}: {{ property.last }}</li>
              {%- endfor -%}
            </ul>
          {%- endif -%}

          <div class="CartItem__PriceList">
            {%- if line_item.original_price != line_item.final_price -%}
              <span class="CartItem__Price Price Price--highlight">{{ line_item.final_price | money }}</span>
              <span class="CartItem__OriginalPrice Price Price--compareAt">{{ line_item.original_price | money }}</span>
            {%- else -%}
              <span class="CartItem__Price Price">{{ line_item.final_price | money }}</span>
            {%- endif -%}

            {%- if line_item.line_level_discount_allocations != blank -%}
              <ul class="CartItem__DiscountList">
                {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                  <li class="CartItem__Discount">
                    {%- render 'icon' with 'sale' -%}{{ discount_allocation.discount_application.title }}: -{{ discount_allocation.amount | money }}
                  </li>
                {%- endfor -%}
              </ul>
            {%- endif -%}
          </div>

          {%- if line_item.unit_price_measurement -%}
            <div class="CartItem__UnitPriceMeasurement">
              <div class="UnitPriceMeasurement Heading Text--subdued">
                <span class="UnitPriceMeasurement__Price">{{ line_item.unit_price | money }}</span>
                <span class="UnitPriceMeasurement__Separator">/ </span>

                {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                  <span class="UnitPriceMeasurement__ReferenceValue">{{ line_item.unit_price_measurement.reference_value }}</span>
                {%- endif -%}

                <span class="UnitPriceMeasurement__ReferenceUnit">{{ line_item.unit_price_measurement.reference_unit }}</span>
              </div>
            </div>
          {%- endif -%}
        </div>

        {%- capture item_actions -%}
          {% liquid
            assign hasGiftWrap = false
            if line_item.product.id == 6815240487027
              assign hasGiftWrap = true
            endif
          -%}

          <div class="CartItem__Actions Heading Text--subdued" style="text-align: center;{% if hasGiftWrap %} display: none;{% endif %}">
            <div class="CartItem__QuantitySelector">
              <div class="QuantitySelector">
                {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

                <a data-key="{{ line_item.id }}" class="QuantitySelector__Button Link Link--primary" title="{{ 'cart.items.set_quantity' | t: new_quantity: quantity_minus_one }}" href="{{ routes.cart_change_url }}?quantity={{ quantity_minus_one }}&line={{ forloop.index }}" data-quantity="{{ quantity_minus_one }}" data-line="{{ forloop.index }}" data-action="update-item-quantity">
                  {%- render 'icon' with 'minus' -%}
                </a>

                <input data-key="{{ line_item.id }}" type="text" name="updates[]" id="updates_{{ line_item.key }}" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" data-line="{{ forloop.index }}" value="{{ line_item.quantity }}">

                {%- assign quantity_plus_one = line_item.quantity | plus: 1 -%}

                <a data-key="{{ line_item.id }}" class="QuantitySelector__Button Link Link--primary" title="{{ 'cart.items.set_quantity' | t: new_quantity: quantity_plus_one }}" href="{{ routes.cart_change_url }}?quantity={{ quantity_plus_one }}&line={{ forloop.index }}" data-quantity="{{ quantity_plus_one }}" data-line="{{ forloop.index }}" data-action="update-item-quantity">
                  {%- render 'icon' with 'plus' -%}
                </a>
              </div>
            </div>

            <a href="{{ routes.cart_change_url }}?quantity=0&line={{ forloop.index }}" class="CartItem__Remove Link Link--underline Link--underlineShort" data-quantity="0" data-line="{{ forloop.index }}" data-action="remove-item">{{ 'cart.items.remove' | t }}</a>
          </div>
        {%- endcapture -%}

        {{ item_actions }}
      </div>

      {%- unless for_drawer -%}
        {{ item_actions }}

        <div class="CartItem__LinePriceList Heading Text--subdued" style="text-align: right">
          {%- if line_item.original_line_price != line_item.final_line_price -%}
            <span class="CartItem__Price Price Price--highlight">{{ line_item.final_line_price | money }}</span>
            <span class="CartItem__Price Price Price--compareAt">{{ line_item.original_line_price | money }}</span>
          {%- else -%}
            <span class="CartItem__Price Price">{{ line_item.final_line_price | money }}</span>
          {%- endif -%}
        </div>
      {%- endunless -%}
    </div>

    {%- if for_drawer -%}
      </div>
    {%- endif -%}
  {%- endfor -%}
</div>
