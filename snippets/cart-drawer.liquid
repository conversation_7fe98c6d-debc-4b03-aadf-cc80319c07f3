{%- capture section_settings -%}
{
  "type": {{ settings.cart_type | json }},
  "itemCount": {{ cart.item_count }},
  "drawer": true,
  "hasShippingEstimator": false
}
{%- endcapture -%}

<div id="sidebar-cart" class="Drawer Drawer--fromRight" aria-hidden="true" data-section-id="cart" data-section-type="cart" data-section-settings='{{ section_settings }}'>
  <div class="sidebar-cart-inner">
    {%- comment -%} Recommended {%- endcomment -%}
    <div class="cart-drawer__recommended" data-rebuy-id="32177"></div>

    <form class="Cart Drawer__Content sidebar-cart-transition" action="{{ routes.cart_url }}" method="POST" novalidate onsubmit="validateTerms(event);">
      <div class="Drawer__Header Drawer__Header--bordered Drawer__Container">
        <span class="Drawer__Title Heading u-h4">{{ 'cart.general.title' | t }}</span>

        <button class="Drawer__Close Icon-Wrapper--clickable" data-action="close-drawer" data-drawer-id="sidebar-cart" aria-label="{{ 'header.navigation.close_cart' | t }}">
          {%- render 'icon' with 'close' -%}
        </button>
      </div>

      <div class="Drawer__Main" data-scrollable>
        {%- if settings.cart_show_free_shipping_threshold -%}
          {%- assign threshold_in_cents = settings.cart_free_shipping_threshold | times: 100 -%}

          <div class="Cart__ShippingNotice Text--subdued">
            <div class="Drawer__Container">
              {%- if cart.total_price >= threshold_in_cents -%}
                <p>{{- 'cart.general.free_shipping' | t -}}</p>
              {%- else -%}
                {%- capture remaining_amount -%}<span>{{ cart.total_price | minus: threshold_in_cents | abs | money }}</span>{%- endcapture -%}
                <p>{{- 'cart.general.free_shipping_remaining_html' | t: remaining_amount: remaining_amount -}}</p>
              {%- endif -%}
            </div>
          </div>
        {%- endif -%}

        {%- if cart.item_count == 0 -%}
          <p class="Cart__Empty Heading u-h5">{{ 'cart.general.empty' | t }}</p>
        {%- else -%}
          <div class="Drawer__Container">
            <input type="hidden" name="attributes[collection_mobile_items_per_row]" value="">
            <input type="hidden" name="attributes[collection_desktop_items_per_row]" value="">

            {% render 'cart-items' %}
          </div>
        {%- endif -%}
      </div>

      {%- unless cart.item_count == 0 -%}
        <div class="Drawer__Footer" data-drawer-animated-bottom>
          {%- capture shipping_and_taxes_notice -%}{{ 'cart.general.shipping_and_taxes_notice' | t }}{%- endcapture -%}

          {%- if settings.cart_enable_notes or shipping_and_taxes_notice != blank -%}
            {%- if settings.cart_enable_notes -%}
              {%- if cart.note == blank -%}
                <button type="button" class="Cart__NoteButton" data-action="toggle-cart-note">{{ 'cart.general.add_note' | t }}</button>
              {%- else -%}
                <button type="button" class="Cart__NoteButton" data-action="toggle-cart-note">{{ 'cart.general.edit_note' | t }}</button>
              {%- endif -%}
            {%- endif -%}

            {%- if shipping_and_taxes_notice != blank -%}
              <p class="Cart__Taxes Text--subdued">{{ shipping_and_taxes_notice }}</p>
            {%- endif -%}

            {% render 'gift-wrapping' %}

            {%- if cart.cart_level_discount_applications != blank -%}
              {%- for discount_application in cart.cart_level_discount_applications -%}
                <p class="Cart__Discount">{%- render 'icon' with 'sale' -%} {{ 'cart.general.discount' | t }} ({{ discount_application.title }}): -<span>{{ discount_application.total_allocated_amount | money }}</span></p>
              {%- endfor -%}
            {%- endif -%}
          {%- endif -%}

          {% if settings.enable_tc_checkbox %}
            <div class="accept-terms">
              <input type="checkbox" id="agree" name="checkbox" />
              <label for="agree">
                {% capture terms %}<a class="accept-terms--link" target="_blank" href="{{settings.tc_link}}">{{ 'cart.terms.terms_link_content' | t }}</a>{% endcapture %}
                <p>{{ 'cart.terms.terms_link' | t | replace: "[terms]", terms}}</p>
              </label>
            </div>
          {% endif %}

          <button type="submit" name="checkout" class="Cart__Checkout Button Button--primary Button--full">
            <span>{{- 'cart.general.checkout' | t -}}</span>
            <span class="Button__SeparatorDot"></span>
            <span>{{ cart.total_price | money_with_currency }}</span>
          </button>

          {%- if settings.cart_show_page_link -%}
            <a href="{{routes.cart_url}}" class="Cart__Checkout Button Button--primary Button--full">
              <span>{{- 'cart.general.go_to_cart' | t -}}</span>
            </a>
          {%- endif -%}

          {%- if settings.cart_enable_notes -%}
            <div class="Cart__OffscreenNoteContainer" aria-hidden="true">
              {%- if cart.note == blank -%}
                <span class="Cart__NoteButton">{{ 'cart.general.add_note' | t }}</span>
              {%- else -%}
                <span class="Cart__NoteButton">{{ 'cart.general.edit_note' | t }}</span>
              {%- endif -%}

              <div class="Form__Item">
                <textarea class="Cart__Note Form__Textarea" name="note" id="cart-note" rows="3" placeholder="{{ 'cart.general.note_placeholder' | t }}" data-scrollable>{{ cart.note }}</textarea>
              </div>

              <button type="button" class="Button Button--primary Button--full" data-action="toggle-cart-note">{{ 'cart.general.save_note' | t }}</button>
            </div>
                        <div class="bonus_info">
          {%- comment -%}
          --------------------------------------------------------------------------------------------------------------------
          SMSKlub.dk code for showing bonus
          --------------------------------------------------------------------------------------------------------------------
          {%- endcomment -%}
          <p>
                      <a href="#" class="bonus_link Button Button--secondary Button--full">Brug din bonus<span class="Button__SeparatorDot"></span><span class="bonus_amount"></span> Kr.</a>
          </p>
          </div>
          {%- endif -%}
        </div>
      {%- endunless -%}
    </form>
  </div>
</div>
