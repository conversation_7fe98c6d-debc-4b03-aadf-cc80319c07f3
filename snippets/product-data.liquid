{% comment %}
------------------------------------------------------------------------------
Product Data. This must be outputted for all products (including home page).

IMPORTANT: THIS CODE IS VITAL. DO NOT EDIT IT NOT REMOVE IT. MAKE SURE TO KEEP
THE EXACT SAME ATTRIBUTES.
------------------------------------------------------------------------------
{% endcomment %}

{%- assign inventory_block = section.blocks | where: 'type', 'inventory' | first -%}

<script type="application/json" data-product-json>
  {
    "product": {{ product | json }},
    "selected_variant_id": {{ product.selected_or_first_available_variant.id }}
    {%- if inventory_block != blank -%}
      ,"inventories": {
        {%- for variant in product.variants -%}
          {%- assign inventory_message = '' -%}

          {%- if inventory_block.settings.inventory_quantity_threshold == 0 -%}
            {%- capture inventory_message -%}{{- 'product.form.inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- else -%}
            {%- capture inventory_message -%}{{- 'product.form.low_inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- endif -%}

          "{{ variant.id }}": {
            "inventory_management": {{ variant.inventory_management | json }},
            "inventory_policy": {{ variant.inventory_policy | json }},
            "inventory_quantity": {{ variant.inventory_quantity | json }},
            "inventory_message": {{ inventory_message | json }}
          }{% unless forloop.last %},{% endunless %}
        {%- endfor -%}
      }
    {%- endif -%}
  }
</script>