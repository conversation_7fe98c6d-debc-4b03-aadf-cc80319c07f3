<link rel="stylesheet" href="{{ 'app_wishlist-king_templates.css' | asset_url }}" />

{% assign show_icons = true %}

<style>
  .wk-link {
    --wk-icon--stroke-width: 2px;
  }

  .wk-link__icon {
    margin-right: 0;
    display: none;
    top: 1px;
  }

  .wk-link__label {
    text-transform: uppercase;
  }

  .wk-link__count {
    padding-left: 5px;
  }

  .wk-link__count::before {
    content: "("
  }

  .wk-link__count::after {
    content: ")"
  }

  .Header--withIcons .wk-link {
    margin-left: 12px;
    margin-right: 18px;
  }

  .Header--withIcons .wk-link::before {
    position: absolute;
    content: "";
    top: -8px;
    right: -12px;
    left: -12px;
    bottom: -8px;
    transform: translateZ(0);
  }

  @media screen and (max-width: 600px) {
    .Header--withIcons .wk-link {
      transform: translateY(1px);
  	  --wk-icon--stroke-width: 0.9px;
      --wk-link__icon--size: 18px;
    }
  }

  @media screen and (min-width: 641px) {
    .Header--withIcons .wk-link {
      margin-left: 25px;
      margin-right: 25px;
    }
  }

  .Header--withIcons .wk-link__icon {
    display: block;
  }

  .Header--withIcons .wk-link__label {
    display: none;
  }

  .Header--withIcons .wk-link__count {
    display: none;
  }

  .SidebarMenu__Nav--secondary .wk-link {
    transition: color .2s ease-in-out, opacity .2s ease-in-out;
    color: var(--navigation-text-color-light);
  }

  .SidebarMenu__Nav--secondary .wk-link:hover {
    color: var(--navigation-text-color);
  }

  .SidebarMenu__Nav--secondary .wk-link__label {
    text-transform: none;
  }

  .hidden-desk .wk-link {
    padding: 10px;
    /* color: var(--button-background); */
  }

  .hidden-desk .wk-link__icon {
    display: inline-flex;
    top: 1px;
  }

  .hidden-desk .wk-link__label,
  .hidden-desk .wk-link__count {
    display: none;
  }

  .Header--transparent .wk-icon__svg {
    filter: drop-shadow(0 1px rgba(0, 0, 0, 0.25));
  }

  .wk-button {
    margin-top: 20px;
    {%- if section.settings.show_payment_button and product.template_suffix != 'pre-order' -%}
    margin-top: 0px;
    {%- endif -%}

    text-transform: uppercase;
    font-size: calc({{settings.base_text_font_size}}px - 2px);
    text-align: center;
    letter-spacing: 0.2em;
    font-family: {{settings.heading_font.family}}, {{ settings.heading_font.fallback_families }};
    font-weight: {{ settings.heading_font.weight }};
    font-style: {{ settings.heading_font.style }};
  }

  .wk-button__icon {
    top: -1px;
  }

  .wk-button__label {}

  .wk-button.wk-button--floating {}

  .wk-product-form__submit {
    color: {{ settings.button_text_color }};
    background: {{ settings.button_background }};
    padding: 14px 5px;
    line-height: normal;
    border: 1px solid transparent;
    border-radius: 0;
    text-transform: uppercase;
    font-size: calc({{settings.base_text_font_size}}px - 2px);
    text-align: center;
    letter-spacing: 0.2em;
    font-family: {{settings.heading_font.family}}, {{ settings.heading_font.fallback_families }};
    font-weight: {{ settings.heading_font.weight }};
    font-style: {{ settings.heading_font.style }};
  }

  @media (max-width: 850px) {
    .wk-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 600px) {
    .wk-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 320px) {
    .wk-grid {
      grid-template-columns: repeat(1, 1fr);
    }
  }

  .ProductItem[data-product-id] {
    position: relative;
  }

  :root {
    --wk-icon--stroke-width: 1.5px;

    --wk-link--color: currentColor;
    --wk-link__icon--size: 21px;

    --wk-button--color: currentColor;
    --wk-button__icon--color: var(--button-background);
    --wk-button__icon--size: 16px;

    --wk-product-form__submit--background: white;
    --wk-product-form__submit--color: currentColor;

    --wk-share-button--color: white;
    --wk-share-button--background: #3d3d3d;
    --wk-share-button--size: 26px;

    --wk-page__image--aspect-ratio: 1 / 1;
    --wk-page__grid--max-columns: 4;
  }
</style>

<script type="module">
  import WishlistKing from "{{ 'app_wishlist-king_sdk.js' | asset_url }}";
  import templates from "{{ 'app_wishlist-king_templates.js' | asset_url }}";
  import settings from "{{ 'app_wishlist-king_settings.js' | asset_url }}";

  // Uncomment this line to use WishlistKing in third party scripts
  // window.WishlistKing = WishlistKing;

  WishlistKing.toolkit.init({
    templates,
    settings,
    events: {
      ready() {
        // App is ready and WishlistKing.toolkit can be used
      },
      addedToCart(product) {
        // This is a good place to trigger AJAX cart update
        window.scrollTo(0, 0);
        location.reload();
      },
      loginRequired(intent) {
        window.location = [
          "/account/login?wk-redirect=",
          encodeURIComponent(
            JSON.stringify({
              path: window.location.pathname,
              intent: intent,
            })
          ),
        ].join();
      },
    },
    shop: {
      name: {{ shop.name | replace: "&amp;", "&" | json }},
      domain: "{{ shop.domain }}",
      permanent_domain: "{{ shop.permanent_domain }}",
      currency: "{{ shop.currency }}",
      money_format: {{ shop.money_format | json }},
      root_url: "{% unless routes.root_url == '/'  %}{{ routes.root_url }}{% endunless %}",
    },
    session: {
      customer: {{ customer.id | json }},
      customer_email: {{ customer.email | json }},
    },
    wishlist: {
      filter: function (product) {
        return !product.hidden;
      },
      share: {
        title: {{ "app_wishlist-king.general.share_list_title" | t | json }},
        description: {{ "app_wishlist-king.general.share_list_description" | t: shopName: shop.name | json }},
      },
    },
    theme: {
      locale: {
        wishlist: {{ "app_wishlist-king.general.wishlist" | t | json }},
        wishlist_empty_note: {{ "app_wishlist-king.general.wishlist_empty_note" | t | json }},
        add_to_wishlist: {{ "app_wishlist-king.general.add_to_wishlist" | t | json }},
        remove_from_wishlist: {{ "app_wishlist-king.general.remove_from_wishlist" | t | json }},
        clear_wishlist: {{ "app_wishlist-king.general.clear_wishlist" | t | json }},
        in_wishlist: {{ "app_wishlist-king.general.in_wishlist" | t | json }},
        share_wishlist: {{ "app_wishlist-king.general.share_wishlist" | t | json }},
        copy_share_link: {{ "app_wishlist-king.general.copy_share_link" | t | json }},
        share_link_copied: {{ "app_wishlist-king.general.share_link_copied" | t | json }},
        share_by_email_body: {{ "app_wishlist-king.general.share_by_email_body" | t | json }},
        login_or_signup_note: {{ "app_wishlist-king.general.login_or_signup_note_html" | t: login_url: routes.account_login_url, register_url: routes.account_register_url | json }},
        add_to_cart: {{ "app_wishlist-king.general.add_to_cart" | t | json }},
        sold_out: {{ "app_wishlist-king.general.sold_out" | t | json }},
        quantity: {{ "app_wishlist-king.general.quantity" | t | json }},
        view_product: {{ "app_wishlist-king.general.view_product" | t | json }},
        view_wishlist: {{ "app_wishlist-king.general.view_wishlist" | t | json }},
        share_on_facebook: {{ "app_wishlist-king.general.share_on_facebook" | t | json }},
        share_on_twitter: {{ "app_wishlist-king.general.share_on_twitter" | t | json }},
        share_on_pinterest: {{ "app_wishlist-king.general.share_on_pinterest" | t | json }},
        share_with_whatsapp: {{ "app_wishlist-king.general.share_with_whatsapp" | t | json }},
        share_by_email: {{ "app_wishlist-king.general.share_by_email" | t | json }},
        get_link: {{ "app_wishlist-king.general.get_link" | t | json }},
        send_to_customer_service: {{ "app_wishlist-king.general.send_to_customer_service" | t | json }},
      },
      customer: {{ customer.id | json }},
      customer_accounts_enabled: {{ shop.customer_accounts_enabled | json }},
    },
  });

  {% if show_icons %}
  WishlistKing.observe(
    {
      selector: ".Header__Icon[href='{{ routes.cart_url }}']",
    },
    (target) => {
      target.insertBefore(
        WishlistKing.createComponent("wishlist-link")
      );
    }
  );
  {% else %}
  // Header link
  WishlistKing.observe(
    {
      selector: ".Header__SecondaryNav > .HorizontalList > .HorizontalList__Item:last-of-type",
    },
    (target) => {
      target.insertBefore(
        WishlistKing.createElement("li", {className: "HorizontalList__Item Heading Link Link--primary Text--subdued u-h8"},
          WishlistKing.createComponent("wishlist-link")
        )
      );
    }
  );
  {% endif %}

  // Header link mobile
  /* WishlistKing.observe(
    {
      selector: ".Header__Icon.hidden-desk[href='{{ routes.cart_url }}']",
    },
    (target) => {
      target.insertBefore(
        WishlistKing.createElement("span", {className: "hidden-desk"},
          WishlistKing.createComponent("wishlist-link")
        )
      );
    }
  ); */

  // Link in drawer
  WishlistKing.observe(
    {
      selector: ".SidebarMenu__Nav--secondary > .Linklist",
    },
    (target) => {
      target.append(
        WishlistKing.createElement("li", {className: "Linklist__Item"},
          WishlistKing.createComponent("wishlist-link")
        )
      );
    }
  );

  // Wishlist button on collection page
  WishlistKing.observe(
    {
      selector: ".ProductItem[data-product-id]",
    },
    (target) => {
      target.append(
        WishlistKing.createComponent("wishlist-button-floating", {
          id: target.container.getAttribute("data-product-id"),
        })
      );
    }
  );

  // Wishlist page
  WishlistKing.observe(
    {
      selector: ".PageContent",
      template: "page",
      handle: WishlistKing.toolkit.settings.wishlistPageHandle,
    },
    (target) => {
      target.insertBefore(WishlistKing.createComponent("wishlist-page"));
    }
  );

  // Shared wishlist page
  WishlistKing.observe(
    {
      selector: ".PageContent",
      template: "page",
      handle: WishlistKing.toolkit.settings.sharedWishlistPageHandle,
    },
    (target) => {
      target.insertBefore(WishlistKing.createComponent("wishlist-page-shared"));
    }
  );
</script>
