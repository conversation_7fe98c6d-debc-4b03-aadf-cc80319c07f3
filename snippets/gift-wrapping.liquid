{% assign id = linklists.gift-wrapping.links.first.object.variants.first.id %}
{% assign gift_wraps_in_cart = 0 %}
  {% for item in cart.items %}
    {% if item.id == id %}
      {% assign gift_wraps_in_cart = item.quantity %}
    {% endif %}
  {% endfor %}

{% assign items_in_cart = cart.item_count | minus: gift_wraps_in_cart %}
{% if linklists.gift-wrapping.links.size > 0 and linklists.gift-wrapping.links.first.type == 'product_link' %}

<div id="is-a-gift" class="giftwrap" data-id="{{ id }}">
    <input id="gift-wrapping" type="checkbox" name="attributes[gift-wrapping]" value="yes" {% if gift_wraps_in_cart > 0 %}checked{% endif %} />
    <label for="gift-wrapping">
      {{ 'cart.gift.gift_wrapping' | t }}
    </label>
</div>
{% endif %}
