{%- comment -%}
This snippet structures the micro-data using JSON-LD specification. Please note that for Product especially,
the schema often changes. We try to output as much info as possible, but Google may add new requirements over time,
or change the format of some info
{%- endcomment -%}

{%- if request.page_type == 'product' -%}
  {%- assign days_product_price_valid_until = 10 | times: 86400 -%}

  {%- assign gtin_option = 'gtin' -%}
  {%- if product.selected_or_first_available_variant.barcode != blank -%}
    {%- assign is_barcode_available = true -%}
    {%- assign gtin_string_length = product.selected_or_first_available_variant.barcode | size -%}

    {%- if gtin_string_length == 8 or gtin_string_length == 12 or gtin_string_length == 13 or gtin_string_length == 14 -%}
      {%- assign is_valid_gtin_length = true -%}
      {%- assign gtin_option = gtin_option | append: gtin_string_length -%}
    {%- endif -%}
  {%- endif -%}

  {%- capture main_entity_microdata -%}
    "@type": "Product",
    "offers": [
      {%- for variant in product.variants -%}
        {
          "@type": "Offer",
          "name": {{ variant.title | json }},
          "availability": {%- if variant.available -%}"https://schema.org/InStock"{%- else -%}"https://schema.org/OutOfStock"{%- endif -%},
          "price": {{ variant.price | divided_by: 100.0 | json }},
          "priceCurrency": {{ cart.currency.iso_code | json }},
          "priceValidUntil": "{{ 'now' | date: '%s' | plus: days_product_price_valid_until | date: '%Y-%m-%d'}}",
          {%- if variant.sku != blank -%}
            "sku": {{ variant.sku | json }},
          {%- endif -%}
          "url": "{{ product.url }}?variant={{ variant.id }}"
        }{% unless forloop.last %},{% endunless %}
      {%- endfor -%}
    ],
    {%- if product.metafields.reviews.rating.value != blank -%}
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "{{ product.metafields.reviews.rating.value }}",
        "reviewCount": "{{ product.metafields.reviews.rating_count.value }}",
        "worstRating": "{{ product.metafields.reviews.rating.value.scale_min }}",
        "bestRating": "{{ product.metafields.reviews.rating.value.scale_max }}"
      },
    {%- endif -%}
    {%- if is_barcode_available and is_valid_gtin_length %}
      "{{gtin_option}}": {{ product.selected_or_first_available_variant.barcode | json }},
      "productId": {{ product.selected_or_first_available_variant.barcode | json }},
    {%- elsif is_barcode_available %}
      "mpn": {{ product.selected_or_first_available_variant.barcode | json }},
      "productId": {{ product.selected_or_first_available_variant.barcode | json }},
    {%- endif %}
    "brand": {
      "name": {{ product.vendor | json }}
    },
    "name": {{ product.title | json }},
    "description": {{ product.description | strip_html | json }},
    "category": {{ product.type | json }},
    "url": "{{ product.url }}",
    "sku": {{ product.selected_or_first_available_variant.sku | json }},
    "image": {
      "@type": "ImageObject",
      "url": "https:{{ product.featured_media | img_url: '1024x' }}",
      "image": "https:{{ product.featured_media | img_url: '1024x' }}",
      "name": {{ product.featured_media.alt | json }},
      "width": "1024",
      "height": "1024"
    }
  {%- endcapture -%}
{%- elsif request.page_type == 'article' -%}
  {%- capture main_entity_microdata -%}
    "@type": "BlogPosting",
    "mainEntityOfPage": "{{ article.url }}",
    "articleSection": {{ blog.title | json }},
    "keywords": "{{ article.tags | join: ', ' }}",
    "headline": {{ article.title | json }},
    "description": {{ article.excerpt_or_content | strip_html | truncatewords: 25 | json }},
    "dateCreated": "{{ article.created_at | date: '%Y-%m-%dT%T' }}",
    "datePublished": "{{ article.published_at | date: '%Y-%m-%dT%T' }}",
    "dateModified": "{{ article.published_at | date: '%Y-%m-%dT%T' }}",
    "image": {
      "@type": "ImageObject",
      "url": "https:{{ article.image | img_url: '1024x' }}",
      "image": "https:{{ article.image | img_url: '1024x' }}",
      "name": {{ article.image.alt | json }},
      "width": "1024",
      "height": "1024"
    },
    "author": {
      "@type": "Person",
      "name": "{{ article.user.first_name | escape }} {{ article.user.last_name | escape }}",
      "givenName": {{ article.user.first_name | json }},
      "familyName": {{ article.user.last_name | json }}
    },
    "publisher": {
      "@type": "Organization",
      "name": {{ shop.name | json }}
    },
    "commentCount": {{ article.comments_count }},
    "comment": [
      {%- for comment in article.comments limit: 5 -%}
        {
          "@type": "Comment",
          "author": {{ comment.author | json }},
          "datePublished": "{{ comment.created_at | date: '%Y-%m-%dT%T' }}",
          "text": {{ comment.content | json }}
        }{%- unless forloop.last -%},{%- endunless -%}
      {%- endfor -%}
    ]
  {%- endcapture -%}
{%- endif -%}

{%- capture breadcrumb_entity_microdata -%}
  "@type": "BreadcrumbList",
  "itemListElement": [{
      "@type": "ListItem",
      "position": 1,
      "name": {{ 'general.breadcrumb.home' | t | json }},
      "item": "{{ shop.url }}"
    }

    {%- if request.page_type == 'product' -%}
      {%- if collection -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ collection.title | json }},
          "item": "{{ shop.url }}{{ collection.url }}"
        }, {
          "@type": "ListItem",
          "position": 3,
          "name": {{ product.title | json }},
          "item": "{{ shop.url }}{{ product.url }}"
        }
      {%- else -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ product.title | json }},
          "item": "{{ shop.url }}{{ product.url }}"
        }
      {%- endif -%}
    {%- elsif request.page_type == 'collection' -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ collection.title | json }},
          "item": "{{ shop.url }}{{ collection.url }}"
        }
    {%- elsif request.page_type == 'blog' -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }},
          "item": "{{ shop.url }}{{ blog.url }}"
        }
    {%- elsif request.page_type == 'article' -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }},
          "item": "{{ shop.url }}{{ blog.url }}"
        }, {
          "@type": "ListItem",
          "position": 3,
          "name": {{ blog.title | json }},
          "item": "{{ shop.url }}{{ article.url }}"
        }
    {%- elsif request.page_type == 'page' -%}
       ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ page.title | json }},
          "item": "{{ shop.url }}{{ page.url }}"
        }
    {%- endif -%}
  ]
{%- endcapture -%}

{% if main_entity_microdata != blank %}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    {{ main_entity_microdata }}
  }
  </script>
{% endif %}

{% if breadcrumb_entity_microdata != blank %}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    {{ breadcrumb_entity_microdata }}
  }
  </script>
{% endif %}