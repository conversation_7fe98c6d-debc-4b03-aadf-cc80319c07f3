{%- assign block = section.blocks | where: 'type', 'countdown' | first -%}
{%- if block != blank -%}
    {%- capture timer -%}
        <div data-days></div>
        <div data-hours>t</div>
        <div data-minutes>m</div>
        <div data-seconds>s</div>
    {%- endcapture -%}

      <script type="application/javascript">
          window.countdown = {
              days: {{ 'countdown.timer_text.days' | t | json }},
              hours: {{ 'countdown.timer_text.hours' | t | json }},
              minutes: {{ 'countdown.timer_text.minutes' | t | json }},
              seconds: {{ 'countdown.timer_text.seconds' | t | json }},
              same_day_html: {{- 'countdown.same_day_html' | t: timer: timer | json -}},
              next_day_html: {{- 'countdown.next_day_html' | t: timer: timer | json -}},
              times: [
                  "{{ block.settings.countdown_sunday }}",
                  "{{ block.settings.countdown_monday }}",
                  "{{ block.settings.countdown_tuesday }}",
                  "{{ block.settings.countdown_wednesday }}",
                  "{{ block.settings.countdown_thursday }}",
                  "{{ block.settings.countdown_friday }}",
                  "{{ block.settings.countdown_saturday }}"
              ]
          }
      </script>
      <div data-countdown{% if block.settings.countdown_text_color != blank %} style="color: {{ block.settings.countdown_text_color }};"{% endif %}>
          {{ 'countdown.same_day_html' | t: timer: timer }}
      </div>
    <style>
        [data-countdown] {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            font-size: 13px;
        }

        [data-countdown] > div {
            margin: 0 2px;
            display: inline-block;
            font-weight: bold;
        }

        [data-countdown] [data-seconds] {
            margin-right: 3px;
        }
    </style>
{%- endif -%}
