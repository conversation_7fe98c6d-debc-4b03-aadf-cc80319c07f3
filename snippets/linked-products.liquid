{%- assign grouptag = product.tags | where: 'linked_' | first -%}
{%- assign current_handle = product.handle -%}
{%- if grouptag != blank -%}
    <span class="ProductForm__Label">
        {{- 'product.labels.color' | t -}}
    </span>

    <div class="linkedProducts__wrapper">
        {%- assign colHandle = grouptag | remove: 'linked_' | prepend: "linked-" -%}

        {%- for product in collections[colHandle].products -%}
            <div class="linkedProducts-product{% if product.handle == current_handle %} active{% endif %}">
                <a class="linkedProducts-product__wrapper" href="{{product.url}}">
                    <div class="linkedProducts-image__wrapper">
                        {%- capture supported_sizes -%}{%- render 'image-size', sizes: '200,400,600,700,800,900,1000,1200', image: product.featured_image -%}{%- endcapture -%}
                        {%- assign image_url = product.featured_image | img_url: '50x50'  -%}
                        <img class="Image--lazyLoad"
                             src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                             data-src="{{ image_url }}"
                             alt="{{ product.featured_image.alt | escape }}">
                    </div>
                </a>
            </div>
            {% if forloop.index == 5 and collections[colHandle].products_count > 5 %}
                <div id="linked_show_more" class="linkedProducts__breaker">
                    +{{ collections[colHandle].products_count | minus: 5 }}
                </div>
            {% endif %}
        {%- endfor -%}
    </div>
{%- endif -%}
