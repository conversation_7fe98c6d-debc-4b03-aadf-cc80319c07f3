{%- assign social_media_count = 0 -%}

{%- capture social_media -%}
  {%- if settings.social_facebook != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_facebook | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Facebook">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'facebook' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_twitter != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_twitter | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Twitter">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'twitter' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_instagram != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_instagram | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Instagram">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'instagram' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_pinterest != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_pinterest | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Pinterest">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'pinterest' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_youtube != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_youtube | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="YouTube">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'youtube' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_tiktok != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_tiktok | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="TikTok">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'tiktok' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_vimeo != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_vimeo | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Vimeo">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'vimeo' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_linkedin != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_linkedin | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="LinkedIn">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'linkedin' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_snapchat != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_snapchat | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="SnapChat">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'snapchat' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_tumblr != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_tumblr | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Tumblr">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'tumblr' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}

  {%- if settings.social_fancy != blank -%}
    <li class="HorizontalList__Item">
      <a href="{{ settings.social_fancy | replace_first: 'https://', '' | replace_first: 'http://', '' | prepend: 'https://' }}" class="Link Link--primary" target="_blank" rel="noopener" aria-label="Fancy">
        <span class="Icon-Wrapper--clickable">{%- render 'icon' with 'fancy' -%}</span>
      </a>
    </li>

    {% assign social_media_count = social_media_count | plus: 1 %}
  {%- endif -%}
{%- endcapture -%}

{%- assign spacing_to_use = spacing -%}

{%- if spacing == 'large' and social_media_count > 6 -%}
  {%- assign spacing_to_use = 'small' -%}
{%- endif -%}

{%- if social_media_count > 0 -%}
  <ul class="{{ class }} HorizontalList {% if spacing %}HorizontalList--spacing{{ spacing | capitalize }}{% endif %}">
    {{ social_media }}
  </ul>
{%- endif -%}