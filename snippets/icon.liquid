{%- capture icon_class -%}Icon Icon--{{ icon }} {{ class }}{%- endcapture -%}

{%- case icon -%}
  <!-- SOCIAL MEDIA -->

  {%- when 'facebook' -%}
    <svg class="{{ icon_class }}" viewBox="0 0 9 17">
      <path d="M5.842 17V9.246h2.653l.398-3.023h-3.05v-1.93c0-.874.246-1.47 1.526-1.47H9V.118C8.718.082 7.75 0 6.623 0 4.27 0 2.66 1.408 2.66 3.994v2.23H0v3.022h2.66V17h3.182z"></path>
    </svg>

  {%- when 'fancy' -%}
    <svg class="{{ icon_class }}" viewBox="0 0 24 32">
      <path d="M30.208 0q.75 0 1.271.521T32 1.792v28.416q0 .75-.521 1.271T30.208 32h-8.083V19.667h4.125l.583-4.875h-4.708v-3.125q0-1.125.5-1.708t1.833-.583H27V5.043q-1.667-.167-3.708-.167-2.792 0-4.5 1.646t-1.708 4.646v3.625h-4.125v4.875h4.125v12.333H1.792q-.75 0-1.271-.521T0 30.209V1.793q0-.75.521-1.271T1.792.001h28.416z"></path>
    </svg>

  {%- when 'instagram' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 32 32">
      <path d="M15.994 2.886c4.273 0 4.775.019 6.464.095 1.562.07 2.406.33 2.971.552.749.292 1.283.635 1.841 1.194s.908 1.092 1.194 1.841c.216.565.483 1.41.552 2.971.076 1.689.095 2.19.095 6.464s-.019 4.775-.095 6.464c-.07 1.562-.33 2.406-.552 2.971-.292.749-.635 1.283-1.194 1.841s-1.092.908-1.841 1.194c-.565.216-1.41.483-2.971.552-1.689.076-2.19.095-6.464.095s-4.775-.019-6.464-.095c-1.562-.07-2.406-.33-2.971-.552-.749-.292-1.283-.635-1.841-1.194s-.908-1.092-1.194-1.841c-.216-.565-.483-1.41-.552-2.971-.076-1.689-.095-2.19-.095-6.464s.019-4.775.095-6.464c.07-1.562.33-2.406.552-2.971.292-.749.635-1.283 1.194-1.841s1.092-.908 1.841-1.194c.565-.216 1.41-.483 2.971-.552 1.689-.083 2.19-.095 6.464-.095zm0-2.883c-4.343 0-4.889.019-6.597.095-1.702.076-2.864.349-3.879.743-1.054.406-1.943.959-2.832 1.848S1.251 4.473.838 5.521C.444 6.537.171 7.699.095 9.407.019 11.109 0 11.655 0 15.997s.019 4.889.095 6.597c.076 1.702.349 2.864.743 3.886.406 1.054.959 1.943 1.848 2.832s1.784 1.435 2.832 1.848c1.016.394 2.178.667 3.886.743s2.248.095 6.597.095 4.889-.019 6.597-.095c1.702-.076 2.864-.349 3.886-.743 1.054-.406 1.943-.959 2.832-1.848s1.435-1.784 1.848-2.832c.394-1.016.667-2.178.743-3.886s.095-2.248.095-6.597-.019-4.889-.095-6.597c-.076-1.702-.349-2.864-.743-3.886-.406-1.054-.959-1.943-1.848-2.832S27.532 1.247 26.484.834C25.468.44 24.306.167 22.598.091c-1.714-.07-2.26-.089-6.603-.089zm0 7.778c-4.533 0-8.216 3.676-8.216 8.216s3.683 8.216 8.216 8.216 8.216-3.683 8.216-8.216-3.683-8.216-8.216-8.216zm0 13.549c-2.946 0-5.333-2.387-5.333-5.333s2.387-5.333 5.333-5.333 5.333 2.387 5.333 5.333-2.387 5.333-5.333 5.333zM26.451 7.457c0 1.059-.858 1.917-1.917 1.917s-1.917-.858-1.917-1.917c0-1.059.858-1.917 1.917-1.917s1.917.858 1.917 1.917z"></path>
    </svg>

  {%- when 'tiktok' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 13 16">
      <path d="M12.998 6.146A4.055 4.055 0 019.23 4.331v6.245a4.616 4.616 0 11-4.615-4.615c.096 0 .19.008.285.014V8.25c-.095-.012-.188-.029-.285-.029a2.356 2.356 0 000 4.711c1.3 0 2.45-1.025 2.45-2.326L7.089 0h2.176A4.053 4.053 0 0013 3.618v2.528" fill="currentColor"></path>
    </svg>

  {%- when 'pinterest' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 32 32">
      <path d="M16 0q3.25 0 6.208 1.271t5.104 3.417 3.417 5.104T32 16q0 4.333-2.146 8.021t-5.833 5.833T16 32q-2.375 0-4.542-.625 1.208-1.958 1.625-3.458l1.125-4.375q.417.792 1.542 1.396t2.375.604q2.5 0 4.479-1.438t3.063-3.937 1.083-5.625q0-3.708-2.854-6.437t-7.271-2.729q-2.708 0-4.958.917T8.042 8.689t-2.104 3.208-.729 3.479q0 2.167.812 3.792t2.438 2.292q.292.125.5.021t.292-.396q.292-1.042.333-1.292.167-.458-.208-.875-1.083-1.208-1.083-3.125 0-3.167 2.188-5.437t5.729-2.271q3.125 0 4.875 1.708t1.75 4.458q0 2.292-.625 4.229t-1.792 3.104-2.667 1.167q-1.25 0-2.042-.917t-.5-2.167q.167-.583.438-1.5t.458-1.563.354-1.396.167-1.25q0-1.042-.542-1.708t-1.583-.667q-1.292 0-2.167 1.188t-.875 2.979q0 .667.104 1.292t.229.917l.125.292q-1.708 7.417-2.083 8.708-.333 1.583-.25 3.708-4.292-1.917-6.938-5.875T0 16Q0 9.375 4.687 4.688T15.999.001z"></path>
    </svg>

  {%- when 'tumblr' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 32 32">
      <path d="M6.593 13.105h3.323v11.256q0 2.037.456 3.35.509 1.206 1.581 2.144 1.045.965 2.76 1.581 1.635.563 3.725.563 1.822 0 3.404-.402 1.367-.268 3.564-1.313v-5.038q-2.224 1.528-4.61 1.528-1.179 0-2.358-.616-.697-.456-1.045-1.26-.268-.884-.268-3.564v-8.228h7.236V8.068h-7.236V.001h-4.342q-.214 2.278-1.045 4.047-.831 1.715-2.09 2.734-1.313 1.233-3.055 1.769v4.556z"></path>
    </svg>

  {%- when 'twitter' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 32 26">
      <path d="M32 3.077c-1.1748.525-2.4433.8748-3.768 1.031 1.356-.8123 2.3932-2.0995 2.887-3.6305-1.2686.7498-2.6746 1.2997-4.168 1.5934C25.751.796 24.045.0025 22.158.0025c-3.6242 0-6.561 2.937-6.561 6.5612 0 .5124.0562 1.0123.1686 1.4935C10.3104 7.7822 5.474 5.1702 2.237 1.196c-.5624.9687-.8873 2.0997-.8873 3.2994 0 2.2746 1.156 4.2867 2.9182 5.4615-1.075-.0314-2.0872-.3313-2.9745-.8187v.0812c0 3.1806 2.262 5.8363 5.2677 6.4362-.55.15-1.131.2312-1.731.2312-.4248 0-.831-.0438-1.2372-.1188.8374 2.6057 3.262 4.5054 6.13 4.5616-2.2495 1.7622-5.074 2.812-8.1546 2.812-.531 0-1.0498-.0313-1.5684-.0938 2.912 1.8684 6.3613 2.9494 10.0668 2.9494 12.0726 0 18.6776-10.0043 18.6776-18.6776 0-.2874-.0063-.5686-.0188-.8498C30.0066 5.5514 31.119 4.3954 32 3.077z"></path>
    </svg>

  {%- when 'vimeo' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 32 32">
      <path d="M0 9.393l1.511 2.023q2.1-1.562 2.535-1.562 1.664 0 3.098 5.301.384 1.46 1.255 4.75t1.357 5.057q1.895 5.301 4.763 5.301 4.558 0 11.062-8.757 6.376-8.322 6.606-13.162v-.563q0-5.89-4.712-6.043h-.358q-6.325 0-8.706 7.759 1.383-.589 2.407-.589 2.177 0 2.177 2.253 0 .282-.026.589-.154 1.818-2.151 4.968-2.049 3.303-3.047 3.303-1.332 0-2.356-5.019-.307-1.178-1.306-7.605-.435-2.766-1.613-4.097-1.024-1.152-2.561-1.178-.205 0-.435.026-1.613.154-4.788 2.945Q3.073 6.629 0 9.395z"></path>
    </svg>

  {%- when 'youtube' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 33 32">
      <path d="M0 25.693q0 1.997 1.318 3.395t3.209 1.398h24.259q1.891 0 3.209-1.398t1.318-3.395V6.387q0-1.997-1.331-3.435t-3.195-1.438H4.528q-1.864 0-3.195 1.438T.002 6.387v19.306zm12.116-3.488V9.876q0-.186.107-.293.08-.027.133-.027l.133.027 11.61 6.178q.107.107.107.266 0 .107-.107.213l-11.61 6.178q-.053.053-.107.053-.107 0-.16-.053-.107-.107-.107-.213z"></path>
    </svg>

  {%- when 'linkedin' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <path d="M19 0H5a5 5 0 0 0-5 5v14a5 5 0 0 0 5 5h14a5 5 0 0 0 5-5V5a5 5 0 0 0-5-5zM8 19H5V8h3v11zM6.5 6.73a1.76 1.76 0 1 1 0-3.53 1.76 1.76 0 0 1 0 3.53zM20 19h-3v-5.6c0-3.37-4-3.12-4 0V19h-3V8h3v1.76a3.8 3.8 0 0 1 7 2.48V19z"></path>
    </svg>

  {%- when 'snapchat' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <path d="M5.83 4.53c-.6 1.35-.36 3.75-.27 5.44-.65.36-1.48-.27-1.95-.27-.49 0-1.07.32-1.17.8-.06.35.1.85 1.2 1.29.43.17 1.46.37 1.7.93.33.78-1.72 4.4-4.92 4.93a.5.5 0 0 0-.42.52c.06.97 2.24 1.35 3.21 1.5.1.14.18.7.3 1.13.06.2.21.43.59.43.5 0 1.31-.38 2.74-.15 1.4.24 2.71 2.22 5.23 2.22 2.35 0 3.75-2 5.1-2.21a5.89 5.89 0 0 1 2.19.05c.51.1.98.16 1.12-.35.13-.43.21-.99.3-1.12.97-.15 3.16-.53 3.22-1.5a.5.5 0 0 0-.42-.52c-3.15-.52-5.26-4.13-4.92-4.93.24-.56 1.26-.76 1.7-.93.8-.32 1.22-.72 1.2-1.17 0-.59-.7-.94-1.22-.94-.53 0-1.29.63-1.9.29.1-1.7.33-4.1-.27-5.44A6.64 6.64 0 0 0 12 .7a6.6 6.6 0 0 0-6.16 3.83z"></path>
    </svg>

  <!-- UI -->

  {%- when 'cart' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 17 20">
      <path d="M0 20V4.995l1 .006v.015l4-.002V4c0-2.484 1.274-4 3.5-4C10.518 0 12 1.48 12 4v1.012l5-.003v.985H1V19h15V6.005h1V20H0zM11 4.49C11 2.267 10.507 1 8.5 1 6.5 1 6 2.27 6 4.49V5l5-.002V4.49z" fill="currentColor"></path>
    </svg>

  {%- when 'cart-desktop' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 19 23">
      <path d="M0 22.985V5.995L2 6v.03l17-.014v16.968H0zm17-15H2v13h15v-13zm-5-2.882c0-2.04-.493-3.203-2.5-3.203-2 0-2.5 1.164-2.5 3.203v.912H5V4.647C5 1.19 7.274 0 9.5 0 11.517 0 14 1.354 14 4.647v1.368h-2v-.912z" fill="currentColor"></path>
    </svg>

  {%- when 'search' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 18 17">
      <g transform="translate(1 1)" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square">
        <path d="M16 16l-5.0752-5.0752"></path>
        <circle cx="6.4" cy="6.4" r="6.4"></circle>
      </g>
    </svg>

  {%- when 'search-desktop' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 21 21">
      <g transform="translate(1 1)" stroke="currentColor" stroke-width="2" fill="none" fill-rule="evenodd" stroke-linecap="square">
        <path d="M18 18l-5.7096-5.7096"></path>
        <circle cx="7.2" cy="7.2" r="7.2"></circle>
      </g>
    </svg>

  {%- when 'nav' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 20 14">
      <path d="M0 14v-1h20v1H0zm0-7.5h20v1H0v-1zM0 0h20v1H0V0z" fill="currentColor"></path>
    </svg>

  {%- when 'nav-desktop' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 16">
      <path d="M0 15.985v-2h24v2H0zm0-9h24v2H0v-2zm0-7h24v2H0v-2z" fill="currentColor"></path>
    </svg>

  {%- when 'account' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 20 20">
      <g transform="translate(1 1)" stroke="currentColor" stroke-width="2" fill="none" fill-rule="evenodd" stroke-linecap="square">
        <path d="M0 18c0-4.5188182 3.663-8.******** 8.********-8.********h1.63636364C14.337 9.******** 18 13.4811818 18 18"></path>
        <circle cx="9" cy="4.********" r="4.********"></circle>
      </g>
    </svg>

  {%- when 'play' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <path d="M12 0C5.383 0 0 5.383 0 12s5.383 12 12 12 12-5.383 12-12S18.617 0 12 0zm-2 15.5V9l4.5 3.25L10 15.5z" fill="currentColor"></path>
    </svg>

  {%- when 'rss' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 16 16">
      <g fill="currentColor">
        <circle cx="3" cy="13" r="2"></circle>
        <path d="M15 15h-2.7C12.3 8.8 7.2 3.7 1 3.7V1c7.7 0 14 6.3 14 14z"></path>
        <path d="M10.3 15H7.7c0-3.7-3-6.7-6.7-6.7V5.7c5.1 0 9.3 4.2 9.3 9.3z"></path>
      </g>
    </svg>

  {%- when 'checkmark' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <polygon fill="currentColor" points="9,20 2,13 5,10 9,14 19,4 22,7 "></polygon>
    </svg>

  {%- when 'close' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 16 14">
      <path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path>
    </svg>

  {%- when 'plus' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 16 16">
      <g stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square">
        <path d="M8,1 L8,15"></path>
        <path d="M1,8 L15,8"></path>
      </g>
    </svg>

  {%- when 'minus' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 16 2">
      <path d="M1,1 L15,1" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square"></path>
    </svg>

  {%- when 'select-arrow' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 19 12">
      <polyline fill="none" stroke="currentColor" points="17 2 9.5 10 2 2" fill-rule="evenodd" stroke-width="2" stroke-linecap="square"></polyline>
    </svg>

  {%- when 'select-arrow-right' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 11 18">
      <path d="M1.5 1.5l8 7.5-8 7.5" stroke-width="2" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square"></path>
    </svg>

  {%- when 'select-arrow-left' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 11 18">
      <path d="M9.5 1.5L1.5 9l8 7.5" stroke-width="2" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square"></path>
    </svg>

  {%- when 'arrow-bottom' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 21 11">
      <polyline fill="none" stroke="currentColor" points="0.5 0.5 10.5 10.5 20.5 0.5" stroke-width="1.25"></polyline>
    </svg>

  {%- when 'arrow-top' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 21 11">
      <polyline fill="none" stroke="currentColor" points="20.5 10.5 10.5 0.5 0.5 10.5" stroke-width="1.25"></polyline>
    </svg>

  {%- when 'arrow-left' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 11 21">
      <polyline fill="none" stroke="currentColor" points="10.5 0.5 0.5 10.5 10.5 20.5" stroke-width="1.25"></polyline>
    </svg>

  {%- when 'arrow-right' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 11 21">
      <polyline fill="none" stroke="currentColor" points="0.5 0.5 10.5 10.5 0.5 20.5" stroke-width="1.25"></polyline>
    </svg>

  {%- when 'share' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <g stroke="currentColor" fill="none" fill-rule="evenodd" stroke-width="1.5">
        <path d="M8.6,10.2 L15.4,6.8"></path>
        <path d="M8.6,13.7 L15.4,17.1"></path>
        <circle stroke-linecap="square" cx="5" cy="12" r="4"></circle>
        <circle stroke-linecap="square" cx="19" cy="5" r="4"></circle>
        <circle stroke-linecap="square" cx="19" cy="19" r="4"></circle>
      </g>
    </svg>

  {%- when 'lock' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <g stroke="currentColor" fill="none" stroke-width="2" stroke-linecap="square" stroke-linejoin="miter">
        <path stroke-miterlimit="10" d="M12,1L12,1 C9.2,1,7,3.2,7,6v3h10V6C17,3.2,14.8,1,12,1z"></path>
        <rect x="4" y="9" stroke-miterlimit="10" width="16" height="14"></rect>
        <circle stroke-miterlimit="10" cx="12" cy="15" r="2"></circle>
        <line stroke-miterlimit="10" x1="12" y1="17" x2="12" y2="19"></line>
      </g>
    </svg>

  {%- when 'shopify-logo' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 150 43">
      <path fill="currentColor" d="M33.3 8.9s0-.2-.1-.3c-.1-.1-.2-.1-.2-.1l-3.4-.2-2.1-2.1c-.1-.1-.2-.1-.3-.1l-1.8 36.1L38 39.5 33.3 8.9zm-7.5-3l-.9.3c-.6-1.6-1.3-2.8-2.3-3.5-.7-.5-1.5-.7-2.3-.6l-.6-.6c-.9-.7-2.1-.9-3.6-.3C11.8 2.7 10 8.3 9.3 11l-3.8 1.1s-.9.2-1.1.5c-.2.3-.3 1-.3 1L.9 37.9l23.6 4.4L26.3 6c-.2-.2-.4-.1-.5-.1zm-5.7 1.7L16 8.9c.5-2.1 1.6-4.3 3.6-5.1.4 1 .5 2.5.5 3.8zm-3.5-5.2c.9-.3 1.6-.3 2.1 0-2.7 1.2-3.9 4.3-4.4 6.9l-3.3 1c.7-2.5 2.3-6.7 5.6-7.9zm2.3 17.9c-.2-.1-.4-.2-.7-.3-.3-.1-.5-.2-.8-.3-.3-.1-.6-.1-1-.2h-1.1c-.3 0-.6.1-.9.2-.3.1-.5.2-.7.4-.2.2-.3.4-.4.6-.1.2-.2.5-.2.7 0 .2 0 .4.1.6l.3.6.6.6c.2.2.5.4.8.6.5.3.9.6 1.4 1 .5.4.9.8 1.2 1.3.4.5.7 1 .9 1.7.2.6.3 1.3.3 2.1-.1 1.2-.3 2.3-.8 3.2-.4.9-1.1 1.6-1.8 2.1s-1.6.8-2.5.9c-.9.1-1.9.1-2.8-.2-.5-.1-.9-.3-1.3-.4l-1.2-.6c-.3-.2-.7-.4-.9-.6-.3-.2-.5-.4-.7-.7L7.8 30c.2.2.4.3.7.5.3.2.6.4.9.5.3.2.7.3 1 .5.4.1.7.2 1.1.3h.8c.2-.1.5-.2.6-.3.2-.1.3-.3.4-.5.1-.2.1-.4.2-.7 0-.2 0-.5-.1-.7-.1-.2-.2-.4-.3-.7-.1-.2-.3-.4-.6-.7-.2-.2-.5-.5-.9-.7-.4-.3-.8-.6-1.2-1-.3-.4-.7-.7-.9-1.2-.2-.4-.4-.9-.6-1.4-.1-.5-.2-1-.2-1.6 0-1 .2-1.8.6-2.6.3-.8.8-1.5 1.4-2.2.6-.6 1.3-1.2 2.2-1.6.9-.4 1.8-.7 2.9-.9.5-.1 1-.1 1.4-.1.5 0 .9 0 1.3.1s.8.1 1.1.2l.9.3-1.6 4.8zm2.6-13.1v-.5c0-1.3-.2-2.4-.5-3.2.3 0 .6.1.9.3.8.5 1.3 1.6 1.7 2.8l-2.1.6zM45.3 29.6c.9.5 2.5 1.1 4.1 1.1 1.4 0 2.2-.8 2.2-1.7 0-.9-.5-1.5-2.1-2.4-1.9-1.1-3.3-2.6-3.3-4.6 0-3.5 3-6 7.4-6 1.9 0 3.4.4 4.2.8l-1.2 3.5c-.7-.3-1.8-.7-3.1-.7-1.4 0-2.3.6-2.3 1.7 0 .8.7 1.4 1.9 2 2 1.1 3.6 2.6 3.6 4.7 0 4-3.2 6.2-7.7 6.1-2.1 0-4-.6-4.9-1.2l1.2-3.3zm12.4 4.5l4.9-25.2h5l-1.9 9.8h.1c1.3-1.6 3.1-2.7 5.3-2.7 2.6 0 4.1 1.7 4.1 4.5 0 .9-.1 2.2-.4 3.3l-2 10.3h-5l1.9-9.9c.1-.7.2-1.5.2-2.2 0-1.1-.4-1.8-1.6-1.8-1.6 0-3.3 2-4 5.3l-1.7 8.7h-4.9v-.1zM93.3 23c0 6.1-4 11.4-9.9 11.4-4.5 0-6.9-3.1-6.9-6.9 0-6 4-11.4 10-11.4 4.7 0 6.8 3.3 6.8 6.9zm-11.7 4.3c0 1.8.7 3.2 2.4 3.2 2.7 0 4.1-4.7 4.1-7.7 0-1.5-.6-3-2.4-3-2.6.1-4.1 4.7-4.1 7.5zm10.5 13.8L95.6 23c.4-2 .8-4.7 1-6.6h4.4l-.3 2.8h.1c1.3-1.9 3.3-3 5.3-3 3.7 0 5.2 2.9 5.2 6.3 0 6-3.9 12.1-9.7 12.1-1.2 0-2.4-.5-2.9-.5h-.1l-1.4 7h-5.1zm7.2-11.2c.5.4 1.2.7 2.1.7 2.8 0 4.7-4.6 4.7-7.8 0-1.3-.5-2.7-2-2.7-1.7 0-3.4 2-4 5.1l-.8 4.7zm12.2 4.2l3.4-17.7h5.1l-3.4 17.7h-5.1zm6.5-19.6c-1.4 0-2.4-1.1-2.4-2.6 0-1.6 1.3-2.9 2.9-2.9 1.5 0 2.5 1.1 2.5 2.6 0 1.8-1.4 2.9-3 2.9zm2.9 19.6l2.7-14h-2.3l.7-3.7h2.3l.1-.8c.4-2.1 1.2-4.2 2.9-5.6 1.3-1.1 3.1-1.6 4.9-1.6 1.2 0 2.1.2 2.7.4l-1 3.9c-.4-.1-.9-.3-1.6-.3-1.7 0-2.7 1.5-3 3.2l-.2.8h3.5l-.7 3.7h-3.5l-2.7 14h-4.8zm18-17.7l.8 7.9c.2 1.8.4 3.3.4 4.2h.1c.4-.9.8-2.3 1.5-4.2l3.1-7.9h5.2l-6.1 13.1c-2.2 4.5-4.3 7.7-6.6 9.9-1.8 1.7-3.9 2.5-4.9 2.7l-1.4-4.2c.8-.3 1.9-.7 2.8-1.4 1.2-.8 2.1-1.9 2.7-3 .1-.3.2-.5.1-1.9l-3-15.2h5.3z"></path>
    </svg>

  {%- when 'wall-1' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 36 36">
      <rect fill="currentColor" width="36" height="36"></rect>
    </svg>

  {%- when 'wall-2' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 36 36">
      <path fill="currentColor" d="M21 36V21h15v15H21zm0-36h15v15H21V0zM0 21h15v15H0V21zM0 0h15v15H0V0z"></path>
    </svg>

  {%- when 'wall-4' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 36 36">
      <path fill="currentColor" d="M28 36v-8h8v8h-8zm0-22h8v8h-8v-8zm0-14h8v8h-8V0zM14 28h8v8h-8v-8zm0-14h8v8h-8v-8zm0-14h8v8h-8V0zM0 28h8v8H0v-8zm0-14h8v8H0v-8zM0 0h8v8H0V0z"></path>
    </svg>

  {%- when 'sale' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 24 24">
      <path d="M22.707 12.293l-11-11A1.002 1.002 0 0 0 11 1H2a1 1 0 0 0-1 1v9c0 .265.105.52.293.707l11 11a.997.997 0 0 0 1.414 0l9-9a.999.999 0 0 0 0-1.414zM7 9a2 2 0 1 1-.001-3.999A2 2 0 0 1 7 9zm6 8.414L8.586 13 10 11.586 14.414 16 13 17.414zm3-3L11.586 10 13 8.586 17.414 13 16 14.414z" fill="currentColor"></path>
    </svg>

  {%- when 'rating-star' -%}
    <svg fill="none" focusable="false" width="{{ width | default: 14 }}" height="{{ height | default: 14 }}" class="{{ icon_class }}" viewBox="0 0 14 13">
      <path d="M7 0L8.6458 4.73475L13.6574 4.83688L9.66296 7.86525L11.1145 12.6631L7 9.8L2.8855 12.6631L4.33704 7.86525L0.342604 4.83688L5.3542 4.73475L7 0Z" fill="currentColor"></path>
    </svg>

  {%- when 'rating-star-half' -%}
    <svg fill="none" focusable="false" width="{{ width | default: 14 }}" height="{{ height | default: 14 }}" class="{{ icon_class }}" viewBox="0 0 14 13">
      <path d="M7 0L8.6458 4.73475L13.6574 4.83688L9.66296 7.86525L11.1145 12.6631L7 9.8L2.8855 12.6631L4.33704 7.86525L0.342604 4.83688L5.3542 4.73475L7 0Z" fill="url(#rating-star-gradient-half)"></path>
    </svg>

  <!-- STORE PICKUP -->

  {% when 'store-availability-in-stock' %}
    <svg focusable="false" class="{{ icon_class }}" viewBox="0 0 13 9" role="presentation">
      <path fill="none" d="M1 4l4 4 7-7" stroke="#307A07" stroke-linecap="square"></path>
    </svg>

  {% when 'store-availability-out-of-stock' %}
    <svg focusable="false" class="{{ icon_class }}" viewBox="0 0 11 10" role="presentation">
      <path fill="none" d="M10 9.5l-9-9m9 0l-9 9" stroke="#CB2B2B"></path>
    </svg>

  <!-- MEDIA API (SHOPIFY ICONS) -->

  {%- when 'media-model-badge' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 26 26">
      <path d="M1 25h24V1H1z" fill="{{ settings.light_background }}"></path>
      <path d="M.5 25v.5h25V.5H.5z" fill="none" stroke="{{ settings.text_color }}" stroke-opacity=".15"></path>
      <path d="M19.13 8.28L14 5.32a2 2 0 00-2 0l-5.12 3a2 2 0 00-1 1.76V16a2 2 0 001 1.76l5.12 3a2 2 0 002 0l5.12-3a2 2 0 001-1.76v-6a2 2 0 00-.99-1.72zm-6.4 11.1l-5.12-3a.53.53 0 01-.26-.38v-6a.53.53 0 01.27-.46l5.12-3a.53.53 0 01.53 0l5.12 3-4.72 2.68a1.33 1.33 0 00-.67 1.2v6a.53.53 0 01-.26 0z" fill="{{ settings.text_color }}" opacity=".6"></path>
    </svg>

  {%- when 'media-view-in-space' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 16 16">
      <path d="M14.13 3.28L9 .32a2 2 0 00-2 0l-5.12 3a2 2 0 00-1 1.76V11a2 2 0 001 1.76l5.12 3a2 2 0 002 0l5.12-3a2 2 0 001-1.76V5a2 2 0 00-.99-1.72zm-6.4 11.1l-5.12-3a.53.53 0 01-.26-.38V5a.53.53 0 01.27-.46l5.12-3a.53.53 0 01.53 0l5.12 3-4.72 2.68A1.33 1.33 0 008 8.42v6a.53.53 0 01-.26 0l-.01-.04z" fill="{{ settings.text_color }}" fill-rule="nonzero"></path>
    </svg>

  {%- when 'media-video-badge' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 26 26" fill="none">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M1 25h24V1H1v24z" fill="{{ settings.light_background }}"></path>
      <path d="M.5 25v.5h25V.5H.5V25z" fill="none" stroke="{{ settings.text_color }}" stroke-opacity=".15"></path>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M9.718 6.72a1 1 0 00-1.518.855v10.736a1 1 0 001.562.827l8.35-5.677a1 1 0 00-.044-1.682l-8.35-5.06z" fill="{{ settings.text_color }}" fill-opacity=".6"></path>
    </svg>

  {%- when 'media-arrow-left' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 6 9">
      <path d="M5 8.5l-4-4 4-4" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square"></path>
    </svg>

  {%- when 'media-arrow-right' -%}
    <svg class="{{ icon_class }}" role="presentation" viewBox="0 0 6 9">
      <path d="M1 8.5l4-4-4-4" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="square"></path>
    </svg>
{%- endcase -%}