label {
  user-select: none;
  cursor: pointer;
}

.hide {
  display: none;
}

/* Annoucement Bar  / annoucement.liquid */

.AnnouncementBar {
  position: relative;
  text-align: center;
  font-size: 14px;
  z-index: 1;
}

.AnnouncementBar__Wrapper {
  padding: 5px 15px;
}

.AnnouncementBar__Image {
  width: auto;
  height: 20px;
  margin-left: 10px;
  -webkit-filter: brightness(0.5);
  filter: brightness(0.5);
}

.AnnouncementBar__Content {
  color: inherit;
  margin: 0;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.AnnouncementBar__item a {
  color: inherit;
  text-decoration: none;
}

@media(max-width:768px) {
  .AnnouncementBar__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
  }

  .AnnouncementBar__Image {
    margin-right: 0;
    margin-bottom: 5px;
  }
}

.AnnouncementBar__Content p {
  margin: 0;
  color: inherit;
  font-size: 14px;
}

.AnnouncementBar__Content>* {
  width: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 0px;
  flex: 1 1 0;
  text-align: center;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.AnnouncementBar__Content:after {
  content: 'flickity';
  display: none;
}

@media screen and (min-width: 768px) {

  /* disable Flickity for large devices */
  .AnnouncementBar__Content:after {
    content: '';
  }
}

@media screen and (min-width: 641px) {
  .AnnouncementBar {
    font-size: 16px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * USP section
 * ----------------------------------------------------------------------------
 */

.USP {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  padding: 70px 30px;
}

@media screen and (min-width:950px) {
  .USP {
    grid-auto-flow: column;
    grid-auto-columns: 1fr;
  }
}

.USP__block {
  display: grid;
  place-content: center;
  text-align: center;
}

.USP__block__image {
  margin-bottom: 1rem;
  height: 50px;
}

.USP__block__image>img {
  height: 50px;
  width: 50px;
  object-fit: contain;
  object-position: 100% 100%;
}

.USP__block__maintext {
  font-weight: bold;
  margin-bottom: 0;
}

/**
 * ----------------------------------------------------------------------------
 * Collage section
 * ----------------------------------------------------------------------------
 */

.collage {
  display: grid;
  height: 50vh;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(8, 1fr);
  grid-gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.hideOnMobile {
  display: none;
}

@media screen and (min-width:768px) {
  .hideOnMobile {
    display: block;
  }
}

.collageImage {
  position: relative;
}

.collageImage>div {
  position: absolute;
  left: 5%;
  bottom: 5%;
  font-size: 1.5rem;
  font-weight: bold;
  text-transform: uppercase;
  width: 100%;
  overflow: hidden;
  display: grid;
  justify-content: start;
}

.collageImage>img {
  object-fit: cover;
  object-position: center;
  height: 100% !important;
  width: 100%;
}

.collageContent {
  line-height: 1.2;
}

.collageContent__subtext {
  margin: 0;
  font-size: 1rem;
}

.collageContent__text {
  margin: 0;
}

.collageContent__CtaBtn.Button {
  width: fit-content;
  padding: 7px 14px;
}

.collageImage:nth-child(1) {
  display: inline-block;
  grid-row: 1 / -1;
  grid-column: 1 / 7;
}

.collageImage:nth-child(2) {
  grid-row: 5/ -1;
  grid-column: 7 / -1;
}

.collageImage:nth-child(3) {
  grid-row: 1 / 5;
  grid-column: 7 / -1;
}

.collageImage:nth-child(4) {
  display: none;
  grid-column: 10/-1;
  grid-row: 5/ -1;
}

@media screen and (min-width:768px) {
  .collageContent__CtaBtn.Button {
    padding: 14px 28px;
  }

  .collageImage>div {
    font-size: 3rem;
  }

  .collage {
    max-height: 104vh;
    height: auto;
  }

  .collageImage:nth-child(2) {
    grid-row: 5/ -1;
    grid-column: 7 / 10;
  }

  .collageImage:nth-child(4) {
    display: block;
  }
}

/**
 * ----------------------------------------------------------------------------
 * ProductItem variant
 * ----------------------------------------------------------------------------
 */
.ProducItem__variants {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  position: absolute;
  bottom: 5%;
  left: 5%;
  right: 5%;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition: opacity .5s cubic-bezier(.5, 1, .89, 1), visibility .3s cubic-bezier(.5, 1, .89, 1);
  list-style: none;

}

.ProducItem__variants__variant {
  /* width:100%; */
  min-width: 58px;
  padding: 5px 15px;
  background-color: white;
  color: var(--text-color-light);
  border: 1px solid var(--text-color-light);
  margin: 5px 2.5px 0;
}

.ProducItem__variants__variant:not(:last-child) {}

.line-through {
  position: relative;
}

.line-through:after {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  height: 1px;
  background: #000;
  content: "";
  width: 50%;
  display: block;
}

.ProductItem__withhover:hover>.ProducItem__variants {
  opacity: 1;
  visibility: visible;
}


/**
 * ----------------------------------------------------------------------------
 * Seo Section
 * ----------------------------------------------------------------------------
 */

.seoSection {
  display: grid;
  gap: 3rem;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  margin-left: auto;
  margin-right: auto;
  padding: 30px 30px;
  width: 100%;
}

.seoArticle {
  height: 100%;
}

.seoArticle:first-child {
  place-self: end;
}

.seoArticle:nth-child(2) {
  place-self: start;
}

.seoArticle__heading {
  font-size: 22px;
  margin-bottom: 1.75rem;
}

@media (min-width: 768px) {
  .seoSection {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .seoArticle__heading {
    font-size: 28px;
  }
}

@media (min-width: 1100px) {
  .seoSection {
    padding: 120px 120px;
  }

  .seoArticle {
    max-width: 600px;

  }
}

.more {
  display: none;
}

/*
 * ----------------------------------------------------------------------------
 * Linked Products
 * ----------------------------------------------------------------------------
 */
.linkedProducts__wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: 1rem 0;

  max-height: 52px;
  overflow: hidden;

  max-width: 314px;
}

.linkedProducts-product, .linkedProducts__breaker {
  width: 52px;
  height: 52px;
  background-color: #fafafa;
}

.linkedProducts-image__wrapper {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.linkedProducts-image__wrapper img, .linkedProducts-image__wrapper {
  width: 100%;
  height: 100%;
}

.linkedProducts-product {
  border: 1px solid transparent;
  transition: border .2s ease-out;
  margin-bottom: 2px;
}

.linkedProducts-product.active {
  border-color: #000000;
}

.linkedProducts__breaker {
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  margin-left: 2px;

  width: 52px;
  height: 52px;
}

@media(min-width: 1008px) {
  .linkedProducts-product, .linkedProducts__breaker {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .linkedProducts__wrapper {
    max-width: 356px;
  }
}


@media (hover: hover) {
  .linkedProducts-product:hover {
    border-color: #bababa;
  }

  .linkedProducts-product.active:hover {
    border-color: #000000;
  }
}

/*
 * ----------------------------------------------------------------------------
 * Countdown timer
 * ----------------------------------------------------------------------------
 */
#countdown {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  font-size: 14px;

}

#countdown>div {
  display: flex;
  flex-wrap: wrap;
}

#countdown div.bold {
  font-weight: bold;
}

#countdown div.space-left {
  margin-left: 4px;
}

#countdown div.smalltext {
  margin-left: 1px;
}

#countdown>div.hide {
  display: none;
}


.countdownWrapper {
  margin-top: 1rem;
  margin-bottom: 2rem;
}

/* Accept terms and conditions */

.accept-terms {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@media screen and (max-width: 560px) {
  .accept-terms {
    display: none;
  }
}

.accept-terms--link {
  text-decoration: underline;
}

/**
* ----------------------------------------------------------------------------
* Cart terms
* ----------------------------------------------------------------------------
*/

.terms-wrapper,
.offer-wrapper {
  z-index: 1000;
  position: fixed;
  display: flex;
  align-items: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.terms-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  background: black;
  transition: opacity 250ms ease-in-out;
  pointer-events: all;
}

.terms-overlay--active {
  opacity: 0.3;
  visibility: visible;
}

.terms-content-wrapper,
.offer-content-wrapper {
  z-index: 1;
  background: white;
  margin: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 90%;
  max-width: 600px;
  border-radius: 5px;
  visibility: hidden;
  opacity: 0;
  transform: translateY(50%);
  transition: all 250ms ease-in-out;
  pointer-events: all;
}

.terms-content-wrapper--active,
.offer-content-wrapper--active {
  visibility: visible;
  opacity: 1;
  transform: translateY(0%);
}

.terms-content-wrapper h2 {
  font-size: 1.625rem;
  font-weight: 600;
}

.terms-content-wrapper p {
  font-size: 1.125rem;
}

.terms-content {
  color: black;
}

.terms-buttons {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: space-around;
}

.terms-buttons div {
  cursor: pointer;
  width: 100%;
  margin: 5px;
  padding: 10px;
  border-radius: 5px;
  transition: transform 150ms ease-in-out;
}

.terms-buttons div:hover {
  transform: translateY(-4px);
}

.terms-accept {
  color: var(--button-text-color);
  background-color: var(--button-background);
  border: 2px solid transparent;
}

.terms-deny {
  color: black;
  border: 2px solid black;
}

.cartTermsLabels {
  display: none;
}

.cartTermsLabels a,
.terms-content-wrapper a {
  text-decoration: underline;
}

@media screen and (min-width: 1024px) {
  .terms-buttons {
    flex-direction: row;
  }

  .cartTermsLabels {
    display: block;
  }
}

/**
* ----------------------------------------------------------------------------
* Sidebar cart recommended
* ----------------------------------------------------------------------------
*/

.cart-drawer__recommended {
  overflow-y: scroll;
  border-right: 1px solid var(--border-color);
  opacity: 0;
  transform: translateX(100%);
  visibility: hidden;
  transition: .3s all ease-in-out;
  transition-delay: 0.8s;
  min-width: 160px;
  max-width: 160px;
}

.cart-drawer__recommended .rebuy-widget {
  padding: 2rem 1rem 1rem;
}

.rebuy-product-media {
  width: 100% !important;
}

.cart-drawer__recommended .rebuy-product-info {
  width: 100% !important;
  text-align: center !important;
  padding: 0 !important;
}

.powered-by-rebuy,
.rebuy-button {
  display: none !important;
}

.rebuy-product-title {
  font-size: 11px;
  color: var(--heading-color);
}

.primary-title {
  font-size: 17px !important;
}

.rebuy-money {
  color: var(--text-color-light) !important;
  font-size: 11px;
}

.rebuy-product-block {
  padding-bottom: 0 !important;
}

#sidebar-cart {
  background: none;
}

.Drawer__Content {
  background-color: #fff;
}

.cart-drawer__recommended[data-initialized="true"] {
  opacity: 1;
  visibility: visible;
  background-color: #fff;
  transform: translateX(0);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.cart-drawer__recommended::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.cart-drawer__recommended {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.sidebar-cart-inner {
  display: flex;
  height: 100vh;
}

/**
 * ----------------------------------------------------------------------------
 * Hero Video
 * ----------------------------------------------------------------------------
 */

.heroVideoContainer--mobile {
  display: block;
}

.heroVideoContainer--desktop {
  display: none;
}

.heroVideoContainer {
  width: 100%;
  position: relative;
}

.heroVideo {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.heroVideo__CTA {
  position: absolute;
}

.heroVideo__CTA>h2 {
  color: #fff;
}

.CtaButton {
  color: #000;
  background-color: #fff;
  margin: 8px;
  margin-left: 0;
  border-radius: 2rem;
}

.heroVideo__CTA>.ButtonGroup {
  margin-left: 0;
}

@media screen and (min-width:640px) {
  .heroVideoContainer--desktop {
    display: block;
  }

  .heroVideoContainer--mobile {
    display: none;
  }
}

.Button,
.Button--primary {
  border: none;
}

/**
* ----------------------------------------------------------------------------
* Brands
* ----------------------------------------------------------------------------
*/

.brands {
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  margin-bottom: 2rem;
}

.brandsList {
  display: flex;
  flex-wrap: wrap;
  padding: 0 1rem;
}

.brandsListItem {
  display: block;
}

.brandsBlocks {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.brandBlocks__item {
  flex: 0 1 50%;
  padding: 0.5rem;
}

.brandsListItem {
  padding: 0.25rem 0.5rem;
}

.brandBlocks__item__image-wrapper {
  position: relative;
  height: 150px;
  background-color: #fff;
  border: 1px solid #e1e1e1;
}

.brandBlocks__item__image {
  position: absolute;
  object-fit: contain;
  width: 100%;
  height: 100%;
}

@media screen and (min-width:768px) {
  .brands {
    grid-template-columns: auto 1fr;
  }

  .brandBlocks__item {
    flex: 0 1 33.33%;
  }

  .brandsList {
    padding: 0 1rem;
    display: block;
  }
}

@media screen and (min-width: 641px) {
  .Drawer {
    width: 560px;
  }

  .Drawer__Content {
    flex: 1 1 0;
  }
}

@media screen and (min-width: 1300px) {
  .brandBlocks__item {
    flex: 0 1 20%;
  }

  .brandsList {
    padding: 0 0 0 2rem;
  }
}

/**
* ----------------------------------------------------------------------------
* Gift wrap checkbox
* ----------------------------------------------------------------------------
*/
.giftwrap {
  display: flex;
  align-items: center;
  margin: 1rem 0;
}

#gift-wrapping {
  margin-right: 0.5rem;
}

/*
 * ----------------------------------------------------------------------------
 * Wishlist king product page
 * ----------------------------------------------------------------------------
 */

.button-wk-wrap {
  display: flex;
  flex-direction: row-reverse;
}

.button-wk-wrap .custom-button-wk+button[type="submit"] {
  margin-right: 10px;
}

.custom-button-wk {
  flex-shrink: 0;
  position: relative;
  width: 43px;
  height: auto;
}

.custom-button-wk::before {
  content: '';
  display: block;
  width: 100%;
  padding-bottom: 100%;
}

.custom-button-wk .wk-button {
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;

  background-color: #5d4e32;
}

.custom-button-wk .wk-button:hover {
  background-color: #100d08;
}



.custom-button-wk .wk-button .wk-icon {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 10px;
  color: #fff;
}

.button-wk-wrap .wk-button__label {
  display: none;
}


/* Klaviyo */

.Btn__Klaviyo {
  margin-top: 1rem;
  width: 100% !important;
}
