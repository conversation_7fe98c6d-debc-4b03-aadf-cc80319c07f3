/**
 * Include your custom JavaScript here.
 *
 * We also offer some hooks so you can plug your own logic. For instance, if you want to be notified when the variant
 * changes on product page, you can attach a listener to the document:
 *
 * document.addEventListener('variant:changed', function(event) {
 *   var variant = event.detail.variant; // Gives you access to the whole variant details
 * });
 *
 * You can also add a listener whenever a product is added to the cart:
 *
 * document.addEventListener('product:added', function(event) {
 *   var variant = event.detail.variant; // Get the variant that was added
 *   var quantity = event.detail.quantity; // Get the quantity that was added
 * });
 *
 * If you just want to force refresh the mini-cart without adding a specific product, you can trigger the event
 * "cart:refresh" in a similar way (in that case, passing the quantity is not necessary):
 *
 * document.documentElement.dispatchEvent(new CustomEvent('cart:refresh', {
 *   bubbles: true
 * }));
 */

const readmoreBtn = document.querySelectorAll(".readmoreBtn");

const toggleText = (i) => {
  const readmoreText = document.querySelectorAll(".more");
  const dots = document.querySelectorAll(".dots");

  if (dots[i].style.display === "none") {
    dots[i].style.display = "inline";
    readmoreBtn[i].innerHTML = window.languages.readmoreBtn;
    readmoreText[i].style.display = "none";
  } else {
    dots[i].style.display = "none";
    readmoreBtn[i].innerHTML = window.languages.readLessBtn;
    readmoreText[i].style.display = "inline";
  }

};
if (readmoreBtn.length !== 0) {
  readmoreBtn[0].addEventListener("click", () => {
    toggleText(0);
  });
  readmoreBtn[1].addEventListener("click", () => {
    toggleText(1);
  });
}


const variantListItems = document.querySelectorAll(".SizeSwatchList>.HorizontalList__Item");
const globalVariantList = window.product.variant;

globalVariantList?.forEach((element, index) => {
  if (element.available === false) {
    variantListItems[index].classList.add("line-through")
  }
});


/* terms and conditions */
const termsOverlay = document.querySelector('.terms-overlay');
const termsContent = document.querySelector('.terms-content-wrapper');
const termsDeny = document.querySelector('.terms-deny');
const termsAccept = document.querySelector('.terms-accept');

function showTerms() {
  termsOverlay.classList.add('terms-overlay--active');
  termsContent.classList.add('terms-content-wrapper--active');
}

function hideTerms() {
  termsOverlay.classList.remove('terms-overlay--active');
  termsContent.classList.remove('terms-content-wrapper--active');
}

function validateTerms(e) {
  e.preventDefault();
  const cartTerms = document.querySelector('input[name=checkbox]');

  if (cartTerms.checked) {
    window.location = '/checkout';
  } else {
    showTerms();
  }
}

termsOverlay?.addEventListener('click', () => {
  hideTerms();
});

termsAccept?.addEventListener('click', () => {
  window.location = '/checkout';
});

termsDeny?.addEventListener('click', () => {
  hideTerms();
});

const productPage = window.location.href.includes('/products/');

if (productPage) {
  var node = document.querySelector('[data-countdown]');
  var getWeekday = window.countdown.times;

  var now = new Date();
  var countdownFrom = new Date();

  var currentDay = getWeekday[now.getDay()].split(':');
  var clocktimer;

  function checkDist () {
    currentDay = getWeekday[countdownFrom.getDay()].split(':');

    countdownFrom.setHours(+currentDay[0]);
    countdownFrom.setMinutes(+currentDay[1]);
    countdownFrom.setSeconds(0);

    now = new Date();
    const dist = countdownFrom.getTime() - now;

    return dist < 0
  }

  function getDisplayTimer() {
    currentDay = getWeekday[countdownFrom.getDay()].split(':');

    countdownFrom.setHours(+currentDay[0]);
    countdownFrom.setMinutes(+currentDay[1]);
    countdownFrom.setSeconds(0);

    now = new Date();
    const dist = countdownFrom.getTime() - now;
    if (dist > 0) {
      const daysFloor = Math.floor(dist / (1000 * 60 * 60 * 24));
      const hoursFloor = Math.floor((dist % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutesFloor = Math.floor((dist % (1000 * 60 * 60)) / (1000 * 60));
      const secondsFloor = Math.floor((dist % (1000 * 60)) / 1000);

      if (daysFloor > 0) {
        node.innerHTML = window.countdown.next_day_html;
        const days = node.querySelector('[data-days]');
        days.textContent = `${daysFloor}${window.countdown.days}`;
      } else {
        node.innerHTML = window.countdown.same_day_html;
      }
      const hours = node.querySelector('[data-hours]');
      const minutes = node.querySelector('[data-minutes]');
      const seconds = node.querySelector('[data-seconds]');
      hours.textContent = `${hoursFloor}${window.countdown.hours}`;
      minutes.textContent = `${minutesFloor}${window.countdown.minutes}`;
      seconds.textContent = `${secondsFloor}${window.countdown.seconds}`;
    }
  }

  const currentDayTimeIsEmpty = !getWeekday[countdownFrom.getDay()];

  const getNextAvailable = () => {
    const check = !getWeekday[countdownFrom.getDay()];
    if (check) {
      countdownFrom.setDate(countdownFrom.getDate() + 1);
      getNextAvailable();
    }
  };

  if (currentDayTimeIsEmpty) {
    getNextAvailable();
  }

  if (checkDist()) {
    countdownFrom.setDate(countdownFrom.getDate() + 1);
    countdownFrom.setHours(0);
    countdownFrom.setMinutes(0);
    countdownFrom.setSeconds(0);
    getNextAvailable()
  }

  clocktimer = setInterval(() => {
    getDisplayTimer();
  }, 1000);
}

/**
 * Show points based on variant
 */

if (window.location.pathname.includes('/products/')) {
  document.addEventListener('variant:changed', function (evt) {
    var variant = evt.detail.variant;
    var pointsElm = document.querySelector(`.product-points[data-variant="${variant.id}"]`);
    var pointsElmArr = document.querySelectorAll(`.product-points[data-variant]`);

    pointsElmArr.forEach(i => i.classList.add('u-visually-hidden'));
    if (pointsElm) {
      pointsElm.classList.remove('u-visually-hidden');
    }
  })
}

/**
 * Gift wrapping
 */

theme.cartUtils = {
  updateCart: function (callback = null) {
    fetch(`/cart.js?t=${Date.now()}`, {
      credentials: 'same-origin',
      method: 'GET'
    }).then(response => response.json())
      .then(cart => {
        theme.cartData = cart
        if (callback) {
          callback()
        }
      });
  },

  addToCart: function (formData, callback = null) {
    fetch('/cart/add.js', {
      method: 'POST',
      body: JSON.stringify(formData),
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
      .then(response => response.json())
      .then((data) => {
        if (callback) {
          callback(data)
        }
      });
  },

  changeItem: function (key, qty) {
    return fetch(window.routes.cartChangeUrl + '.js', {
      method: 'POST',
      body: JSON.stringify({id: key, quantity: qty}),
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    }).then(response => response.json())
  }
};

/**
 * Gift wrapping functionality
 */
(function() {
  'use strict';

  console.log('🎁 Gift wrapping module starting...');

  // Wait for DOM to be ready and try multiple times to find the element
  function initGiftWrap() {
    const gift = document.querySelector("#is-a-gift");
    if (!gift) {
      console.log('🎁 Gift wrap element not found yet, retrying...');
      setTimeout(initGiftWrap, 100);
      return;
    }

    const gift_id = +gift.dataset.id;
    console.log('🎁 Gift wrap ID:', gift_id);

    // Initialize gift wrap functionality
    setupGiftWrap(gift_id);
  }

  function setupGiftWrap(gift_id) {

    // Simple helper functions
    const getGiftWrapInput = () => document.querySelector('[name="attributes[gift-wrapping]"]');
    const hasEligibleItems = () => theme.cartData?.items?.some(i => i.id !== gift_id) || false;
    const hasGiftWrapInCart = () => theme.cartData?.items?.some(i => i.id === gift_id) || false;

    // Main function: Add 1 gift wrap if checked + has items, remove if not
    function manageGiftWrap() {
      // Check if cart data is available
      if (!theme.cartData) {
        console.log('🎁 ⏳ Cart data not available yet, updating...');
        theme.cartUtils.updateCart(() => {
          console.log('🎁 ✅ Cart data loaded, retrying gift wrap management');
          manageGiftWrap();
        });
        return;
      }

      const isChecked = getGiftWrapInput()?.checked;
      const shouldHave = isChecked && hasEligibleItems();
      const currentlyHas = hasGiftWrapInCart();

      console.log('🎁 Gift wrap:', {
        isChecked,
        shouldHave,
        currentlyHas,
        cartItems: theme.cartData?.items?.length || 0
      });

      if (shouldHave && !currentlyHas) {
        console.log('🎁 ➕ Adding gift wrap');
        theme.cartUtils.addToCart({id: gift_id, quantity: 1}, (data) => {
          console.log('🎁 ✅ Gift wrap added, refreshing cart');
          theme.cartUtils.updateCart(() => {
            document.documentElement.dispatchEvent(new CustomEvent('cart:refresh', { bubbles: true }));
          });
        });
      } else if (!shouldHave && currentlyHas) {
        console.log('🎁 ❌ Removing gift wrap');
        theme.cartUtils.changeItem(gift_id.toString(), 0).then((cart) => {
          console.log('🎁 ✅ Gift wrap removed, refreshing cart');
          theme.cartData = cart;
          document.documentElement.dispatchEvent(new CustomEvent('cart:refresh', { bubbles: true }));
        });
      } else {
        console.log('🎁 ✅ Gift wrap state correct');
      }
    }

    // Event handler for checkbox changes
    function handleCheckboxChange(event) {
      console.log('🎁 📋 Checkbox changed:', event.target.checked);
      theme.cartUtils.updateCart(manageGiftWrap);
    }

    // Initialize
    function initEvents() {
      const input = getGiftWrapInput();
      if (input) {
        input.removeEventListener("change", handleCheckboxChange);
        input.addEventListener("change", handleCheckboxChange);
        console.log('🎁 ✅ Event listeners attached');
      } else {
        console.log('🎁 ⚠️ Gift wrap input not found for events');
      }
    }

    // Setup
    initEvents();

    // Always try to manage gift wrap, even with empty cart
    console.log('🎁 🚀 Running initial gift wrap check...');
    manageGiftWrap();

    // Event listeners
    document.addEventListener('cart:refreshed', () => {
      console.log('🎁 🔄 Cart refreshed, re-initializing events');
      initEvents();
    });

    document.addEventListener('product:added', (evt) => {
      if (evt.detail.variant !== gift_id) {
        console.log('🎁 ➕ Product added, checking gift wrap');
        theme.cartUtils.updateCart(manageGiftWrap);
      }
    });

    console.log('🎁 ✅ Gift wrap setup complete!');
  }

  // Start the initialization process
  initGiftWrap();
})();

/**
 * Linked products show more
 */
const showMore = document.querySelector('#linked_show_more');
if (showMore) {
  showMore.addEventListener('click', function () {
    showMore.parentElement.style.maxHeight = '999px';
    showMore.parentElement.style.maxWidth = '999px';
    showMore.remove();
  })
}
