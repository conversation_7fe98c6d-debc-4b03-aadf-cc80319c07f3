/**
 * Include your custom JavaScript here.
 *
 * We also offer some hooks so you can plug your own logic. For instance, if you want to be notified when the variant
 * changes on product page, you can attach a listener to the document:
 *
 * document.addEventListener('variant:changed', function(event) {
 *   var variant = event.detail.variant; // Gives you access to the whole variant details
 * });
 *
 * You can also add a listener whenever a product is added to the cart:
 *
 * document.addEventListener('product:added', function(event) {
 *   var variant = event.detail.variant; // Get the variant that was added
 *   var quantity = event.detail.quantity; // Get the quantity that was added
 * });
 *
 * If you just want to force refresh the mini-cart without adding a specific product, you can trigger the event
 * "cart:refresh" in a similar way (in that case, passing the quantity is not necessary):
 *
 * document.documentElement.dispatchEvent(new CustomEvent('cart:refresh', {
 *   bubbles: true
 * }));
 */

const readmoreBtn = document.querySelectorAll(".readmoreBtn");

const toggleText = (i) => {
  const readmoreText = document.querySelectorAll(".more");
  const dots = document.querySelectorAll(".dots");

  if (dots[i].style.display === "none") {
    dots[i].style.display = "inline";
    readmoreBtn[i].innerHTML = window.languages.readmoreBtn;
    readmoreText[i].style.display = "none";
  } else {
    dots[i].style.display = "none";
    readmoreBtn[i].innerHTML = window.languages.readLessBtn;
    readmoreText[i].style.display = "inline";
  }

};
if (readmoreBtn.length !== 0) {
  readmoreBtn[0].addEventListener("click", () => {
    toggleText(0);
  });
  readmoreBtn[1].addEventListener("click", () => {
    toggleText(1);
  });
}


const variantListItems = document.querySelectorAll(".SizeSwatchList>.HorizontalList__Item");
const globalVariantList = window.product.variant;

globalVariantList?.forEach((element, index) => {
  if (element.available === false) {
    variantListItems[index].classList.add("line-through")
  }
});


/* terms and conditions */
const termsOverlay = document.querySelector('.terms-overlay');
const termsContent = document.querySelector('.terms-content-wrapper');
const termsDeny = document.querySelector('.terms-deny');
const termsAccept = document.querySelector('.terms-accept');

function showTerms() {
  termsOverlay.classList.add('terms-overlay--active');
  termsContent.classList.add('terms-content-wrapper--active');
}

function hideTerms() {
  termsOverlay.classList.remove('terms-overlay--active');
  termsContent.classList.remove('terms-content-wrapper--active');
}

function validateTerms(e) {
  e.preventDefault();
  const cartTerms = document.querySelector('input[name=checkbox]');

  if (cartTerms.checked) {
    window.location = '/checkout';
  } else {
    showTerms();
  }
}

termsOverlay?.addEventListener('click', () => {
  hideTerms();
});

termsAccept?.addEventListener('click', () => {
  window.location = '/checkout';
});

termsDeny?.addEventListener('click', () => {
  hideTerms();
});

const productPage = window.location.href.includes('/products/');

if (productPage) {
  var node = document.querySelector('[data-countdown]');
  var getWeekday = window.countdown.times;

  var now = new Date();
  var countdownFrom = new Date();

  var currentDay = getWeekday[now.getDay()].split(':');
  var clocktimer;

  function checkDist () {
    currentDay = getWeekday[countdownFrom.getDay()].split(':');

    countdownFrom.setHours(+currentDay[0]);
    countdownFrom.setMinutes(+currentDay[1]);
    countdownFrom.setSeconds(0);

    now = new Date();
    const dist = countdownFrom.getTime() - now;

    return dist < 0
  }

  function getDisplayTimer() {
    currentDay = getWeekday[countdownFrom.getDay()].split(':');

    countdownFrom.setHours(+currentDay[0]);
    countdownFrom.setMinutes(+currentDay[1]);
    countdownFrom.setSeconds(0);

    now = new Date();
    const dist = countdownFrom.getTime() - now;
    if (dist > 0) {
      const daysFloor = Math.floor(dist / (1000 * 60 * 60 * 24));
      const hoursFloor = Math.floor((dist % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutesFloor = Math.floor((dist % (1000 * 60 * 60)) / (1000 * 60));
      const secondsFloor = Math.floor((dist % (1000 * 60)) / 1000);

      if (daysFloor > 0) {
        node.innerHTML = window.countdown.next_day_html;
        const days = node.querySelector('[data-days]');
        days.textContent = `${daysFloor}${window.countdown.days}`;
      } else {
        node.innerHTML = window.countdown.same_day_html;
      }
      const hours = node.querySelector('[data-hours]');
      const minutes = node.querySelector('[data-minutes]');
      const seconds = node.querySelector('[data-seconds]');
      hours.textContent = `${hoursFloor}${window.countdown.hours}`;
      minutes.textContent = `${minutesFloor}${window.countdown.minutes}`;
      seconds.textContent = `${secondsFloor}${window.countdown.seconds}`;
    }
  }

  const currentDayTimeIsEmpty = !getWeekday[countdownFrom.getDay()];

  const getNextAvailable = () => {
    const check = !getWeekday[countdownFrom.getDay()];
    if (check) {
      countdownFrom.setDate(countdownFrom.getDate() + 1);
      getNextAvailable();
    }
  };

  if (currentDayTimeIsEmpty) {
    getNextAvailable();
  }

  if (checkDist()) {
    countdownFrom.setDate(countdownFrom.getDate() + 1);
    countdownFrom.setHours(0);
    countdownFrom.setMinutes(0);
    countdownFrom.setSeconds(0);
    getNextAvailable()
  }

  clocktimer = setInterval(() => {
    getDisplayTimer();
  }, 1000);
}

/**
 * Show points based on variant
 */

if (window.location.pathname.includes('/products/')) {
  document.addEventListener('variant:changed', function (evt) {
    var variant = evt.detail.variant;
    var pointsElm = document.querySelector(`.product-points[data-variant="${variant.id}"]`);
    var pointsElmArr = document.querySelectorAll(`.product-points[data-variant]`);

    pointsElmArr.forEach(i => i.classList.add('u-visually-hidden'));
    if (pointsElm) {
      pointsElm.classList.remove('u-visually-hidden');
    }
  })
}

/**
 * Gift wrapping
 */

theme.cartUtils = {
  updateCart: function (callback = null) {
    fetch(`/cart.js?t=${Date.now()}`, {
      credentials: 'same-origin',
      method: 'GET'
    }).then(response => response.json())
      .then(cart => {
        theme.cartData = cart
        if (callback) {
          callback()
        }
      });
  },

  addToCart: function (formData, callback = null) {
    fetch('/cart/add.js', {
      method: 'POST',
      body: JSON.stringify(formData),
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
      .then(response => response.json())
      .then((data) => {
        if (callback) {
          callback(data)
        }
      });
  },

  changeItem: function (key, qty) {
    return fetch(window.routes.cartChangeUrl + '.js', {
      method: 'POST',
      body: JSON.stringify({id: key, quantity: qty}),
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    }).then(response => response.json())
  },
}

const gift = document.querySelector("#is-a-gift");
if (gift) {
  const gift_id = +gift.dataset.id;

  // Helper function to get gift wrap input
  const getGiftWrapInput = () => document.querySelector('[name="attributes[gift-wrapping]"]');

  // Helper function to calculate required gift wrap quantity
  function getRequiredGiftWrapQty() {
    const eligibleItems = theme.cartData.items.filter(i => i.id !== gift_id);
    return eligibleItems.length ? eligibleItems.reduce((sum, item) => sum + item.quantity, 0) : 0;
  }

  // Helper function to get current gift wrap in cart
  const getCurrentGiftWrap = () => theme.cartData.items.find(i => i.id === gift_id);

  // Helper function to dispatch product added event
  function dispatchProductAdded(quantity) {
    document.dispatchEvent(new CustomEvent('product:added', {
      bubbles: true,
      detail: { variant: gift_id, quantity }
    }));
  }

  // Main function to manage gift wrap based on checkbox state
  function manageGiftWrap() {
    const giftWrapInput = getGiftWrapInput();
    const isChecked = giftWrapInput?.checked;
    const requiredQty = getRequiredGiftWrapQty();
    const currentGiftWrap = getCurrentGiftWrap();
    const currentQty = currentGiftWrap?.quantity || 0;

    if (!isChecked || requiredQty === 0) {
      // Remove gift wrap if unchecked or no eligible items
      if (currentQty > 0) {
        theme.cartUtils.changeItem(gift_id.toString(), 0).then((cart) => {
          theme.cartData = cart;
          document.documentElement.dispatchEvent(new CustomEvent('cart:refresh', { bubbles: true }));
        });
      }
    } else if (currentQty !== requiredQty) {
      // Add or update gift wrap quantity
      if (currentGiftWrap) {
        theme.cartUtils.changeItem(gift_id.toString(), requiredQty).then(() => dispatchProductAdded(requiredQty));
      } else {
        theme.cartUtils.addToCart({id: gift_id, quantity: requiredQty}, () => dispatchProductAdded(requiredQty));
      }
    }
  }

  // Event handler for checkbox changes
  function handleCheckboxChange(event) {
    const giftWrapInput = getGiftWrapInput();
    giftWrapInput.checked = event.target.checked;
    manageGiftWrap();
  }

  // Initialize event listeners
  function initGiftwrapEvents() {
    const giftWrapInput = getGiftWrapInput();
    if (giftWrapInput) {
      giftWrapInput.removeEventListener("change", handleCheckboxChange);
      giftWrapInput.addEventListener("change", handleCheckboxChange);
    }
  }

  // Initialize everything
  initGiftwrapEvents();

  // Clean up any unwanted gift wrap on page load
  if (theme.cartData) {
    manageGiftWrap();
  } else {
    theme.cartUtils.updateCart(manageGiftWrap);
  }

  // Re-initialize events when cart is refreshed
  document.addEventListener('cart:refreshed', initGiftwrapEvents);

  // Update gift wrap when products are added/removed
  document.addEventListener('product:added', function (evt) {
    if (evt.detail.variant !== gift_id) {
      theme.cartUtils.updateCart(manageGiftWrap);
    }
  });
}

/**
 * Linked products show more
 */
const showMore = document.querySelector('#linked_show_more');
if (showMore) {
  showMore.addEventListener('click', function () {
    showMore.parentElement.style.maxHeight = '999px';
    showMore.parentElement.style.maxWidth = '999px';
    showMore.remove();
  })
}
