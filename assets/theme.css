@charset "utf-8";

/**
 * ----------------------------------------------------------------------------------------------
 * This is a variation of Normalize.css (http://necolas.github.io/normalize.css/)
 * ----------------------------------------------------------------------------------------------
 */
/**
 * Base
 */
*,
*:before,
*:after {
  box-sizing: border-box !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  font-family: sans-serif;
  -webkit-text-size-adjust: 100%;
     -moz-text-size-adjust: 100%;
          text-size-adjust: 100%;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

body {
  margin: 0;
}

[hidden] {
  display: none !important;
}

/**
 * HTML5 display definitions
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Text-level semantic
 */
:active {
  outline: none;
}

a {
  color: inherit;
  background-color: transparent;
  text-decoration: none;
}
a:active, a:hover {
  outline: 0;
}

b,
strong {
  font-weight: bold;
}

small {
  font-size: 80%;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  font-size: inherit;
  font-weight: inherit;
}

p:last-child,
h1:last-child,
h2:last-child,
h3:last-child,
h4:last-child,
h5:last-child,
h6:last-child {
  margin-bottom: 0;
}

/**
 * Embedded content
 */
img {
  max-width: 100%;
  height: auto;
  border-style: none;
  vertical-align: top;
}

svg:not(:root) {
  overflow: hidden;
}

/**
 * Grouping content
 */
ul,
ol {
  margin: 0;
  padding: 0;
  list-style-position: inside;
}

pre {
  overflow: auto;
}

code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 16px;
}

/**
 * Forms
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button,
input[type=submit] {
  padding: 0;
  overflow: visible;
  background: none;
  border: none;
  border-radius: 0;
  -webkit-appearance: none;
}

button,
select {
  text-transform: none;
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled],
html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
  border-radius: 0;
}

input[type=checkbox],
input[type=radio] {
  box-sizing: border-box;
  padding: 0;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  height: auto;
}

input[type=search] {
  -webkit-appearance: none;
  box-sizing: content-box;
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: inherit;
}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  color: inherit;
}

input::placeholder,
textarea::placeholder {
  color: inherit;
}

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 6px 10px 12px;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

/**
 * Tables
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

/*------------------------------------
 *   Grid LAYOUT
 *------------------------------------*/

.Grid {
  display: block;
  list-style: none;
  padding: 0;
  margin: 0 0 0 -24px;
  font-size: 0;
}

.Grid__Cell {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
  padding: 0 0 0 24px;
  margin: 0;
  vertical-align: top;
  font-size: 1rem;
}

.Grid--center {
  text-align: center;
}

.Grid--center > .Grid__Cell {
  text-align: left;
}

.Grid__Cell--center {
  display: block;
  margin: 0 auto;
}

.Grid--right {
  text-align: right;
}

.Grid--right > .Grid__Cell {
  text-align: left;
}

.Grid--middle > .Grid__Cell {
  vertical-align: middle;
}

.Grid--bottom > .Grid__Cell {
  vertical-align: bottom;
}

.Grid--m {
  margin-left: -30px;
}

.Grid--m > .Grid__Cell {
  padding-left: 30px;
}

.Grid--l {
  margin-left: -50px;
}

.Grid--l > .Grid__Cell {
  padding-left: 50px;
}

.Grid--xl {
  margin-left: -50px;
}

.Grid--xl > .Grid__Cell {
  padding-left: 50px;
}

@media screen and (min-width: 1140px) {
  .Grid--m {
    margin-left: -60px;
  }

  .Grid--m > .Grid__Cell {
    padding-left: 60px;
  }

  .Grid--l {
    margin-left: -80px;
  }

  .Grid--l > .Grid__Cell {
    padding-left: 80px;
  }

  .Grid--xl {
    margin-left: -100px;
  }

  .Grid--xl > .Grid__Cell {
    padding-left: 100px;
  }
}

/*------------------------------------*\
    Grid WIDTHS
\*------------------------------------*/

.\31\/2, .\32\/4, .\36\/12 {
  width: 50%;
}

.\31\/3, .\34\/12 {
  width: 33.33333%;
}

.\32\/3, .\38\/12 {
  width: 66.66667%;
}

.\31\/4, .\33\/12 {
  width: 25%;
}

.\33\/4, .\39\/12 {
  width: 75%;
}

.\31\/12 {
  width: 8.33333%;
}

.\32\/12 {
  width: 16.66667%;
}

.\35\/12 {
  width: 41.66667%;
}

.\37\/12 {
  width: 58.33333%;
}

.\31\30\/12 {
  width: 83.33333%;
}

.\31\31\/12 {
  width: 91.66667%;
}

@media screen and (max-width: 640px) {
  .hidden-phone {
    display: none !important;
  }

  .\31\/2--phone, .\32\/4--phone, .\36\/12--phone {
    width: 50%;
  }

  .\31\/3--phone, .\34\/12--phone {
    width: 33.33333%;
  }

  .\32\/3--phone, .\38\/12--phone {
    width: 66.66667%;
  }

  .\31\/4--phone, .\33\/12--phone {
    width: 25%;
  }

  .\33\/4--phone, .\39\/12--phone {
    width: 75%;
  }

  .\31\/12--phone {
    width: 8.33333%;
  }

  .\32\/12--phone {
    width: 16.66667%;
  }

  .\35\/12--phone {
    width: 41.66667%;
  }

  .\37\/12--phone {
    width: 58.33333%;
  }

  .\31\30\/12--phone {
    width: 83.33333%;
  }

  .\31\31\/12--phone {
    width: 91.66667%;
  }
}

@media screen and (min-width: 641px) and (max-width: 1007px) {
  .hidden-tablet {
    display: none !important;
  }

  .\31\/2--tablet, .\32\/4--tablet, .\36\/12--tablet {
    width: 50%;
  }

  .\31\/3--tablet, .\34\/12--tablet {
    width: 33.33333%;
  }

  .\32\/3--tablet, .\38\/12--tablet {
    width: 66.66667%;
  }

  .\31\/4--tablet, .\33\/12--tablet {
    width: 25%;
  }

  .\33\/4--tablet, .\39\/12--tablet {
    width: 75%;
  }

  .\31\/12--tablet {
    width: 8.33333%;
  }

  .\32\/12--tablet {
    width: 16.66667%;
  }

  .\35\/12--tablet {
    width: 41.66667%;
  }

  .\37\/12--tablet {
    width: 58.33333%;
  }

  .\31\30\/12--tablet {
    width: 83.33333%;
  }

  .\31\31\/12--tablet {
    width: 91.66667%;
  }
}

@media screen and (min-width: 641px) {
  .hidden-tablet-and-up {
    display: none !important;
  }

  .\31\/2--tablet-and-up, .\32\/4--tablet-and-up, .\36\/12--tablet-and-up {
    width: 50%;
  }

  .\31\/3--tablet-and-up, .\34\/12--tablet-and-up {
    width: 33.33333%;
  }

  .\32\/3--tablet-and-up, .\38\/12--tablet-and-up {
    width: 66.66667%;
  }

  .\31\/4--tablet-and-up, .\33\/12--tablet-and-up {
    width: 25%;
  }

  .\33\/4--tablet-and-up, .\39\/12--tablet-and-up {
    width: 75%;
  }

  .\31\/12--tablet-and-up {
    width: 8.33333%;
  }

  .\32\/12--tablet-and-up {
    width: 16.66667%;
  }

  .\35\/12--tablet-and-up {
    width: 41.66667%;
  }

  .\37\/12--tablet-and-up {
    width: 58.33333%;
  }

  .\31\30\/12--tablet-and-up {
    width: 83.33333%;
  }

  .\31\31\/12--tablet-and-up {
    width: 91.66667%;
  }
}

@media screen and (max-width: 1007px) {
  .hidden-pocket {
    display: none !important;
  }

  .\31\/2--pocket, .\32\/4--pocket, .\36\/12--pocket {
    width: 50%;
  }

  .\31\/3--pocket, .\34\/12--pocket {
    width: 33.33333%;
  }

  .\32\/3--pocket, .\38\/12--pocket {
    width: 66.66667%;
  }

  .\31\/4--pocket, .\33\/12--pocket {
    width: 25%;
  }

  .\33\/4--pocket, .\39\/12--pocket {
    width: 75%;
  }

  .\31\/12--pocket {
    width: 8.33333%;
  }

  .\32\/12--pocket {
    width: 16.66667%;
  }

  .\35\/12--pocket {
    width: 41.66667%;
  }

  .\37\/12--pocket {
    width: 58.33333%;
  }

  .\31\30\/12--pocket {
    width: 83.33333%;
  }

  .\31\31\/12--pocket {
    width: 91.66667%;
  }
}

@media screen and (min-width: 1008px) and (max-width: 1139px) {
  .hidden-lap {
    display: none !important;
  }

  .\31\/2--lap, .\32\/4--lap, .\36\/12--lap {
    width: 50%;
  }

  .\31\/3--lap, .\34\/12--lap {
    width: 33.33333%;
  }

  .\32\/3--lap, .\38\/12--lap {
    width: 66.66667%;
  }

  .\31\/4--lap, .\33\/12--lap {
    width: 25%;
  }

  .\33\/4--lap, .\39\/12--lap {
    width: 75%;
  }

  .\31\/12--lap {
    width: 8.33333%;
  }

  .\32\/12--lap {
    width: 16.66667%;
  }

  .\35\/12--lap {
    width: 41.66667%;
  }

  .\37\/12--lap {
    width: 58.33333%;
  }

  .\31\30\/12--lap {
    width: 83.33333%;
  }

  .\31\31\/12--lap {
    width: 91.66667%;
  }
}

@media screen and (min-width: 1008px) {
  .hidden-lap-and-up {
    display: none !important;
  }

  .\31\/2--lap-and-up, .\32\/4--lap-and-up, .\36\/12--lap-and-up {
    width: 50%;
  }

  .\31\/3--lap-and-up, .\34\/12--lap-and-up {
    width: 33.33333%;
  }

  .\32\/3--lap-and-up, .\38\/12--lap-and-up {
    width: 66.66667%;
  }

  .\31\/4--lap-and-up, .\33\/12--lap-and-up {
    width: 25%;
  }

  .\33\/4--lap-and-up, .\39\/12--lap-and-up {
    width: 75%;
  }

  .\31\/12--lap-and-up {
    width: 8.33333%;
  }

  .\32\/12--lap-and-up {
    width: 16.66667%;
  }

  .\35\/12--lap-and-up {
    width: 41.66667%;
  }

  .\37\/12--lap-and-up {
    width: 58.33333%;
  }

  .\31\30\/12--lap-and-up {
    width: 83.33333%;
  }

  .\31\31\/12--lap-and-up {
    width: 91.66667%;
  }
}

@media screen and (min-width: 1140px) {
  .hidden-desk {
    display: none !important;
  }

  .\31\/2--desk, .\32\/4--desk, .\36\/12--desk {
    width: 50%;
  }

  .\31\/3--desk, .\34\/12--desk {
    width: 33.33333%;
  }

  .\32\/3--desk, .\38\/12--desk {
    width: 66.66667%;
  }

  .\31\/4--desk, .\33\/12--desk {
    width: 25%;
  }

  .\33\/4--desk, .\39\/12--desk {
    width: 75%;
  }

  .\31\/12--desk {
    width: 8.33333%;
  }

  .\32\/12--desk {
    width: 16.66667%;
  }

  .\35\/12--desk {
    width: 41.66667%;
  }

  .\37\/12--desk {
    width: 58.33333%;
  }

  .\31\30\/12--desk {
    width: 83.33333%;
  }

  .\31\31\/12--desk {
    width: 91.66667%;
  }
}
/* Create each media query */
@media screen and (min-width: 1500px) {
  .hidden-widescreen {
    display: none !important;
  }

  .\31\/2--widescreen, .\32\/4--widescreen, .\36\/12--widescreen {
    width: 50%;
  }

  .\31\/3--widescreen, .\34\/12--widescreen {
    width: 33.33333%;
  }

  .\32\/3--widescreen, .\38\/12--widescreen {
    width: 66.66667%;
  }

  .\31\/4--widescreen, .\33\/12--widescreen {
    width: 25%;
  }

  .\33\/4--widescreen, .\39\/12--widescreen {
    width: 75%;
  }

  .\31\/12--widescreen {
    width: 8.33333%;
  }

  .\32\/12--widescreen {
    width: 16.66667%;
  }

  .\35\/12--widescreen {
    width: 41.66667%;
  }

  .\37\/12--widescreen {
    width: 58.33333%;
  }

  .\31\30\/12--widescreen {
    width: 83.33333%;
  }

  .\31\31\/12--widescreen {
    width: 91.66667%;
  }
}
/*! PhotoSwipe main CSS by Dmitry Semenov | photoswipe.com | MIT license */
/*
	Styles for basic PhotoSwipe functionality (sliding area, open/close transitions)
*/
.pswp {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
  touch-action: none;
  z-index: 1500;
  -webkit-text-size-adjust: 100%;
  -webkit-backface-visibility: hidden;
  outline: none;
}

.pswp img {
  max-width: none;
}

.pswp--animate_opacity {
  opacity: 0.001;
  /* 0.001, because opacity:0 doesn't trigger Paint action, which causes lag at start of transition */
  will-change: opacity;
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.22, 1);
}

.pswp--open {
  display: block;
}

.pswp--zoom-allowed .pswp__img {
  cursor: var(--cursor-zoom-in-svg) 18 18, zoom-in;
  cursor: -webkit-image-set(var(--cursor-zoom-in-svg) 1x, var(--cursor-zoom-in-2x-svg) 2x) 18 18, zoom-in;
}

.pswp--zoomed-in .pswp__img {
  cursor: -webkit-grab;
  cursor: grab;
}

.pswp--dragging .pswp__img {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

.pswp__bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: var(--background);
  opacity: 0;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  will-change: opacity;
}

.pswp__scroll-wrap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.pswp__container,
.pswp__zoom-wrap {
  touch-action: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* Prevent selection and tap highlights */
.pswp__container,
.pswp__img {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

.pswp__container {
  transition: transform 0.7s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.pswp__zoom-wrap {
  position: absolute;
  width: 100%;
  transform-origin: left top;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.22, 1);
}

.pswp__bg {
  will-change: opacity;
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.22, 1);
}

.pswp--animated-in .pswp__bg,
.pswp--animated-in .pswp__zoom-wrap {
  transition: none;
}

.pswp__container,
.pswp__zoom-wrap {
  -webkit-backface-visibility: hidden;
}

.pswp__item {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
}

.pswp__img {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
}

.pswp__img--placeholder {
  -webkit-backface-visibility: hidden;
}

.pswp__img--placeholder--blank {
  background: var(--background);
}

.pswp--ie .pswp__img {
  width: 100% !important;
  height: auto !important;
  left: 0;
  top: 0;
}

/**
 * Custom UI
 */
.pswp__ui {
  position: absolute;
  visibility: hidden;
  width: 100%;
  bottom: 50px;
  opacity: 0;
  transform: translateY(35px);
  left: 0;
  text-align: center;
  transition: all 0.15s ease-in-out;
}

.pswp__button[disabled] {
  opacity: 0;
  pointer-events: none;
}

.pswp--animated-in .pswp__ui {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.pswp--animated-in .pswp__ui--hidden {
  visibility: hidden;
  opacity: 0;
}

.pswp__button--close {
  margin: 0 18px;
}

.pswp__button svg {
  pointer-events: none;
}

.pswp__error-msg {
  position: absolute;
  top: 40%;
  margin: 0 15px;
  padding: 8px 15px;
  background: #e6554d;
  color: #ffffff;
  text-align: center;
}

.js .no-js,
.no-js .hide-no-js {
  display: none !important;
}

.no-scroll {
  overflow: hidden;
}

body:not(.is-tabbing) [tabindex]:focus,
body:not(.is-tabbing) button:focus,
body:not(.is-tabbing) input:focus,
body:not(.is-tabbing) select:focus,
body:not(.is-tabbing) textarea:focus {
  outline: none;
}

[data-scrollable] {
  overflow: auto;
  /*-webkit-overflow-scrolling: touch;*/
}

.Container {
  margin: 0 auto;
  padding: 0 24px;
}

.Container--narrow {
  max-width: 1420px;
}

.Container--extraNarrow {
  max-width: 800px;
}

/*
   This fixes an issue in IE10/11 when using min-height in flex children
   @info: https://github.com/philipwalton/flexbugs#3-min-height-on-a-flex-container-wont-apply-to-its-flex-items
*/
.FlexboxIeFix {
  display: flex;
  flex-direction: row;
}

@media screen and (min-width: 641px) {
  .Container {
    padding: 0 50px;
  }
}

@media screen and (min-width: 1140px) {
  .Container {
    padding: 0 80px;
  }
}
/**
 * Very general typographic rules that are applied site wide
 */
html {
  font-size: var(--base-text-font-size);
}

body {
  font-family: var(--text-font-family);
  font-weight: var(--text-font-weight);
  font-style: var(--text-font-style);
  color: var(--text-color);
  background: var(--background);
  line-height: 1.65;
}

.Link {
  transition: color 0.2s ease-in-out, opacity 0.2s ease-in-out;
}

.supports-hover .Link--primary:hover,
.Link--primary.is-active {
  color: var(--text-color);
}

.supports-hover .Link--secondary:hover,
.Link--secondary.is-active {
  color: var(--text-color-light);
}

.Link--underline {
  position: relative;
  display: inline-block;
}

.Link--underline::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  bottom: 0;
  background: currentColor;
  transform: scale(1, 1);
  transform-origin: left center;
  transition: transform 0.2s ease-in-out;
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .Link--underline:hover::before {
    transform: scale(0, 1);
  }
}

.Link--underlineShort::before {
  width: 100%;
}

.features--heading-uppercase .Link--underlineShort::before {
  width: calc(100% - 0.2em);
}

.Link--underlineNative {
  text-decoration: underline;
  text-underline-position: under;
}

.Heading {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  color: var(--heading-color);
  transition: color 0.2s ease-in-out;
}

.features--heading-uppercase .Heading {
  letter-spacing: 0.2em;
  text-transform: uppercase;
}

.Text--subdued {
  color: var(--text-color-light);
}

.Text--alignCenter {
  text-align: center !important;
}

.Text--alignRight {
  text-align: right !important;
}

.Icon-Wrapper--clickable {
  position: relative;
  background: transparent; /* This is used to increase the clickable area */
}

.Icon-Wrapper--clickable::before {
  position: absolute;
  content: "";
  top: -8px;
  right: -12px;
  left: -12px;
  bottom: -8px;
  transform: translateZ(0); /* Needed to avoid a glitch on iOS */
}

.Icon {
  display: inline-block;
  height: 1em;
  width: 1em;
  fill: currentColor;
  vertical-align: middle;
  stroke-width: 1px;
  background: none;
  pointer-events: none;
}

.u-visually-hidden {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

.features--heading-small .u-h1,
.features--heading-small .Rte h1 {
  font-size: 20px;
}

.features--heading-small .u-h2,
.features--heading-small .Rte h2 {
  font-size: 18px;
}

.features--heading-small .u-h3,
.features--heading-small .Rte h3 {
  font-size: 16px;
}

.features--heading-small .u-h4,
.features--heading-small .Rte h4 {
  font-size: 15px;
}

.features--heading-small .u-h5,
.features--heading-small .Rte h5 {
  font-size: 13px;
}

.features--heading-small .u-h6,
.features--heading-small .Rte h6 {
  font-size: 12px;
}

.features--heading-small .u-h7 {
  font-size: 11px;
}

.features--heading-small .u-h8 {
  font-size: 10px;
}

.features--heading-normal .u-h1,
.features--heading-normal .Rte h1 {
  font-size: 22px;
}

.features--heading-normal .u-h2,
.features--heading-normal .Rte h2 {
  font-size: 20px;
}

.features--heading-normal .u-h3,
.features--heading-normal .Rte h3 {
  font-size: 18px;
}

.features--heading-normal .u-h4,
.features--heading-normal .Rte h4 {
  font-size: 16px;
}

.features--heading-normal .u-h5,
.features--heading-normal .Rte h5 {
  font-size: 14px;
}

.features--heading-normal .u-h6,
.features--heading-normal .Rte h6 {
  font-size: 13px;
}

.features--heading-normal .u-h7 {
  font-size: 12px;
}

.features--heading-normal .u-h8 {
  font-size: 12px;
}

.features--heading-large .u-h1,
.features--heading-large .Rte h1 {
  font-size: 24px;
}

.features--heading-large .u-h2,
.features--heading-large .Rte h2 {
  font-size: 22px;
}

.features--heading-large .u-h3,
.features--heading-large .Rte h3 {
  font-size: 20px;
}

.features--heading-large .u-h4,
.features--heading-large .Rte h4 {
  font-size: 18px;
}

.features--heading-large .u-h5,
.features--heading-large .Rte h5 {
  font-size: 16px;
}

.features--heading-large .u-h6,
.features--heading-large .Rte h6 {
  font-size: 15px;
}

.features--heading-large .u-h7 {
  font-size: 13px;
}

.features--heading-large .u-h8 {
  font-size: 13px;
}

@media screen and (min-width: 641px) {
  .features--heading-small .u-h1,
  .features--heading-small .Rte h1 {
    font-size: 20px;
  }

  .features--heading-small .u-h2,
  .features--heading-small .Rte h2 {
    font-size: 18px;
  }

  .features--heading-small .u-h3,
  .features--heading-small .Rte h3 {
    font-size: 18px;
  }

  .features--heading-small .u-h4,
  .features--heading-small .Rte h4 {
    font-size: 16px;
  }

  .features--heading-small .u-h5,
  .features--heading-small .Rte h5 {
    font-size: 13px;
  }

  .features--heading-small .u-h6,
  .features--heading-small .Rte h6 {
    font-size: 12px;
  }

  .features--heading-small .u-h7 {
    font-size: 11px;
  }

  .features--heading-small .u-h8 {
    font-size: 10px;
  }

  .features--heading-normal .u-h1,
  .features--heading-normal .Rte h1 {
    font-size: 22px;
  }

  .features--heading-normal .u-h2,
  .features--heading-normal .Rte h2 {
    font-size: 20px;
  }

  .features--heading-normal .u-h3,
  .features--heading-normal .Rte h3 {
    font-size: 20px;
  }

  .features--heading-normal .u-h4,
  .features--heading-normal .Rte h4 {
    font-size: 17px;
  }

  .features--heading-normal .u-h5,
  .features--heading-normal .Rte h5 {
    font-size: 14px;
  }

  .features--heading-normal .u-h6,
  .features--heading-normal .Rte h6 {
    font-size: 13px;
  }

  .features--heading-normal .u-h7 {
    font-size: 12px;
  }

  .features--heading-normal .u-h8 {
    font-size: 12px;
  }

  .features--heading-large .u-h1,
  .features--heading-large .Rte h1 {
    font-size: 24px;
  }

  .features--heading-large .u-h2,
  .features--heading-large .Rte h2 {
    font-size: 22px;
  }

  .features--heading-large .u-h3,
  .features--heading-large .Rte h3 {
    font-size: 20px;
  }

  .features--heading-large .u-h4,
  .features--heading-large .Rte h4 {
    font-size: 19px;
  }

  .features--heading-large .u-h5,
  .features--heading-large .Rte h5 {
    font-size: 16px;
  }

  .features--heading-large .u-h6,
  .features--heading-large .Rte h6 {
    font-size: 15px;
  }

  .features--heading-large .u-h7 {
    font-size: 13px;
  }

  .features--heading-large .u-h8 {
    font-size: 13px;
  }
}

.js .features--show-page-transition .PageTransition {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: var(--background);
  z-index: 1000;
  opacity: 1;
  visibility: visible;
  pointer-events: none;
  transition: all 0.25s ease;
}
/**
 * ----------------------------------------------------------------------------
 * Standard button
 * ----------------------------------------------------------------------------
 */

.Button,
.shopify-payment-button__button--unbranded,
input.spr-button-primary,
.spr-summary-actions-newreview,
.spr-button-primary:not(input),
.spr-pagination-prev > a,
.spr-pagination-next > a {
  position: relative;
  display: inline-block;
  padding: 14px 28px;
  line-height: normal;
  border: 1px solid transparent;
  border-radius: 0;
  text-transform: uppercase;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  text-align: center;
  letter-spacing: 0.2em;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  background-color: transparent;
  transition: color 0.45s cubic-bezier(0.785, 0.135, 0.15, 0.86), border 0.45s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: 1;
  -webkit-tap-highlight-color: initial;
}

.Button::before,
.shopify-payment-button__button--unbranded::before,
input.spr-button-primary::before,
.spr-summary-actions-newreview.spr-summary-actions-newreview::before,
.spr-button-primary:not(input)::before,
.spr-pagination-prev > a::before,
.spr-pagination-next > a::before {
  position: absolute;
  content: "";
  display: block;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  transform: scale(1, 1);
  transform-origin: left center;
  z-index: -1;
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .features--show-button-transition .Button:not([disabled])::before,
  .features--show-button-transition .shopify-payment-button__button--unbranded:not([disabled])::before,
  .features--show-button-transition input.spr-button-primary:not([disabled])::before,
  .features--show-button-transition .spr-summary-actions-newreview:not([disabled])::before,
  .features--show-button-transition .spr-button-primary:not(input):not([disabled])::before,
  .features--show-button-transition .spr-pagination-prev > a:not([disabled])::before,
  .features--show-button-transition .spr-pagination-next > a:not([disabled])::before {
    transition: transform 0.45s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  }

  .features--show-button-transition .Button:not([disabled]):hover::before,
  .features--show-button-transition .shopify-payment-button__button--unbranded:not([disabled]):hover::before,
  .features--show-button-transition input.spr-button-primary:not([disabled]):hover::before,
  .features--show-button-transition .spr-summary-actions-newreview:not([disabled]):hover::before,
  .features--show-button-transition .spr-button-primary:not(input):not([disabled]):hover::before,
  .features--show-button-transition .spr-pagination-prev > a:not([disabled]):hover::before,
  .features--show-button-transition .spr-pagination-next > a:not([disabled]):hover::before {
    transform-origin: right center;
    transform: scale(0, 1);
  }
}

.Button[disabled],
.shopify-payment-button__button--unbranded[disabled],
input.spr-button-primary[disabled],
.spr-summary-actions-newreview[disabled],
.spr-button-primary:not(input)[disabled],
.spr-pagination-prev > a[disabled],
.spr-pagination-next > a[disabled] {
  cursor: not-allowed;
}

.Button--primary,
.shopify-payment-button__button--unbranded,
.spr-summary-actions-newreview,
.spr-button-primary:not(input) {
  color: var(--button-text-color);
  border-color: var(--button-background);
}

.Button--primary::before,
.shopify-payment-button__button--unbranded::before,
.spr-summary-actions-newreview::before,
.spr-button-primary:not(input)::before {
  background-color: var(--button-background);
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .features--show-button-transition .Button--primary:not([disabled]):hover,
  .features--show-button-transition .shopify-payment-button__button--unbranded:not([disabled]):hover,
  .features--show-button-transition .spr-summary-actions-newreview:not([disabled]):hover,
  .features--show-button-transition .spr-button-primary:not(input):not([disabled]):hover {
    color: var(--button-background);
    background-color: transparent;
  }
}

.Button--secondary,
.spr-pagination-prev > a,
.spr-pagination-next > a {
  color: var(--text-color-light);
  border: 1px solid rgba(var(--text-color-light-rgb), 0.2);
}

.Button--secondary::before,
.spr-pagination-prev > a::before,
.spr-pagination-next > a::before {
  /* background-color: var(--button-background); */
  background-color: transparent;
  transform-origin: right center;
  border: 1px solid var(--button-background);
  /* transform: scale(0, 1); */
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .features--show-button-transition .Button--secondary:not([disabled]):hover,
  .features--show-button-transition .spr-pagination-prev > a:not([disabled]):hover,
  .features--show-button-transition .spr-pagination-next > a:not([disabled]):hover {
    color: var(--button-text-color);
    border-color: var(--button-background);
  }

  .features--show-button-transition .Button--secondary:not([disabled]):hover::before,
  .features--show-button-transition .spr-pagination-prev > a:not([disabled]):hover::before,
  .features--show-button-transition .spr-pagination-next > a:not([disabled]):hover::before {
    transform-origin: left center;
    transform: scale(1, 1);
  }
}

.Button--full {
  width: 100%;
}

.Button--stretched {
  padding-left: 40px;
  padding-right: 40px;
}

.Button--small {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 10px));
  padding: 12px 24px;
}

.Button__SeparatorDot {
  display: inline-block;
  margin: 0 18px;
  content: "";
  height: 3px;
  width: 3px;
  border-radius: 100%;
  background: currentColor;
}

.ButtonWrapper {
  text-align: center;
}

/* We allow those buttons to have secondary state. The secondary state is displayed (for instance during loading time) when
   the class "Button--secondaryState" is added */
.Button__PrimaryState,
.Button__SecondaryState {
  display: block;
  transition: transform 0.4s cubic-bezier(0.75, 0, 0.125, 1), opacity 0.4s cubic-bezier(0.75, 0, 0.125, 1);
}

.Button__SecondaryState {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 100%;
  text-align: center;
  opacity: 0;
  transform: translate(-50%, 100%);
}

.Button--secondaryState .Button__PrimaryState {
  opacity: 0;
  transform: translateY(-100%);
}
.Button--secondaryState .Button__SecondaryState {
  opacity: 1;
  transform: translate(-50%, -50%);
}

/**
 * ----------------------------------------------------------------------------
 * Button group
 * ----------------------------------------------------------------------------
 */

.ButtonGroup {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin: -12px;
}

.ButtonGroup__Item {
  margin: 12px;
}

.ButtonGroup__Item--expand {
  flex: 1 1 0;
}

.ButtonGroup--spacingSmall {
  margin: -8px;
}
.ButtonGroup--spacingSmall .ButtonGroup__Item {
  margin: 8px;
}

.ButtonGroup--sameSize .ButtonGroup__Item {
  flex: 0 1 auto;
  white-space: nowrap;
  max-width: 245px;
}

@supports ((-o-object-fit: cover) or (object-fit: cover)) {
  .ButtonGroup--sameSize .ButtonGroup__Item {
    flex: 1 1 0;
    /* There is a bug in IE11 so we're forced to use this trick */
  }
}

@media screen and (min-width: 641px) {
  @supports (display: inline-grid) {
    .ButtonGroup--sameSize {
      display: inline-grid;
      grid-template-columns: 1fr 1fr;
    }

    .ButtonGroup--sameSize .ButtonGroup__Item {
      max-width: none;
    }
  }
}

/**
 * ----------------------------------------------------------------------------
 * Round button
 *
 * Those buttons are not really button like the others, but they are used in
 * lot of different places to hold things like icons
 * ----------------------------------------------------------------------------
 */

.RoundButton,
.flickity-prev-next-button {
  position: relative;
  width: 45px;
  height: 45px;
  border: none;
  border-radius: 50%;
  background: var(--button-text-color);
  color: var(--button-background);
  cursor: pointer;
  text-align: center;
  z-index: 1;
  box-shadow: 0 2px 10px rgba(54, 54, 54, 0.15);
  vertical-align: middle;
  line-height: 0;
  transform: scale(1.001); /* Avoid rounding error during animation in Chrome */
  transition: background 0.15s ease-in-out, opacity 0.15s ease-in-out, transform 0.2s ease-in-out, color 0.2s ease-in-out;
  overflow: hidden;
}

.RoundButton.is-active,
.flickity-prev-next-button.is-active {
  background: var(--button-background);
  color: var(--button-text-color);
  outline: none;
}

.RoundButton svg,
.flickity-prev-next-button svg {
  height: 15px;
  width: 15px;
  fill: currentColor;
}

.RoundButton--small {
  width: 35px;
  height: 35px;
}
.RoundButton--small svg {
  height: 14px;
  width: 14px;
}

.RoundButton--medium {
  width: 50px;
  height: 50px;
}

.RoundButton--medium svg {
  height: 18px;
  width: 18px;
}

.RoundButton--large {
  width: 55px;
  height: 55px;
}
.RoundButton--large svg {
  height: 15px;
  width: 15px;
}

.RoundButton--flat {
  box-shadow: 0 1px 5px rgba(54, 54, 54, 0.15);
  color: rgba(var(--button-background-rgb), 0.5);
}

/* We allow those buttons to have secondary state. The secondary state is displayed (for instance during loading time) when
   the class "RoundButton--withSecondaryState" is added */
.RoundButton__PrimaryState,
.RoundButton__SecondaryState {
  display: block;
  transition: transform 0.4s cubic-bezier(0.75, 0, 0.125, 1), opacity 0.4s cubic-bezier(0.75, 0, 0.125, 1);
}

.RoundButton__SecondaryState {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 100%;
  text-align: center;
  opacity: 0;
  transform: translate(-50%, 100%);
}

.RoundButton--secondaryState .RoundButton__PrimaryState {
  opacity: 0;
  transform: translateY(-100%);
}

.RoundButton--secondaryState .RoundButton__SecondaryState {
  opacity: 1;
  transform: translate(-50%, -50%);
}

/* Animation states */
@-webkit-keyframes buttonFromLeftToRight {
  0% {
    transform: translateX(0%);
  }

  25% {
    opacity: 0;
    transform: translateX(100%);
  }

  50% {
    opacity: 0;
    transform: translateX(-100%);
  }

  75% {
    opacity: 1;
    transform: translateX(0%);
  }
}
@keyframes buttonFromLeftToRight {
  0% {
    transform: translateX(0%);
  }

  25% {
    opacity: 0;
    transform: translateX(100%);
  }

  50% {
    opacity: 0;
    transform: translateX(-100%);
  }

  75% {
    opacity: 1;
    transform: translateX(0%);
  }
}

@-webkit-keyframes buttonFromRightToLeft {
  0% {
    transform: translateX(0%);
  }

  25% {
    opacity: 0;
    transform: translateX(-100%);
  }

  50% {
    opacity: 0;
    transform: translateX(100%);
  }

  75% {
    opacity: 1;
    transform: translateX(0%);
  }
}

@keyframes buttonFromRightToLeft {
  0% {
    transform: translateX(0%);
  }

  25% {
    opacity: 0;
    transform: translateX(-100%);
  }

  50% {
    opacity: 0;
    transform: translateX(100%);
  }

  75% {
    opacity: 1;
    transform: translateX(0%);
  }
}

@-webkit-keyframes buttonFromTopToBottom {
  0% {
    transform: translateY(0%);
  }

  25% {
    opacity: 0;
    transform: translateY(100%);
  }

  50% {
    opacity: 0;
    transform: translateY(-100%);
  }

  75% {
    opacity: 1;
    transform: translateY(0%);
  }
}

@keyframes buttonFromTopToBottom {
  0% {
    transform: translateY(0%);
  }

  25% {
    opacity: 0;
    transform: translateY(100%);
  }

  50% {
    opacity: 0;
    transform: translateY(-100%);
  }

  75% {
    opacity: 1;
    transform: translateY(0%);
  }
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .RoundButton:hover {
    transform: scale(1.1);
  }

  .RoundButton--small:hover {
    transform: scale(1.15);
  }

  .RoundButton:not([aria-expanded=true]):hover {
    color: var(--button-background);
  }

  .RoundButton[data-animate-left]:hover svg,
  .flickity-prev-next-button.previous:hover svg {
    -webkit-animation: buttonFromRightToLeft 0.5s ease-in-out forwards;
            animation: buttonFromRightToLeft 0.5s ease-in-out forwards;
  }

  .RoundButton[data-animate-right]:hover svg,
  .flickity-prev-next-button.next:hover svg {
    -webkit-animation: buttonFromLeftToRight 0.5s ease-in-out forwards;
            animation: buttonFromLeftToRight 0.5s ease-in-out forwards;
  }

  .RoundButton[data-animate-bottom]:hover svg {
    -webkit-animation: buttonFromTopToBottom 0.5s ease-in-out forwards;
            animation: buttonFromTopToBottom 0.5s ease-in-out forwards;
  }
}
/**
 * ----------------------------------------------------------------------------------------------
 * Flickity styles
 * ----------------------------------------------------------------------------------------------
 */

.flickity-enabled {
  position: relative;
}

body:not(.is-tabbing) .flickity-enabled:focus {
  outline: none;
}

.flickity-viewport {
  overflow: hidden;
  position: relative;
  height: auto;
}

.flickity-slider {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* draggable */
.flickity-enabled {
  -webkit-tap-highlight-color: transparent;
}

.flickity-enabled.is-draggable {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.flickity-enabled.is-draggable .flickity-viewport {
  cursor: -webkit-grab;
  cursor: grab;
}

.flickity-enabled.is-draggable .flickity-viewport.is-pointer-down {
  cursor: -webkit-grabbing;
  cursor: grabbing;
}

/* ---- previous/next buttons ---- */
.flickity-prev-next-button {
  position: absolute;
}

.flickity-prev-next-button[disabled] {
  opacity: 0;
}

.flickity-prev-next-button svg {
  width: auto;
  height: 18px;
  stroke-width: 1.5px;
  stroke: currentColor;
}

/* ---- page dots ---- */
.flickity-page-dots {
  width: 100%;
  padding: 0;
  margin: 22px 0 0 0;
  list-style: none;
  text-align: center;
}

.flickity-page-dots .dot {
  position: relative;
  display: inline-block;
  width: 9px;
  height: 9px;
  margin: 0 6px;
  border-radius: 50%;
  cursor: pointer;
  background: transparent;
  border: 1px solid var(--border-color);
  transition: color 0.2s ease-in-out, background 0.2s ease-in-out; /* This is used to increase the clickable area */
}

.flickity-page-dots .dot::before {
  position: absolute;
  content: "";
  top: -6px;
  right: -6px;
  left: -6px;
  bottom: -6px;
}

.flickity-page-dots .dot.is-selected {
  background: currentColor;
  border-color: currentColor;
}

@media screen and (min-width: 641px) {
  .flickity-page-dots .dot {
    width: 10px;
    height: 10px;
    margin: 0 7px;
    border-width: 2px;
  }
}

/**
 * ----------------------------------------------------------------------------------------------
 * Default styling for site-wide carousel
 * ----------------------------------------------------------------------------------------------
 */

.Carousel {
  position: relative;
}

.Carousel--fixed,
.Carousel--fixed .flickity-viewport,
.Carousel--fixed .Carousel__Cell {
  height: 100%;
}

/* This allows to hide cells that are not first one until slider is loaded */
.Carousel:not(.flickity-enabled) .Carousel__Cell:not(.is-selected) {
  display: none;
}

.Carousel__Cell {
  display: block;
  width: 100%;
}

.js .Carousel--fadeIn .flickity-slider {
  transform: none !important;
}

.js .Carousel--fadeIn .Carousel__Cell {
  left: 0 !important;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.js .Carousel--fadeIn .Carousel__Cell.is-selected {
  opacity: 1;
  visibility: visible;
  transition-delay: 0s;
}

.Carousel--insideDots .flickity-page-dots {
  position: absolute;
  width: auto;
  margin: 0;
  bottom: 20px;
  left: 20px;
}

.Carousel--insideDots .flickity-page-dots .dot {
  border-color: currentColor;
}

.Carousel--insideDots .flickity-page-dots .dot.is-selected {
  background: currentColor;
}

.Carousel--insideDots .flickity-prev-next-button {
  bottom: -25px;
  z-index: 1;
}

.Carousel--insideDots .flickity-prev-next-button.next {
  right: 25px;
}

.Carousel--insideDots .flickity-prev-next-button.previous {
  right: 75px;
  margin-right: 15px;
}

@media screen and (min-width: 1008px) {
  .Carousel--insideDots .flickity-page-dots {
    bottom: 28px;
    right: 24px;
    left: auto;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Collapsible
 * ----------------------------------------------------------------------------
 */
.Collapsible {
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
  overflow: hidden;
}

.Collapsible + .Collapsible {
  border-top: none;
}

.Collapsible--padded {
  padding-left: 24px;
  padding-right: 24px;
}

.Collapsible__Button {
  display: block;
  position: relative;
  width: 100%;
  padding: 20px 0;
  text-align: left;
  cursor: pointer;
}

.Collapsible__Plus {
  position: absolute;
  right: 0;
  top: calc(50% - (11px / 2));
  width: 11px;
  height: 11px;
}

.Collapsible__Plus::before,
.Collapsible__Plus::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-90deg);
  background-color: currentColor;
  transition: transform 0.4s ease-in-out, opacity 0.4s ease-in-out;
}

.Collapsible__Plus::before {
  width: 11px;
  height: 1px;
  opacity: 1;
}

.Collapsible__Plus::after {
  width: 1px;
  height: 11px;
}

.Collapsible__Button[aria-expanded=true] .Collapsible__Plus::before,
.Collapsible__Button[aria-expanded=true] .Collapsible__Plus::after {
  transform: translate(-50%, -50%) rotate(90deg);
}

.Collapsible__Button[aria-expanded=true] .Collapsible__Plus::before {
  opacity: 0;
}

.Collapsible .Collapsible {
  margin-left: 16px;
  border: none;
}

.Collapsible .Collapsible .Collapsible__Button {
  padding: 13px 0;
}

.Collapsible__Inner {
  display: block;
  height: 0;
  visibility: hidden;
  transition: height 0.35s ease-in-out, visibility 0s ease-in-out 0.35s;
  overflow: hidden;
}

.Collapsible__Button[aria-expanded=true] + .Collapsible__Inner {
  visibility: visible;
  transition: height 0.35s ease-in-out;
}

.Collapsible__Content {
  padding-bottom: 18px;
}

.Collapsible .Linklist {
  margin-bottom: 4px;
}

.Collapsible .Linklist--bordered {
  margin-top: 16px;
  margin-bottom: 4px;
}

@media screen and (min-width: 641px) {
  /* When this class is applied to a collapsible, it will display as a collapsible on mobile but not on larger screen, where it will
     automatically appear is auto-expanded */
  .Collapsible--autoExpand {
    border: none;
    overflow: visible;
  }

  .Collapsible--autoExpand .Collapsible__Button {
    cursor: default;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 16px;
  }

  .Collapsible--autoExpand .Collapsible__Plus {
    display: none;
  }

  .Collapsible--autoExpand .Collapsible__Inner {
    height: auto;
    visibility: visible;
    overflow: visible;
  }

  .Collapsible--autoExpand .Collapsible__Content {
    padding-bottom: 0;
  }

  .Collapsible--autoExpand .Linklist {
    margin-bottom: 0;
  }

  .Collapsible--autoExpand {
    margin-bottom: 32px;
  }
}

@media screen and (min-width: 1008px) {
  .Collapsible--padded {
    padding-left: 30px;
    padding-right: 30px;
  }

  .Collapsible--large .Collapsible__Button {
    padding: 34px 0;
  }

  .Collapsible--large .Collapsible__Content {
    padding-bottom: 45px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Base drawer
 * ----------------------------------------------------------------------------
 */
.Drawer {
  position: fixed;
  top: 0;
  left: 0;
  visibility: hidden;
  width: 100%;
  height: 100vh;
  max-height: none;
  z-index: 20;
  transition: transform 0.5s var(--drawer-transition-timing), visibility 0.5s var(--drawer-transition-timing);
  background: var(--background);
  box-shadow: none;
  touch-action: manipulation; /* Animating box-shadow is slow, even on modern browsers, so we instead move it in a pseudo-element and animate opacity */
}

.Drawer:focus {
  outline: none;
}

.Drawer::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s var(--drawer-transition-timing);
}

.Drawer--secondary {
  background: var(--light-background);
}

.Drawer--fromLeft {
  transform: translateX(calc(-100%));
}

.Drawer--fromRight {
  right: 0;
  left: auto;
  transform: translateX(100%);
}

.Drawer[aria-hidden=false] {
  visibility: visible;
  transform: translateX(0);
}

.Drawer[aria-hidden=false]::before {
  opacity: 1;
}

.Drawer__Container {
  width: 100%;
}

.Drawer--fromLeft .Drawer__Container {
  padding-left: 18px;
  padding-right: 24px;
}

.Drawer--fromRight .Drawer__Container {
  padding-left: 24px;
  padding-right: 18px;
}

.Drawer__Header {
  display: flex;
  align-items: center;
  position: relative;
  height: 50px;
  max-height: 60px;
  background: #FFFFFF;
  text-align: center;
  z-index: 1;
}

.Drawer__Header--flexible {
  min-height: 0 !important;
  max-height: none !important;
  height: auto !important;
  padding-top: 20px;
  padding-bottom: 20px;
}

.Drawer__Header--flexible .Drawer__Close {
  top: 25px;
}

@supports (--css: variables) {
  .Drawer__Header {
    height: var(--header-height);
  }
}

.Drawer__Header--center {
  justify-content: center;
}

.Drawer__Close {
  position: absolute;
  margin-left: 0;
  left: 18px;
  top: calc(50% - 7px);
  line-height: 0;
  color: #000000;
}

.Drawer__Close svg {
  width: 15px;
  height: 15px;
  stroke-width: 1.25px;
}

.Drawer--fromRight .Drawer__Close {
  right: 18px;
  left: auto;
}

.Drawer__Header--bordered {
  box-shadow: 0 -1px var(--border-color) inset;
}

.Drawer--secondary .Drawer__Header--bordered {
  box-shadow: 0 -1px rgba(var(--border-color-rgb), 0.6) inset;
}

.Drawer--secondary .Collapsible {
  border-bottom-color: rgba(var(--border-color-rgb), 0.6);
}

.Drawer__Content {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: calc(100% - 60px);
  max-height: calc(100% - 50px); /* This handles an edge case when the header is much bigger than the maximum size for content, but this only works on Safari as of today */
}

@supports (--css: variables) {
  .Drawer__Content {
    height: calc(100% - var(--header-height));
    max-height: calc(100% - var(--header-height));
  }
}

@supports (width: calc(max(100%))) {
  .Drawer__Content {
    height: max(calc(100% - var(--header-height)), calc(100% - 60px));
    max-height: max(calc(100% - var(--header-height)), calc(100% - 60px));
  }
}

.Drawer__Main {
  flex: 1 1 auto;
}

.Drawer__Footer {
  flex: none;
  box-shadow: 0 -1px var(--border-color);
}

.Drawer--secondary .Drawer__Footer {
  box-shadow: 0 -1px rgba(var(--border-color-rgb), 0.6);
}

.Drawer__Footer--padded {
  padding: 24px;
}

@supports (padding: max(0px)) {
  .Drawer__Footer--padded {
    padding-bottom: max(24px, env(safe-area-inset-bottom, 0px) + 24px);
  }
}

@media screen and (min-width: 641px) {
  .Drawer {
    width: 85%;
    transform: translateX(-100%);
  }

  .Drawer--small {
    width: 340px;
  }

  .Drawer--fromRight {
    transform: translateX(100%);
  }

  .Drawer__Header {
    max-height: 80px;
    min-height: 60px;
  }

  @supports (width: calc(max(100%))) {
    .Drawer__Content {
      /* This handles an edge case when the header is much bigger than the maximum size for content, but this only works on Safari as of today */
      height: max(calc(100% - var(--header-height)), calc(100% - 80px));
      max-height: max(calc(100% - var(--header-height)), calc(100% - 80px));
    }
  }

  .Drawer--fromLeft .Drawer__Container,
  .Drawer--fromRight .Drawer__Container {
    padding-left: 30px;
    padding-right: 30px;
  }

  .Drawer__Close {
    left: 30px;
  }

  .Drawer--fromRight .Drawer__Close {
    right: 30px;
    left: auto;
  }

  .Drawer__Close svg {
    stroke-width: 1.5px;
  }

  .Drawer__Footer--padded {
    padding: 24px 30px;
  }
}

@media screen and (min-width: 1000px) {
  .Drawer {
    width: 60%;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Animation
 * ----------------------------------------------------------------------------
 */

[data-drawer-animated-left],
[data-drawer-animated-right] {
  opacity: 0;
  transition: opacity 0.5s ease 0.25s, transform 0.5s ease 0.25s;
}

.Drawer[aria-hidden=false] [data-drawer-animated-left],
.Drawer[aria-hidden=false] [data-drawer-animated-right] {
  opacity: 1;
  transform: translateX(0);
}

[data-drawer-animated-left] {
  transform: translateX(-65px);
}

[data-drawer-animated-right] {
  transform: translateX(65px);
}

[data-drawer-animated-bottom] {
  opacity: 0;
  transform: translateY(45px);
  transition: opacity 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.Drawer[aria-hidden=false] [data-drawer-animated-bottom] {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.45s, transform 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.45s;
}
/**
 * ----------------------------------------------------------------------------
 * Basic form
 * ----------------------------------------------------------------------------
 */
.Form {
  width: 100%;
}

.Form__Item {
  position: relative;
  margin-bottom: 15px;
}

.Form__Input,
.Form__Textarea,
.spr-form-input-text,
.spr-form-input-email,
.spr-form-input-textarea {
  -webkit-appearance: none;
  display: block;
  padding: 12px 14px;
  border-radius: 0;
  border: 1px solid var(--border-color);
  width: 100%;
  line-height: normal;
  resize: none;
  transition: border-color 0.1s ease-in-out;
  background: transparent;
}

.Form__Input:focus,
.Form__Textarea:focus,
.spr-form-input-text:focus,
.spr-form-input-email:focus,
.spr-form-input-textarea:focus {
  border-color: rgba(var(--text-color-rgb), 0.8);
  outline: none;
}

.Form__Input::-moz-placeholder, .Form__Textarea::-moz-placeholder, .spr-form-input-text::-moz-placeholder, .spr-form-input-email::-moz-placeholder, .spr-form-input-textarea::-moz-placeholder {
  color: var(--text-color-light);
}

.Form__Input:-ms-input-placeholder, .Form__Textarea:-ms-input-placeholder, .spr-form-input-text:-ms-input-placeholder, .spr-form-input-email:-ms-input-placeholder, .spr-form-input-textarea:-ms-input-placeholder {
  color: var(--text-color-light);
}

.Form__Input::placeholder,
.Form__Textarea::placeholder,
.spr-form-input-text::placeholder,
.spr-form-input-email::placeholder,
.spr-form-input-textarea::placeholder {
  color: var(--text-color-light);
}

.Form__FloatingLabel {
  position: absolute;
  bottom: calc(100% - 8px);
  left: 10px;
  padding: 0 5px;
  line-height: normal;
  color: var(--text-color-light);
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  opacity: 0;
  background: rgba(var(--background-rgb), 0);
  pointer-events: none;
  transform: translateY(3px);
  transition: all 0.3s ease-in-out;
}

.Form__Input:not(:-moz-placeholder-shown) ~ .Form__FloatingLabel, .Form__Textarea:not(:-moz-placeholder-shown) ~ .Form__FloatingLabel {
  opacity: 1;
  background: var(--background);
  transform: translateY(0);
}

.Form__Input:not(:-ms-input-placeholder) ~ .Form__FloatingLabel, .Form__Textarea:not(:-ms-input-placeholder) ~ .Form__FloatingLabel {
  opacity: 1;
  background: var(--background);
  transform: translateY(0);
}

.Form__Input:not(:placeholder-shown) ~ .Form__FloatingLabel,
.Form__Textarea:not(:placeholder-shown) ~ .Form__FloatingLabel {
  opacity: 1;
  background: var(--background);
  transform: translateY(0);
}

.Form__Checkbox {
  position: absolute;
  opacity: 0;
}

.Form__Checkbox ~ label::before {
  display: inline-block;
  content: "";
  width: 1em;
  height: 1em;
  margin-right: 10px;
  border: 1px solid var(--border-color);
  vertical-align: -2px;
}

.Form__Checkbox ~ svg {
  position: absolute;
  top: 5px;
  left: 1px;
  width: 12px;
  height: 12px;
  transform: scale(0);
  transition: transform 0.2s ease-in-out;
  will-change: transform;
}

.Form__Checkbox:checked ~ label::before {
  border-color: var(--text-color);
}

.Form__Checkbox:checked ~ svg {
  transform: scale(1);
}

.Form__Alert,
.spr-form-message {
  margin-bottom: 20px;
}

.Form__Submit {
  display: block;
  margin-top: 20px;
}

.Form__Label,
.spr-form-label {
  display: block;
  padding-bottom: 8px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  text-transform: uppercase;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  letter-spacing: 0.2em;
  line-height: normal;
}

.Form--small .Form__Input,
.Form--small .Form__Textarea {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
  padding-top: 10px;
  padding-bottom: 10px;
}

.Form--spacingTight .Form__Item {
  margin-bottom: 15px;
}

.Form--spacingTight .Form__Alert {
  margin-bottom: 20px;
}

@media screen and (min-width: 641px) {
  .Form__Item,
  .Form__Alert {
    margin-bottom: 30px;
  }

  .Form__Group {
    display: flex;
  }

  .Form__Group > .Form__Item {
    flex: 1;
  }

  .Form__Group > :nth-child(2) {
    margin-left: 30px;
  }

  .Form--spacingTight .Form__Group > :nth-child(2) {
    margin-left: 15px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Form elements
 * ----------------------------------------------------------------------------
 */

.Form__Header {
  margin-bottom: 24px;
  text-align: center;
}

.Form__Hint {
  margin: 24px 0 0 0;
}

.Form__Hint:not(:last-child) {
  margin-bottom: 32px;
}

.Form__Hint--center {
  text-align: center;
}

.Form__ItemHelp {
  position: absolute;
  right: 12px;
  top: 50%;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  color: var(--text-color-light);
  transform: translateY(-50%);
}

/**
 * ----------------------------------------------------------------------------
 * Styled select
 * ----------------------------------------------------------------------------
 */
select::-ms-expand {
  display: none;
}

.Select {
  position: relative;
  color: currentColor;
  line-height: 1;
  vertical-align: middle;
}

.Select svg {
  position: absolute;
  line-height: normal;
  pointer-events: none;
  vertical-align: baseline;
  fill: currentColor;
}

.Select select {
  /* Disable built-in styles */
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  color: inherit;
  cursor: pointer;
  border-radius: 0;
  line-height: normal; /* Remove the ugly blue background on IE when a value is selected */
}

.Select select:focus::-ms-value {
  background: var(--background);
  color: var(--text-color);
}

/* Make sure to have something easy to read... */
.Select option {
  background: white;
  color: black;
}

.Select--primary::after {
  content: "";
  position: absolute;
  right: 1px;
  top: 1px;
  height: calc(100% - 2px);
  width: 55px;
  background: linear-gradient(to right, rgba(var(--background-rgb), 0), rgba(var(--background-rgb), 0.7) 20%, var(--background) 40%);
  pointer-events: none;
}

.Select--primary select {
  width: 100%;
  height: 45px;
  padding-left: 14px;
  padding-right: 28px;
  border: 1px solid var(--border-color);
  background: var(--background);
}

.Select--primary select:active, .Select--primary select:focus {
  border-color: var(--text-color);
  outline: none;
}

.Select--primary svg {
  top: calc(50% - 5px);
  right: 15px;
  width: 10px;
  height: 10px;
  z-index: 1;
}

.Select--transparent select {
  padding-right: 15px;
  background: transparent;
  border: none;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  font-size: inherit;
  text-transform: uppercase;
}

.features--heading-uppercase .Select--transparent select {
  letter-spacing: 0.2em;
}

.Select--transparent svg {
  top: calc(50% - 3px);
  right: 0;
  height: 6px;
}

/**
 * ----------------------------------------------------------------------------
 * Select button (emulate a select button without the actual select)
 * ----------------------------------------------------------------------------
 */

.SelectButton {
  position: relative;
  color: currentColor;
  padding: 2px 18px 2px 0;
  background: transparent;
  border: none;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  text-transform: uppercase;
}

.features--heading-uppercase .SelectButton {
  letter-spacing: 0.2em;
}

.SelectButton svg {
  position: absolute;
  top: calc(50% - 3px);
  right: 0;
  height: 6px;
}


/*
 * INPUT PREFIX (allow to create small input with a prefixed value)
 */

.input-prefix {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 7px 10px;
  border: 1px solid var(--border-color);
  font-size: 14px;
}

.input-prefix__field {
  padding: 0;
  -webkit-appearance: none;
          appearance: none;
  -moz-appearance: textfield;
  min-width: 0;
  width: 100%;
  background: transparent;
  border: none;
  text-align: end;
}

.input-prefix__field::-webkit-outer-spin-button,
.input-prefix__field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input-prefix__field::-moz-placeholder {
  color: var(--text-color-light);
}

.input-prefix__field:-ms-input-placeholder {
  color: var(--text-color-light);
}

.input-prefix__field::placeholder {
  color: var(--text-color-light);
}

/*
 * RANGE ELEMENT
 * Styling range are pretty complex as each browsers has their own way to do it
 */

/* First we revert the styling of range elements */

.range {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none; /* Hides the slider so that custom slider can be made */
  width: 100%; /* Specific width is required for Firefox. */
  background: transparent; /* Otherwise white in Chrome */
}

.range::-webkit-slider-thumb {
  -webkit-appearance: none;
}

/* Chrome and Safari */

.range::-webkit-slider-thumb {
  position: relative;
  height: 10px;
  width: 10px;
  border-radius: 100%;
  border: none;
  background: var(--text-color);
  cursor: pointer;
  margin-top: -4px;
  z-index: 1;
}

.range::-webkit-slider-runnable-track {
  width: 100%;
  height: 2px;
  cursor: pointer;
  background: var(--border-color);
  border-radius: 4px;
  border: none;
}

/* Firefox */

.range::-moz-range-thumb {
  height: 10px;
  width: 10px;
  border-radius: 100%;
  border: none;
  background: var(--text-color);
  cursor: pointer;
}

.range::-moz-range-progress,
.range::-moz-range-track {
  width: 100%;
  height: 2px;
  cursor: pointer;
  border-radius: 4px;
  border: none;
}

.range::-moz-range-progress {
  background-color: rgba(var(--text-color-rgb), 0.7);
}

.range::-moz-range-track {
  background-color: var(--border-color);
}

/* On non-hover devices, we make the thumb bigger */

@media not screen and (any-hover: hover) {
  .range::-webkit-slider-thumb {
    height: 18px;
    width: 18px;
    margin-top: -8px;
  }

  .range::-moz-range-thumb {
    height: 20px;
    width: 20px;
  }
}

/* Range group (when using double range, we need to rely on some clever trick) */

.range-group {
  height: 2px;
  background: linear-gradient(to right, var(--border-color) var(--range-min), rgba(var(--text-color-rgb), 0.7) var(--range-min), rgba(var(--text-color-rgb), 0.7) var(--range-max), var(--border-color) var(--range-max));
  border-radius: 4px;
}

.range-group .range {
  pointer-events: none;
  height: 2px;
  vertical-align: top;
}

.range-group .range::-webkit-slider-runnable-track {
  background: none;
}

.range-group .range::-webkit-slider-thumb {
  pointer-events: auto;
}

.range-group .range::-moz-range-progress,
.range-group .range::-moz-range-track {
  background: none;
}

.range-group .range::-moz-range-thumb {
  pointer-events: auto;
}

.range-group .range:last-child {
  position: absolute;
  inset-block-start: 0;
  inset-inline-start: 0;
}
.HorizontalList {
  list-style: none;
  margin: -6px -8px;
}

.HorizontalList__Item {
  display: inline-block;
  margin: 6px 8px 6px 8px;
}

.HorizontalList__Item > .Link {
  display: inline-block;
}

.HorizontalList--spacingTight {
  margin-left: -8px;
  margin-right: -8px;
}

.HorizontalList--spacingTight .HorizontalList__Item {
  margin-right: 8px;
  margin-left: 8px;
}

.HorizontalList--spacingLoose {
  margin-left: -14px;
  margin-right: -14px;
}

.HorizontalList--spacingLoose .HorizontalList__Item {
  margin-right: 14px;
  margin-left: 14px;
}

.HorizontalList--spacingExtraLoose {
  margin-left: -17px;
  margin-right: -17px;
}

.HorizontalList--spacingExtraLoose .HorizontalList__Item {
  margin-right: 17px;
  margin-left: 17px;
}

.HorizontalList--spacingFill {
  display: flex;
  justify-content: space-around;
  justify-content: space-evenly;
}

.HorizontalList--spacingFill .HorizontalList__Item {
  margin-left: 0;
  margin-right: 0;
}

@media screen and (min-width: 1140px) {
  .HorizontalList--spacingExtraLoose {
    margin-left: -21px;
    margin-right: -21px;
  }

  .HorizontalList--spacingExtraLoose .HorizontalList__Item {
    margin-right: 21px;
    margin-left: 21px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Image overlay
 * ----------------------------------------------------------------------------
 */

.Image--contrast {
  position: relative;
}

.Image--contrast::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-image: linear-gradient(to top, rgba(4, 4, 4, 0.65), rgba(54, 54, 54, 0.2));
}

.Image--contrast > * {
  z-index: 1;
}

/**
 * ----------------------------------------------------------------------------
 * Image lazy loader (integrates with lazy sizes)
 * ----------------------------------------------------------------------------
 */

@-webkit-keyframes lazyLoader {
  0%, 100% {
    transform: translateX(-50%);
  }
  50% {
    transform: translateX(100%);
  }
}

@keyframes lazyLoader {
  0%, 100% {
    transform: translateX(-50%);
  }
  50% {
    transform: translateX(100%);
  }
}

.Image--fadeIn {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.Image--lazyLoaded.Image--fadeIn {
  opacity: 1;
}

.features--show-image-zooming .Image--zoomOut {
  transform: scale(1.1);
  opacity: 0;
  transition: transform 0.8s cubic-bezier(0.215, 0.61, 0.355, 1), opacity 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.features--show-image-zooming .Image--lazyLoaded.Image--zoomOut {
  opacity: 1;
  transform: none;
}

/* If zoom is disabled we apply the same effect than fadeIn */
body:not(.features--show-image-zooming) .Image--zoomOut {
  opacity: 0;
  transition: opacity 0.3s ease;
}

body:not(.features--show-image-zooming) .Image--lazyLoaded.Image--zoomOut {
  opacity: 1;
}

.Image--slideRight,
.Image--slideLeft {
  transform: translateX(25px);
  opacity: 0;
  transition: transform 0.8s cubic-bezier(0.215, 0.61, 0.355, 1), opacity 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.Image--slideLeft {
  transform: translateX(-25px);
}

.Image--lazyLoaded.Image--slideRight,
.Image--lazyLoaded.Image--slideLeft {
  opacity: 1;
  transform: translateX(0);
}

.Image__Loader {
  position: absolute;
  display: block;
  height: 2px;
  width: 50px;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  opacity: 0;
  visibility: hidden;
  margin: auto;
  pointer-events: none;
  background-color: var(--border-color);
  z-index: -1;
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

.Image__Loader::after {
  position: absolute;
  content: "";
  bottom: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 200%;
  background-color: var(--heading-color);
}

.Image--lazyLoading + .Image__Loader {
  opacity: 1;
  visibility: visible;
  z-index: 1;
}

.Image--lazyLoading + .Image__Loader::after {
  -webkit-animation: lazyLoader 3s infinite;
          animation: lazyLoader 3s infinite;
  -webkit-animation-timing-function: cubic-bezier(0.43, 0.43, 0.25, 0.99);
          animation-timing-function: cubic-bezier(0.43, 0.43, 0.25, 0.99);
}

/**
 * ----------------------------------------------------------------------------
 * Aspect ratio
 * ----------------------------------------------------------------------------
 */

.AspectRatio {
  position: relative;
  margin-left: auto;
  margin-right: auto;
}

.AspectRatio::before {
  content: "";
  display: block;
}

.AspectRatio > img {
  max-height: 100%;
  max-width: 100%;
}

.AspectRatio--withFallback > img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.no-js .AspectRatio > img {
  display: none !important;
}

@supports (--css: variables) {
  /* For dynamic one, we use CSS variables, which makes it only compatible for newer browsers */
  .AspectRatio--withFallback {
    padding-bottom: 0 !important; /* For older browsers we use the padding-bottom trick, so make sure to remove it here */
  }

  .AspectRatio::before {
    padding-bottom: calc(100% / (var(--aspect-ratio)));
  }

  .AspectRatio > img,
  .no-js .AspectRatio > noscript img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }
}

.AspectRatio--square::before {
  padding-bottom: 100%;
}

.AspectRatio--short::before {
  padding-bottom: 75%;
}

.AspectRatio--tall::before {
  padding-bottom: 150%;
}

.AspectRatio--square > img,
.AspectRatio--short > img,
.AspectRatio--tall > img {
  position: absolute;
  width: auto;
  height: auto;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

@supports ((-o-object-fit: contain) or (object-fit: contain)) {
  .AspectRatio--square > img,
  .AspectRatio--short > img,
  .AspectRatio--tall > img {
    width: 100%;
    height: 100%;
    -o-object-fit: contain;
       object-fit: contain;
  }
}

.Image--lazyLoad[data-sizes=auto] {
  width: 100%; /* this is needed to help LazySizes calculate the correct size */
}
/**
 * ----------------------------------------------------------------------------
 * List
 * ----------------------------------------------------------------------------
 */

.Linklist {
  list-style: none;
  padding: 0;
}

.Linklist__Item {
  position: relative;
  display: block;
  margin-bottom: 12px;
  width: 100%;
  line-height: 1.5;
  text-align: left;
  transition: all 0.2s ease-in-out;
}

.Linklist__Item:last-child {
  margin-bottom: 0 !important;
}

.Linklist__Item::before,
.Linklist__Checkbox + .Link::before {
  position: absolute;
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  top: calc(50% - 3px);
  left: 0;
  border-radius: 100%;
  background: var(--text-color);
  opacity: 0;
  transition: opacity 0.1s ease-in-out;
}

.Linklist__Item label {
  cursor: pointer;
}

.Linklist__Item > .Link {
  display: block;
  width: 100%;
  text-align: inherit;
}

.Linklist--spacingLoose .Linklist__Item {
  margin-bottom: 18px;
}

.Linklist__Item.is-selected {
  padding-left: 18px;
}

.Linklist__Item.is-selected::before,
.Linklist__Checkbox:checked + .Link::before {
  opacity: 1;
  transition-delay: 0.1s;
}

.Linklist__Checkbox + .Link {
  transition: padding 0.2s ease-in-out, opacity 0.2s ease-in-out;
}

.Linklist__Checkbox:checked + .Link {
  padding-left: 18px;
  color: var(--text-color);
}

.Linklist--bordered {
  margin-left: 8px;
  padding: 0 20px 0 25px;
  border-left: 1px solid var(--border-color);
}

.Linklist--bordered li:first-child .Linklist__Item {
  margin-top: 2px;
}

.Linklist--bordered li:last-child .Linklist__Item {
  margin-bottom: 2px;
}
/**
 * Google map
 */

.FeaturedMap {
  max-width: 1150px;
  margin: 0 auto;
}

.FeaturedMap__MapContainer {
  position: relative;
  height: 240px;
  background-size: cover;
  background-position: center;
}

.FeaturedMap__GMap {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.FeaturedMap__Info {
  position: relative;
  margin: 0 auto;
  background: var(--light-background);
  text-align: left;
  z-index: 1;
  padding: 25px;
}

.FeaturedMap__Store {
  display: block;
  margin-bottom: 1.2em;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
}

.FeaturedMap__Address {
  margin-bottom: 1.2em;
}

.FeaturedMap__Location {
  margin-top: 2.7em;
}

@media screen and (min-width: 641px) {
  .FeaturedMap {
    display: flex;
    flex-direction: row-reverse;
    align-items: stretch;
    padding-top: 0;
    height: 550px;
  }

  .FeaturedMap__MapContainer {
    flex: 1 0 auto;
    height: 100%;
  }

  .FeaturedMap__Info {
    flex: none;
    min-width: 370px;
    width: 370px;
    padding: 50px 60px;
  }
}
/**
 * --------------------------------------------------------------------
 * MEDIA API
 *
 * All those colors have been defined by Shopify and standardized by them
 * --------------------------------------------------------------------
 */
.plyr.plyr--full-ui.plyr--video {
  color: var(--text-color);
  background-color: transparent;
}

.plyr.plyr--full-ui.plyr--video .plyr__video-wrapper {
  background-color: transparent;
}

.plyr.plyr--full-ui.plyr--video:-webkit-full-screen .plyr__video-wrapper .plyr.plyr--full-ui.plyr--video:-webkit-full-screen .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video:-ms-fullscreen .plyr__video-wrapper .plyr.plyr--full-ui.plyr--video:-ms-fullscreen .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video:fullscreen .plyr__video-wrapper .plyr.plyr--full-ui.plyr--video:fullscreen .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video:-webkit-full-screen .plyr__video-wrapper, .plyr.plyr--full-ui.plyr--video:-webkit-full-screen .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video:-moz-full-screen .plyr__video-wrapper, .plyr.plyr--full-ui.plyr--video:-moz-full-screen .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video:-ms-fullscreen .plyr__video-wrapper, .plyr.plyr--full-ui.plyr--video:-ms-fullscreen .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video .plyr--fullscreen-fallback .plyr__video-wrapper,
.plyr.plyr--full-ui.plyr--video .plyr--fullscreen-fallback .plyr__poster {
  background-color: #000;
}

.plyr.plyr--full-ui.plyr--video .plyr__controls {
  background-color: var(--light-background);
  border-color: rgba(var(--text-color-rgb), 0.05);
}

.plyr.plyr--full-ui.plyr--video .plyr__control.plyr__control--overlaid {
  background-color: var(--light-background);
  border-color: rgba(var(--text-color-rgb), 0.05);
}

.plyr.plyr--full-ui.plyr--video .plyr__control.plyr__control--overlaid.plyr__tab-focus, .plyr.plyr--full-ui.plyr--video .plyr__control.plyr__control--overlaid:hover {
  color: rgba(var(--text-color-rgb), 0.55);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]::-moz-range-thumb {
  box-shadow: 2px 0 0 0 var(--light-background);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]::-ms-thumb {
  box-shadow: 2px 0 0 0 var(--light-background);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]::-webkit-slider-thumb {
  box-shadow: 2px 0 0 0 var(--light-background);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]::-webkit-slider-runnable-track {
  background-image: linear-gradient(to right, currentColor 0, rgba(var(--text-color-rgb), 0.6) 0);
  background-image: linear-gradient(to right, currentColor var(--value, 0), rgba(var(--text-color-rgb), 0.6) var(--value, 0));
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]::-moz-range-track {
  background-color: rgba(var(--text-color-rgb), 0.6);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]::-ms-fill-upper {
  background-color: rgba(var(--text-color-rgb), 0.6);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range].plyr__tab-focus::-webkit-slider-runnable-track {
  box-shadow: 0 0 0 4px rgba(var(--text-color-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range].plyr__tab-focus::-moz-range-track {
  box-shadow: 0 0 0 4px rgba(var(--text-color-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range].plyr__tab-focus::-ms-track {
  box-shadow: 0 0 0 4px rgba(var(--text-color-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]:active::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(var(--text-color-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]:active::-ms-thumb {
  box-shadow: 0 0 0 3px rgba(var(--text-color-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress input[type=range]:active::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(var(--text-color-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress .plyr__tooltip {
  background-color: var(--text-color);
  color: var(--light-background);
}

.plyr.plyr--full-ui.plyr--video .plyr__progress .plyr__tooltip::before {
  border-top-color: var(--text-color);
}

.plyr.plyr--full-ui.plyr--video.plyr--loading .plyr__progress__buffer {
  background-image: linear-gradient(-45deg, rgba(var(--text-color-rgb), 0.6) 25%, transparent 25%, transparent 50%, rgba(var(--text-color-rgb), 0.6) 50%, rgba(var(--text-color-rgb), 0.6) 75%, transparent 75%, transparent);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range] {
  color: var(--light-background);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]::-moz-range-thumb {
  box-shadow: 2px 0 0 0 var(--text-color);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]::-ms-thumb {
  box-shadow: 2px 0 0 0 var(--text-color);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]::-webkit-slider-thumb {
  box-shadow: 2px 0 0 0 var(--text-color);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]::-webkit-slider-runnable-track {
  background-image: linear-gradient(to right, currentColor 0, rgba(var(--light-background-rgb), 0.6) 0);
  background-image: linear-gradient(to right, currentColor var(--value, 0), rgba(var(--light-background-rgb), 0.6) var(--value, 0));
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]::-moz-range-track, .plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]::-ms-fill-upper {
  background-color: rgba(var(--light-background-rgb), 0.6);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range].plyr__tab-focus::-webkit-slider-runnable-track {
  box-shadow: 0 0 0 4px rgba(var(--light-background-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range].plyr__tab-focus::-moz-range-track {
  box-shadow: 0 0 0 4px rgba(var(--light-background-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range].plyr__tab-focus::-ms-track {
  box-shadow: 0 0 0 4px rgba(var(--light-background-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]:active::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(var(--light-background-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]:active::-ms-thumb {
  box-shadow: 0 0 0 3px rgba(var(--light-background-rgb), 0.25);
}

.plyr.plyr--full-ui.plyr--video .plyr__volume input[type=range]:active::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(var(--light-background-rgb), 0.25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: var(--light-background);
  border-color: rgba(var(--text-color-rgb), 0.05);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: var(--text-color);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgba(var(--text-color-rgb), 0.55);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus {
  color: rgba(var(--text-color-rgb), 0.55);
  background: rgba(var(--text-color-rgb), 0.05);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-color: rgba(var(--text-color-rgb), 0.05);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  background: var(--light-background);
  border-color: rgba(var(--text-color-rgb), 0.05);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  color: rgba(var(--text-color-rgb), 0.55);
}

/**
 * --------------------------------------------------------------------
 * VIDEO WRAPPER
 * --------------------------------------------------------------------
 */

.VideoWrapper {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
  max-width: 100%;
}

.VideoWrapper iframe,
.VideoWrapper object,
.VideoWrapper embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* For native one we use a different thing */
.VideoWrapper--native::after {
  display: none;
}

.VideoWrapper--native .plyr,
.VideoWrapper--native video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.VideoWrapper--native .plyr__video-wrapper,
.VideoWrapper--native video {
  height: 100%;
}

/**
 * --------------------------------------------------------------------
 * 3D MODEL WRAPPER
 * --------------------------------------------------------------------
 */

.ModelWrapper {
  position: relative;
  padding-bottom: 100%;
}

.ModelWrapper .shopify-model-viewer-ui,
.ModelWrapper model-viewer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/**
 * ----------------------------------------------------------------------------
 * Anchor
 * ----------------------------------------------------------------------------
 */
.Anchor {
  display: block;
  position: relative;
  top: -75px;
  visibility: hidden;
}

@supports (--css: variables) {
  .Anchor {
    top: calc(-1 * (var(--header-height))); /* + var(--announcement-bar-height)));*/
  }
}

/**
 * ----------------------------------------------------------------------------
 * Loading bar
 * ----------------------------------------------------------------------------
 */

.LoadingBar {
  position: fixed;
  top: 0;
  left: 0;
  height: 2px;
  width: 0;
  opacity: 0;
  background: var(--heading-color);
  transition: width 0.25s ease-in-out;
  z-index: 50;
  pointer-events: none;
}

.LoadingBar.is-visible {
  opacity: 1;
}

@media screen and (min-width: 641px) {
  .LoadingBar {
    height: 3px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Placeholder (used within the theme editor only)
 * ----------------------------------------------------------------------------
 */

.PlaceholderSvg {
  display: block;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

.PlaceholderSvg--dark {
  background: dimgray;
  fill: #a1a1a1;
}

.PlaceholderBackground {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
  opacity: 0.7;
}

.PlaceholderBackground__Svg {
  height: 100% !important;
  width: auto !important;
  min-width: 100%;
}

/**
 * ----------------------------------------------------------------------------
 * Quantity selector
 * ----------------------------------------------------------------------------
 */

.QuantitySelector {
  display: inline-flex;
  align-items: center;
  border: 1px solid var(--border-color);
  white-space: nowrap;
}

.QuantitySelector svg {
  width: 10px;
  height: 10px;
  stroke-width: 1.5px;
  vertical-align: -1px;
}

.QuantitySelector__Button {
  display: inline-block;
  padding: 5px 9px; /* this allows to slightly increase the clickable area */
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.QuantitySelector__CurrentQuantity {
  display: inline-block;
  width: 20px;
  padding: 0;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 10px));
  text-align: center;
  letter-spacing: normal;
  background: transparent;
  border: none;
}

.QuantitySelector--large .QuantitySelector__CurrentQuantity {
  width: 35px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 14px));
}

.QuantitySelector--large .QuantitySelector__Button {
  padding: 10px 20px;
}

.QuantitySelector--large svg {
  width: 11px;
  height: 11px;
}

@media screen and (min-width: 641px) {
  .QuantitySelector__Button {
    padding: 7px 14px 8px 14px;
  }

  .QuantitySelector__CurrentQuantity {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  }
}

/**
 * ----------------------------------------------------------------------------
 * Product price
 * ----------------------------------------------------------------------------
 */

.Price {
  display: inline-block;
}

.Price--highlight {
  color: var(--product-sale-price-color);
}

.Price--compareAt {
  position: relative;
  margin-left: 10px;
}

.Price--compareAt::before {
  position: absolute;
  content: "";
  top: 50%;
  left: -0.4em;
  width: calc(100% + 0.8em);
  height: 1px;
  background: currentColor;
}

/**
 * ----------------------------------------------------------------------------
 * Color swatch
 * ----------------------------------------------------------------------------
 */

.ColorSwatch {
  position: relative;
  display: inline-block;
  height: 30px;
  width: 30px;
  vertical-align: top;
  cursor: pointer;
  background-size: cover;
}

.ColorSwatch::after {
  content: "";
  position: absolute;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
  top: -3px;
  left: -3px;
}

.ColorSwatch:hover::after {
  border: 1px solid var(--border-color);
}

.ColorSwatch--small {
  width: 16px;
  height: 16px;
}

.ColorSwatch--large {
  width: 36px;
  height: 36px;
}

.ColorSwatch--white {
  outline: 1px solid var(--border-color);
  outline-offset: -1px;
}

.ColorSwatch.is-active::after,
.ColorSwatch__Radio:checked + .ColorSwatch::after {
  border: 1px solid currentColor !important;
}

.ColorSwatch__Radio {
  position: absolute;
  height: 0;
  width: 0;
  opacity: 0;
}

.ColorSwatchList .HorizontalList__Item {
  position: relative;
}

.Collapsible .ColorSwatchList {
  padding-top: 4px;
  padding-bottom: 10px;
}

.Collapsible--autoExpand .ColorSwatchList {
  padding-top: 8px;
  padding-bottom: 12px;
}

/**
 * ----------------------------------------------------------------------------
 * Size swatch
 * ----------------------------------------------------------------------------
 */

.SizeSwatch {
  display: inline-block;
  text-align: center;
  min-width: 36px;
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  color: var(--text-color-light);
  cursor: pointer;
}

.SizeSwatch__Radio {
  display: none;
}

.SizeSwatch.is-active::after,
.SizeSwatch__Radio:checked + .SizeSwatch {
  border-color: var(--text-color);
  color: var(--text-color);
}

/**
 * ----------------------------------------------------------------------------
 * Alerts
 * ----------------------------------------------------------------------------
 */

.Alert,
.spr-form-message {
  display: block;
  padding: 10px 20px;
  white-space: normal;
  font-size: 1rem;
  word-break: break-all;
  word-break: break-word;
  text-shadow: none;
}

.Alert--large {
  padding: 18px 20px;
}

.Alert--error,
.spr-form-message-error {
  background: #e4c4c4;
  color: #cb2b2b;
}

.Alert--success,
.spr-form-message-success {
  background: #d2e4c4;
  color: #307a07;
}

.Alert__ErrorList {
  list-style: none;
}

@media screen and (min-width: 641px) {
  .Alert--large {
    padding: 18px 30px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Segment
 * ----------------------------------------------------------------------------
 */

.Segment + .Segment {
  margin-top: 50px;
}

.Segment__Title {
  margin-bottom: 24px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color-light);
}

.Segment__Title--flexed {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.Segment__ActionList {
  margin-top: 16px;
}

.Segment__ActionItem {
  line-height: 1.4;
}

.Segment__ActionItem + .Segment__ActionItem {
  margin-left: 20px;
}

.Segment__ButtonWrapper {
  margin-top: 32px;
}

@media screen and (min-width: 641px) {
  .Segment__Title {
    margin-bottom: 34px;
  }

  .Segment__ActionList {
    margin-top: 24px;
  }
}

@media screen and (min-width: 1140px) {
  .Segment + .Segment {
    margin-top: 65px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Empty state
 * ----------------------------------------------------------------------------
 */

.EmptyState {
  margin: 140px 0;
  text-align: center;
}

.EmptyState__Action {
  display: inline-block;
  margin-top: 20px;
}

@media screen and (min-width: 641px) {
  .EmptyState {
    margin: 200px 0;
  }
}

@media screen and (min-width: 1140px) {
  .EmptyState {
    margin: 250px 0;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Spinner
 * ----------------------------------------------------------------------------
 */

@-webkit-keyframes bouncingSpinnerAnimation {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes bouncingSpinnerAnimation {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.BouncingSpinner {
  display: block;
  text-align: center;
}

.BouncingSpinner > span {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: currentColor;
  border-radius: 100%;
  -webkit-animation: bouncingSpinnerAnimation 1.4s infinite ease-in-out both;
          animation: bouncingSpinnerAnimation 1.4s infinite ease-in-out both;
}

.BouncingSpinner > span:first-child {
  -webkit-animation-delay: -0.32s;
          animation-delay: -0.32s;
}

.BouncingSpinner > span:nth-child(2) {
  -webkit-animation-delay: -0.16s;
          animation-delay: -0.16s;
}

/**
 * ----------------------------------------------------------------------------
 * Video
 * ----------------------------------------------------------------------------
 */

.Video__PlayButton {
  display: inline-block;
  transition: transform 0.2s ease-in-out;
  height: 80px;
  width: 80px;
  cursor: pointer;
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.2));
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .Video__PlayButton:hover {
    transform: scale(1.1);
  }
}

.Video__PlayButton svg {
  width: 80px;
  height: 80px;
  pointer-events: none;
}

/**
 * ----------------------------------------------------------------------------
 * Announcement bar
 * ----------------------------------------------------------------------------
 */

.AnnouncementBar {
  position: relative;
  text-align: center;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 10px));
  z-index: 1;
}

.AnnouncementBar__Wrapper {
  padding: 12px 15px;
}

.AnnouncementBar__Content {
  color: inherit;
  margin: 0;
}

@media screen and (min-width: 641px) {
  .AnnouncementBar {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  }
}

/**
 * ----------------------------------------------------------------------------
 * Share buttons
 * ----------------------------------------------------------------------------
 */

.ShareButtons {
  display: table;
  table-layout: fixed;
  border-collapse: collapse;
  width: 100%;
}

.ShareButtons__Item {
  display: table-cell;
  width: 60px;
  height: 45px;
  min-height: 45px;
  color: var(--text-color-light);
  text-align: center;
  vertical-align: middle;
  background: var(--background);
  border: 1px solid var(--border-color);
  outline: 1px solid transparent;
  outline-offset: -1px;
  transition: all 0.2s ease-in-out;
}

.ShareButtons__Item svg {
  height: 18px;
  width: 18px;
  vertical-align: text-bottom;
}

.ShareButtons__Item--facebook:hover,
.no-supports-hover .ShareButtons__Item--facebook {
  background: #4469af;
  color: #ffffff;
  border-color: #4469af;
  outline: 1.5px solid #4469af;
}

.ShareButtons__Item--pinterest:hover,
.no-supports-hover .ShareButtons__Item--pinterest {
  background: #c8232c;
  color: #ffffff;
  border-color: #c8232c;
  outline: 1.5px solid #c8232c;
}

.ShareButtons__Item--twitter:hover,
.no-supports-hover .ShareButtons__Item--twitter {
  background: #00aced;
  color: #ffffff;
  border-color: #00aced;
  outline: 1.5px solid #00aced;
}

@media screen and (min-width: 641px) {
  .ShareButtons {
    width: auto;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Featured quote
 * ----------------------------------------------------------------------------
 */

.FeaturedQuote {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 120px 40px;
  background: var(--secondary-elements-background);
  color: var(--secondary-elements-text-color);
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 18px));
}

.FeaturedQuote__Author {
  margin-top: 14px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 14px));
  opacity: 0.5;
}

.FeaturedQuote__Content a {
  text-decoration: underline;
  text-underline-position: under;
}

@media screen and (min-width: 1008px) {
  .FeaturedQuote {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Shopify CAPTCHA
 * ----------------------------------------------------------------------------
 */

.shopify-challenge__container {
  margin-top: 80px;
  margin-bottom: 80px;
  text-align: center;
}

.shopify-challenge__container .shopify-challenge__button {
  position: relative;
  display: inline-block;
  padding: 14px 28px;
  line-height: normal;
  border: 1px solid transparent;
  border-radius: 0;
  text-transform: uppercase;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  text-align: center;
  letter-spacing: 0.2em;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  background: var(--button-background);
  color: var(--button-text-color);
}

/**
 * ----------------------------------------------------------------------------
 * Newsletter (home page)
 * ----------------------------------------------------------------------------
 */

.ImageHero--newsletter .SectionHeader.SectionHeader {
  margin-bottom: 30px;
}

.Newsletter .Form__Input::-moz-placeholder {
  color: inherit;
}

.Newsletter .Form__Input:-ms-input-placeholder {
  color: inherit;
}

.Newsletter .Form__Input::placeholder {
  color: inherit;
}

.Newsletter .Form__Input:focus {
  border-color: currentColor;
}

.Newsletter .Form__Submit {
  width: 100%;
}

@media screen and (max-width: 640px) {
  .ImageHero--newsletter {
    min-height: 450px !important;
  }

  .ImageHero--newsletter .ImageHero__ContentOverlay {
    padding-left: 25px;
    padding-right: 25px;
  }
}

@media screen and (min-width: 641px) {
  .Newsletter__Inner {
    display: flex;
  }

  .Newsletter .Form__Input {
    min-width: 395px;
    width: 395px;
  }

  .Newsletter .Form__Submit {
    margin: 0 0 0 20px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * TOOLTIP
 * ----------------------------------------------------------------------------
 */

@media (-moz-touch-enabled: 0), (hover: hover) {
  [data-tooltip] {
    position: relative;
  }

  [data-tooltip]::before {
    position: absolute;
    content: attr(data-tooltip);
    bottom: 70%;
    left: 70%;
    padding: 4px 11px 3px 11px;
    white-space: nowrap;
    border: 1px solid var(--border-color);
    background: var(--light-background);
    color: var(--text-color-light);
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;
    z-index: 1;
  }

  [data-tooltip]:hover::before {
    opacity: 1;
    visibility: visible;
  }
}

/**
 * -------------------------------------------------------------
 * PRICE RANGE
 * -------------------------------------------------------------
 */

.price-range {
  display: block;
  -webkit-padding-before: 5px;
          padding-block-start: 5px;
}

.price-range__input-group {
  display: flex;
  align-items: center;
}

.price-range__input {
  flex: 1 0 0;
  min-width: 0; /* Required for Firefox */
}

.price-range__delimiter {
  margin-inline: 15px;
}

.price-range__range-group {
  position: relative;
  -webkit-margin-after: 20px;
          margin-block-end: 20px;
}

.no-js .price-range__range-group {
  display: none !important; /* When JS is disabled we only rely on the input field */
}

@media not screen and (any-hover: hover) {
  /* On non-touch device the thumb are bigger so we need to adjust the spacing */
  .price-range {
    -webkit-padding-before: 7px;
            padding-block-start: 7px;
  }
}

/**
 * -------------------------------------------------------------
 * RATING
 * -------------------------------------------------------------
 */

.rating {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.rating__stars {
  display: grid;
  grid-auto-flow: column;
  grid-column-gap: 1px;
  -moz-column-gap: 1px;
       column-gap: 1px;
}

.rating__star {
  color: var(--product-star-rating);
  width: 12px;
  height: 12px;
}

.rating__star--empty {
  color: var(--text-color-light)l
}

.rating__caption {
  -webkit-margin-start: 8px;
          margin-inline-start: 8px;
}
.Modal {
  position: fixed;
  display: flex;
  flex-direction: column;
  visibility: hidden;
  top: 50%;
  left: 50%;
  width: 480px;
  max-width: calc(100vw - 40px);
  max-height: calc(100vh - 40px);
  padding: 15px 20px 20px 20px;
  z-index: 20;
  opacity: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  background: var(--background);
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

@supports (--css: variables) {
  .Modal {
    max-height: calc(var(--window-height) - 40px);
  }
}

.Modal[aria-hidden=false] {
  visibility: visible;
  opacity: 1;
}

.Modal--dark {
  background: var(--secondary-elements-background);
  color: var(--secondary-elements-text-color);
}

.Modal--dark .Rte h1,
.Modal--dark .Rte h2,
.Modal--dark .Rte h3,
.Modal--dark .Rte h4,
.Modal--dark .Rte h5,
.Modal--dark .Rte h6 {
  color: var(--secondary-elements-text-color);
}

.Modal--fullScreen {
  max-width: none;
  max-height: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  transform: none;
}

.Modal--pageContent {
  padding: 60px 0 50px 0;
}

.Modal--videoContent {
  justify-content: center;
  background: #000000; /* Full theatre experience ! */
  color: #ffffff;
}

.Modal .Heading:not(.Link) {
  color: inherit;
}

.Modal__Header {
  margin-bottom: 30px;
  text-align: center;
}

.Modal__Close {
  display: block;
  margin: 25px auto 0 auto;
  flex-shrink: 0;
}

.Modal__Close--outside {
  position: absolute;
  margin-top: 0;
  top: 20px;
  right: 20px;
  line-height: 0;
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
}

.Modal__Close--outside:hover {
  opacity: 1;
}

.Modal__Close--outside svg {
  height: 16px;
  width: 16px;
  stroke-width: 1.25px;
}

.Modal--fullScreen .Modal__Close--outside {
  right: 40px;
  top: 40px;
}

.Modal__Content iframe {
  display: none;
}

.Modal[aria-hidden=false] .Modal__Content iframe {
  display: block;
}

/* We override some styles for common elements like table */
.Modal__Content th,
.Modal__Content td {
  border-color: var(--secondary-elements-border-color) !important;
}

.Modal__Content thead th:empty,
.Modal__Content tbody th {
  background: var(--secondary-elements-background) !important;
}

@media screen and (min-width: 641px) {
  .Modal:not(.Modal--pageContent) {
    padding: 35px 40px 40px 40px;
  }
}

@media screen and (min-width: 1008px) {
  .Modal {
    flex-direction: column;
  }

  .Modal--pageContent {
    padding: 100px 0 80px 0;
  }

  .Modal--pageContent .Modal__Content {
    flex: 1 0 0;
  }

  .Modal__Close:not(.Modal__Close--outside) {
    margin-top: 40px;
    flex-shrink: 0;
  }
}
/**
 * ----------------------------------------------------------------------------
 * A11Y
 * ----------------------------------------------------------------------------
 */
.PageSkipLink:focus {
  clip: auto;
  width: auto;
  height: auto;
  margin: 0;
  color: var(--text-color);
  background-color: var(--background);
  padding: 10px;
  z-index: 10000;
  transition: none;
}

/**
 * ----------------------------------------------------------------------------
 * Page overlay
 * ----------------------------------------------------------------------------
 */
.PageOverlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 10;
  visibility: hidden;
  background: rgba(54, 54, 54, 0);
  transition: background 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.PageOverlay.is-visible {
  background: rgba(54, 54, 54, 0.5);
  visibility: visible;
}

/**
 * ----------------------------------------------------------------------------
 * Page header
 * ----------------------------------------------------------------------------
 */
.PageHeader {
  position: relative;
  margin: 35px 0;
}

.PageHeader--withBackground {
  display: flex;
  margin: 0;
  width: 100%;
  min-height: 450px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-size: cover;
  color: #ffffff;
  overflow: hidden;
}

.PageHeader--withBackground .Heading,
.PageHeader--withBackground .Rte a:not(.Button) {
  color: #ffffff;
  -webkit-text-decoration-color: #ffffff;
          text-decoration-color: #ffffff;
}

.PageHeader__ImageWrapper {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: center center;
}
@supports (--css: variables) {
  .PageHeader__ImageWrapper {
    top: calc(-1 * 0px - 0px * 0);
    top: calc(-1 * var(--announcement-bar-height, 0px) - var(--header-height, 0px) * var(--use-unsticky-header, 0));
    height: calc(100% + 0px + 0px * 0);
    height: calc(100% + var(--announcement-bar-height, 0px) + var(--header-height, 0px) * var(--use-unsticky-header, 0));
  }
}

.PageHeader .SectionHeader__Heading,
.PageHeader .SectionHeader__Description {
  margin-top: 0 !important;
}

.PageHeader .Alert {
  margin-top: 22px;
}

.PageHeader__Back {
  display: inline-block;
  margin-bottom: 25px;
}
.PageHeader__Back svg {
  height: 9px;
  margin-right: 12px;
  vertical-align: baseline;
  vertical-align: initial;
}

/* Double selector is just to increase specificity and avoid !important */
.PageHeader--withBackground .SectionHeader.SectionHeader {
  position: relative;
  padding: 60px 0 80px 0;
}
@supports (--css: variables) {
  .PageHeader--withBackground .SectionHeader.SectionHeader {
    margin-top: calc(var(--header-height) * 0);
    margin-top: calc(var(--header-height) * var(--header-is-transparent, 0));
  }
}

@media screen and (max-width: 640px) {
  .PageHeader .SectionHeader__Heading {
    margin-bottom: 10px;
  }
}
@media screen and (min-width: 641px) {
  .PageHeader {
    margin: 50px 0;
  }

  .PageHeader--withBackground {
    min-height: 450px;
    margin-top: 0;
    margin-bottom: 0;
  }
  @supports (--css: variables) {
    .PageHeader--withBackground {
      min-height: calc(380px + var(--header-height) * 0);
      min-height: calc(380px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--small.PageHeader--withBackground {
    min-height: 420px;
  }
  @supports (--css: variables) {
    .PageHeader--small.PageHeader--withBackground {
      min-height: calc(350px + var(--header-height) * 0);
      min-height: calc(350px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--large.PageHeader--withBackground {
    min-height: 480px;
  }
  @supports (--css: variables) {
    .PageHeader--large.PageHeader--withBackground {
      min-height: calc(410px + var(--header-height) * 0);
      min-height: calc(410px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--withBackground .SectionHeader.SectionHeader {
    margin-top: 50px;
    padding: 40px 0;
  }
  @supports (--css: variables) {
    .PageHeader--withBackground .SectionHeader.SectionHeader {
      margin-top: calc(var(--header-height) * 0);
      margin-top: calc(var(--header-height) * var(--header-is-transparent, 0));
    }
  }
}
@media screen and (min-width: 1140px) {
  .PageHeader--withBackground {
    min-height: 550px;
  }
  @supports (--css: variables) {
    .PageHeader--withBackground {
      min-height: calc(450px + var(--header-height) * 0);
      min-height: calc(450px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--small.PageHeader--withBackground {
    min-height: 500px;
  }
  @supports (--css: variables) {
    .PageHeader--small.PageHeader--withBackground {
      min-height: calc(400px + var(--header-height) * 0);
      min-height: calc(400px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--large.PageHeader--withBackground {
    min-height: 620px;
  }
  @supports (--css: variables) {
    .PageHeader--large.PageHeader--withBackground {
      min-height: calc(520px + var(--header-height) * 0);
      min-height: calc(520px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }
}
@media screen and (min-width: 1800px) {
  .PageHeader--withBackground {
    min-height: 650px;
  }
  @supports (--css: variables) {
    .PageHeader--withBackground {
      min-height: calc(600px + var(--header-height) * 0);
      min-height: calc(600px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--small.PageHeader--withBackground {
    min-height: 600px;
  }
  @supports (--css: variables) {
    .PageHeader--small.PageHeader--withBackground {
      min-height: calc(550px + var(--header-height) * 0);
      min-height: calc(550px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }

  .PageHeader--large.PageHeader--withBackground {
    min-height: 700px;
  }
  @supports (--css: variables) {
    .PageHeader--large.PageHeader--withBackground {
      min-height: calc(650px + var(--header-height) * 0);
      min-height: calc(650px + var(--header-height) * var(--header-is-transparent, 0));
    }
  }
}
/**
 * ----------------------------------------------------------------------------
 * Page layout (to create two columns) and content
 * ----------------------------------------------------------------------------
 */

.PageLayout__Section:first-child {
  margin-bottom: 60px;
}

.PageLayout__Section--sticky {
  position: sticky;
  top: 75px;
  align-self: flex-start;
}

.PageLayout:not(:only-child) {
  margin-top: 35px;
  margin-bottom: 35px;
}

@supports (--css: variables) {
  .PageLayout__Section--sticky {
    top: calc(var(--header-height) + 20px);
  }
}

@media screen and (min-width: 641px) {
  .PageLayout {
    display: flex;
    flex-wrap: nowrap;
  }

  .PageLayout:not(:only-child) {
    margin-top: 50px;
    margin-bottom: 50px;
  }

  .PageLayout__Section {
    flex: 1 0 0;
  }

  .PageLayout__Section:first-child {
    margin-bottom: 0;
  }

  .PageLayout__Section--secondary {
    flex: 1 1 200px;
    max-width: 200px;
  }

  .PageLayout__Section + .PageLayout__Section {
    margin-left: 50px;
  }
}
@media screen and (min-width: 641px) and (max-width: 1007px) {
  .PageLayout--breakLap {
    display: block;
  }

  .PageLayout--breakLap .PageLayout__Section:first-child {
    margin-bottom: 60px;
  }

  .PageLayout--breakLap .PageLayout__Section + .PageLayout__Section {
    margin-left: 0;
    width: 100%;
  }
}
@media screen and (min-width: 1008px) {
  .PageLayout__Section--secondary {
    flex-basis: 235px;
    max-width: 235px;
  }
}
@media screen and (min-width: 1140px) {
  .PageLayout__Section + .PageLayout__Section {
    margin-left: 80px;
  }

  .PageLayout__Section--secondary {
    flex-basis: 290px;
    max-width: 290px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Page spacing wrapper
 * ----------------------------------------------------------------------------
 */
.PageSpacingWrapper {
  margin-bottom: 60px;
}

/**
 * ----------------------------------------------------------------------------
 * Page content
 * ----------------------------------------------------------------------------
 */
.PageContent {
  max-width: 1000px;
  margin: 35px auto;
}

.PageContent--fitScreen {
  display: flex;
  min-height: calc(100vh - 120px);
  align-items: center;
}
@supports (--css: variables) {
  .PageContent--fitScreen {
    min-height: calc(var(--window-height) - var(--header-height) - 0px - 120px);
    min-height: calc(var(--window-height) - var(--header-height) - var(--announcement-bar-height, 0px) - 120px);
    /* 120px is the margin */
  }
}

.PageContent--narrow {
  max-width: 680px;
}

.PageContent--extraNarrow {
  max-width: 400px;
}

.PageHeader + .PageContent {
  margin-top: 0;
}

@media screen and (min-width: 641px) {
  .PageContent {
    margin-bottom: 80px;
    margin-top: 80px;
  }

  .PageContent--fitScreen {
    min-height: calc(100vh - 160px);
  }
  @supports (--css: variables) {
    .PageContent--fitScreen {
      min-height: calc(var(--window-height) - var(--header-height) - 0px - 160px);
      min-height: calc(var(--window-height) - var(--header-height) - var(--announcement-bar-height, 0px) - 160px);
      /* 160px is the margin */
    }
  }
}
.Pagination {
  margin: 60px 0;
  text-align: center;
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  line-height: 1;
}

.Pagination--tight {
  margin: 60px 0 !important;
}

.Pagination__Nav {
  display: inline-block;
  list-style: none;
}

.Pagination__NavItem {
  display: inline-block;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
}
.Pagination__NavItem svg {
  width: 6px;
  height: 10px;
  vertical-align: -1px;
}

.Pagination__NavItem.is-active {
  color: var(--text-color);
  border-bottom-color: var(--text-color);
  box-shadow: 0 -2px var(--text-color) inset;
}

@media screen and (min-width: 641px) {
  .Pagination {
    margin: 80px 0;
  }

  .Pagination__NavItem {
    padding-left: 28px;
    padding-right: 28px;
  }
}
@media screen and (min-width: 1140px) {
  .Pagination {
    margin: 120px 0;
  }
}
.Panel {
  position: relative;
  border: 1px solid var(--border-color);
  padding: 60px 24px;
}

.Panel--withArrows {
  margin: 0 15px;
}

.Panel--flush {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.Panel__Title {
  position: absolute;
  top: 0;
  left: 50%;
  margin: 0;
  padding: 0 14px 0 18px;
  transform: translate(-50%, -50%);
  background: var(--background);
  white-space: nowrap;
}

.Panel .flickity-prev-next-button {
  top: calc(50% - (45px / 2));
}

.Panel .flickity-prev-next-button.next {
  right: calc(-45px / 2);
}

.Panel .flickity-prev-next-button.previous {
  left: calc(-45px / 2);
}

@media screen and (min-width: 641px) {
  .Panel {
    padding-left: 50px;
    padding-right: 50px;
  }

  .Panel--withArrows {
    margin-left: 0;
    margin-right: 0;
  }
}
.Popover {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  background: var(--light-background);
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(54, 54, 54, 0.2);
  touch-action: manipulation;
  transform: translateY(100%);
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.Popover--secondary {
  background: var(--background);
}

.Popover[aria-hidden=false] {
  transform: translateY(0);
  visibility: visible;
}

.Popover__Header {
  position: relative;
  padding: 13px 20px;
  border-bottom: 1px solid rgba(var(--border-color-rgb), 0.4);
  text-align: center;
}

.Popover__Close {
  position: absolute;
  left: 20px;
  top: calc(50% - 7px);
  line-height: 0;
}
.Popover__Close svg {
  stroke-width: 1.1px;
}

.Popover__ValueList {
  list-style: none;
  max-height: 385px;
  padding: 18px 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.Popover__Value {
  display: block;
  padding: 12px 20px;
  width: 100%;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  color: var(--text-color-light);
  text-align: center;
  transition: color 0.2s ease-in-out;
}

.Popover__Value:focus {
  background: var(--background);
  outline: none;
}

.Popover__Value.is-selected {
  color: var(--text-color);
}

.Popover__FooterHelp {
  width: 100%;
  padding: 18px 20px;
  text-align: center;
  border-top: 1px solid rgba(var(--border-color-rgb), 0.4);
}

@supports (padding: max(0px)) {
  .Popover__ValueList {
    max-height: calc(385px + env(safe-area-inset-bottom, 0px));
    padding-bottom: max(18px, env(safe-area-inset-bottom, 0px) + 18px);
  }
}

@media screen and (min-width: 1008px) {
  .Popover {
    transform: none;
    width: auto;
    bottom: auto;
    left: auto;
    opacity: 0;
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
  }
  .Popover::before {
    content: "";
    position: absolute;
    right: 40px;
    width: 10px;
    height: 10px;
    border-style: solid;
  }

  .Popover--noWrap {
    white-space: nowrap;
  }

  .Popover--withMinWidth {
    min-width: 375px;
  }

  .Popover--positionBottom::before {
    bottom: 100%;
    border-width: 0 10px 10px 10px;
    border-color: transparent transparent var(--light-background) transparent;
    filter: drop-shadow(0 -2px 2px rgba(54, 54, 54, 0.2));
  }

  .Popover--positionTop::before {
    top: 100%;
    border-width: 10px 10px 0 10px;
    border-color: var(--light-background) transparent transparent transparent;
    filter: drop-shadow(0 2px 2px rgba(54, 54, 54, 0.2));
  }

  .Popover--positionLeft::before {
    left: 100%;
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent var(--light-background);
    filter: drop-shadow(2px 0 2px rgba(54, 54, 54, 0.2));
  }

  .Popover--positionTop.Popover--alignCenter::before,
  .Popover--positionBottom.Popover--alignCenter::before {
    left: calc(50% - 10px);
  }

  .Popover--positionLeft.Popover--alignCenter::before {
    top: calc(50% - 10px);
  }

  .Popover--positionLeft.Popover--alignBottom::before {
    top: 15px;
  }

  .Popover--positionLeft.Popover--alignTop::before {
    bottom: 10px;
  }

  .Popover[aria-hidden=false] {
    opacity: 1;
    transform: none;
  }

  .Popover__Header {
    display: none;
  }

  .Popover__Value {
    padding-left: 50px;
    padding-right: 50px;
    text-align: right;
  }

  .Popover--withMinWidth .Popover__Value,
  .Popover__ValueList--center .Popover__Value {
    text-align: center;
  }

  .Popover--small.Popover--positionTop::before {
    border-width: 8px 8px 0 8px;
  }
  .Popover--small.Popover--positionBottom::before {
    border-width: 0 8px 8px 8px;
  }
  .Popover--small .Popover__Value {
    padding: 5px 32px;
  }
  .features--heading-small .Popover--small .Popover__Value {
    font-size: 10px;
  }
  .features--heading-normal .Popover--small .Popover__Value {
    font-size: 12px;
  }
  .features--heading-large .Popover--small .Popover__Value {
    font-size: 13px;
  }
}
.Rte {
  word-break: break-word;
  /* Prevent long words to go outside the container */
  /* Simple, minimum clearfix added to every RTE text to avoid issue with float */
}
.Rte::after {
  content: "";
  display: block;
  clear: both;
}
.Rte iframe {
  max-width: 100%;
}
.Rte img {
  display: block;
  margin: 0 auto;
}
.Rte a:not(.Button) {
  color: var(--link-color);
  text-decoration: underline;
  -webkit-text-decoration-color: rgba(var(--link-color-rgb), 0.6);
          text-decoration-color: rgba(var(--link-color-rgb), 0.6);
  text-underline-position: under;
  transition: color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
  transition: color 0.2s ease-in-out, text-decoration-color 0.2s ease-in-out;
  transition: color 0.2s ease-in-out, text-decoration-color 0.2s ease-in-out, -webkit-text-decoration-color 0.2s ease-in-out;
}
.Rte a:not(.Button):hover {
  color: var(--text-color);
  -webkit-text-decoration-color: rgba(var(--text-color-rgb), 0.6);
          text-decoration-color: rgba(var(--text-color-rgb), 0.6);
}
.Rte p:not(:last-child),
.Rte ul:not(:last-child),
.Rte ol:not(:last-child) {
  margin-bottom: 1.6em;
}
.Rte img,
.Rte blockquote,
.Rte .VideoWrapper,
.Rte .Form {
  margin-top: 2.4em;
  margin-bottom: 2.4em;
}
.Rte ul, .Rte ol {
  margin-left: 30px;
  padding-left: 0;
  list-style-position: outside;
}
.Rte li {
  padding: 5px 0;
}
.Rte h1,
.Rte h2,
.Rte h3,
.Rte h4,
.Rte h5,
.Rte h6 {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  color: var(--heading-color);
  transition: color 0.2s ease-in-out;
}
.features--heading-uppercase .Rte h1,
.features--heading-uppercase .Rte h2,
.features--heading-uppercase .Rte h3,
.features--heading-uppercase .Rte h4,
.features--heading-uppercase .Rte h5,
.features--heading-uppercase .Rte h6 {
  letter-spacing: 0.2em;
  text-transform: uppercase;
}
.Rte h1 {
  margin: 2.2em 0 0.8em;
}
.Rte h2 {
  margin: 2.2em 0 0.9em;
}
.Rte h3 {
  margin: 2.2em 0 1.2em;
}
.Rte h4 {
  margin: 2.2em 0 1.4em;
}
.Rte h5 {
  margin: 2.2em 0 1.6em;
}
.Rte h6 {
  margin: 2.2em 0 1.8em;
}
.Rte blockquote {
  margin-left: 0;
  padding: 6px 0 6px 40px;
  font-size: 1.15em;
  line-height: 1.75;
  border-left: 3px solid rgba(var(--border-color-rgb), 0.6);
}
.Rte p:last-child,
.Rte blockquote:last-child,
.Rte ul:last-child,
.Rte ol:last-child,
.Rte h1:last-child,
.Rte h2:last-child,
.Rte h3:last-child,
.Rte h4:last-child,
.Rte h5:last-child,
.Rte h6:last-child {
  margin-bottom: 0;
}

/**
 * ----------------------------------------------------------------------------
 * POLICY PAGES
 * ----------------------------------------------------------------------------
 */

.shopify-policy__container {
  margin-top: 50px;
  margin-bottom: 50px;
  max-width: 680px;
}

@media screen and (min-width: 1008px) {
  .Rte img,
  .Rte .VideoWrapper,
  .Rte .Form {
    margin-top: 3em;
    margin-bottom: 3em;
  }
  .Rte blockquote {
    margin-left: 40px;
  }
}
.shopify-section--hidden {
  display: none;
}

.shopify-section--bordered + .shopify-section--bordered {
  border-top: 1px solid var(--border-color);
}

/* When a spacing is applied to a bordered section, as it is isolated, we use padding, otherwise margin */
.Section--spacingNormal {
  margin: 50px 0;
}

.Section--spacingLarge,
.Section--spacingExtraLarge {
  margin: 90px 0;
}

.shopify-section--bordered > .Section--spacingNormal {
  padding: 50px 0;
  margin-top: 0;
  margin-bottom: 0;
}

.shopify-section--bordered > .Section--spacingLarge,
.shopify-section--bordered > .Section--spacingExtraLarge {
  padding: 90px 0;
  margin-top: 0;
  margin-bottom: 0;
}

.SectionHeader:not(:only-child) {
  margin-bottom: 40px;
}

.SectionHeader--center {
  text-align: center;
}

/*
  This is just a hack to slightly increase the selector specificity. What this does is actually taking into account the line height (1.65)
  and removing the remaining amount in both direction (0.65 / 2 => 0.325) to have more easier and consistent alignments
*/
.SectionHeader__Heading.SectionHeader__Heading,
.SectionHeader__SubHeading.SectionHeader__SubHeading {
  margin-top: -0.325em;
}

.SectionHeader__SubHeading + .SectionHeader__Heading,
.SectionHeader__SubHeading + .SectionHeader__TabList,
.SectionHeader__Description {
  margin-top: 16px;
}

.SectionHeader__Description {
  max-width: 530px;
}

.SectionHeader__Description--wide{
  width: 100%;
  max-width: 760px;
}

.SectionHeader__Description, .SectionHeader__Description--seo{
  margin:0 auto;
  margin-bottom: 1.6em;
}
.SectionHeader__Description--seo{
  text-align: center;

}
.SectionHeader__Description a {
  text-decoration: underline;
  -webkit-text-decoration-color: currentColor;
          text-decoration-color: currentColor;
  text-underline-position: under;
}

.SectionHeader--center .SectionHeader__Description {
  margin-left: auto;
  margin-right: auto;
}

.SectionHeader__ButtonWrapper {
  margin-top: 20px;
}

.SectionHeader__IconHolder {
  margin-top: 30px;
}

/* The SectionFooter is usually use at the end of a given section to redirect somewhere else */
.SectionFooter {
  margin-top: 50px;
  text-align: center;
}

@media screen and (min-width: 641px) {
  .SectionHeader__Heading--emphasize {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 22px)) !important;
  }
}
@media screen and (min-width: 1008px) {
  .Section--spacingNormal {
    margin: 80px 0;
  }

  .Section--spacingLarge {
    margin: 120px 0;
  }

  .Section--spacingExtraLarge {
    margin: 145px 0;
  }

  .shopify-section--bordered > .Section--spacingNormal {
    padding: 80px 0;
  }

  .shopify-section--bordered > .Section--spacingLarge {
    padding: 120px 0;
  }

  .shopify-section--bordered > .Section--spacingExtraLarge {
    padding: 145px 0;
  }

  .SectionHeader:not(:only-child) {
    margin-bottom: 70px;
  }

  .SectionHeader__Description {
    margin-top: 24px;
  }

  .SectionHeader__ButtonWrapper {
    margin-top: 30px;
  }

  .SectionFooter {
    margin-top: 80px;
  }
}
.TableWrapper {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.Table,
.Rte table {
  width: 100%;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  border-collapse: separate;
  white-space: nowrap;
}
.Table th,
.Table td,
.Rte table th,
.Rte table td {
  padding: 18px 10px;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
}
.Table th:first-child,
.Table td:first-child,
.Table tfoot td:empty + td,
.Rte table th:first-child,
.Rte table td:first-child,
.Rte table tfoot td:empty + td {
  padding-left: 0;
}
.Table th:last-child,
.Table td:last-child,
.Rte table th:last-child,
.Rte table td:last-child {
  padding-right: 0;
}
.Table th,
.Rte table th {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  text-transform: uppercase;
  letter-spacing: 0.2em;
}
.Table thead th:first-child:empty,
.Table tbody th:first-child,
.Rte table thead th:first-child:empty,
.Rte table tbody th:first-child {
  position: sticky;
  left: 0;
  min-width: 40px;
  max-width: 100px;
  z-index: 1;
  background: var(--background);
  white-space: normal;
}
.Table tbody th:first-child,
.Rte table tbody th:first-child {
  border-right: 1px solid var(--border-color);
}
.Table tfoot td:empty,
.Rte table tfoot td:empty {
  border-bottom: none;
}

.Table--large td {
  padding-top: 25px;
  padding-bottom: 25px;
}

.Table--noBorder tbody tr:not(:last-child) td {
  border-bottom: none;
}

@media screen and (min-width: 1140px) {
  .Table,
  .Rte table {
    white-space: normal;
  }

  .Table--large td {
    padding-top: 35px;
    padding-bottom: 35px;
  }
}
.TabList {
  white-space: nowrap;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.TabList__Item {
  position: relative;
}
.TabList__Item::after {
  position: relative;
  display: block;
  content: "";
  bottom: 1px;
  left: 0;
  height: 1px;
  width: 0;
  background: var(--heading-color);
  transition: width 0.25s ease-in-out;
}

.TabList__Item + .TabList__Item {
  margin-left: 28px;
}

.TabList__Item.is-active::after {
  width: 100%;
}
.features--heading-uppercase .TabList__Item.is-active::after {
  width: calc(100% - 0.2em);
}

.TabPanel {
  display: none;
}

.TabPanel[aria-hidden=false] {
  display: block;
}

@media screen and (min-width: 641px) {
  .TabList__Item + .TabList__Item {
    margin-left: 45px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Layout override
 * ----------------------------------------------------------------------------
 */

.template-customers .OrderAddresses .Grid__Cell + .Grid__Cell {
  margin-top: 50px;
}

@media screen and (min-width: 641px) and (max-width: 1007px) {
  .template-customers .OrderAddresses .Grid__Cell + .Grid__Cell {
    margin-top: 0;
  }
}

@media screen and (min-width: 1140px) {
  .template-customers .OrderAddresses .Grid__Cell + .Grid__Cell {
    margin-top: 65px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Addresses
 * ----------------------------------------------------------------------------
 */

.AddressList .Grid__Cell {
  margin-bottom: 40px;
}

.AccountAddress span {
  display: inline-block;
  margin-bottom: 12px;
}

@media screen and (max-width: 640px) {
  .Modal--address {
    height: 100%;
    width: 100%;
    max-width: none;
    max-height: none;
  }

  .Modal--address .Modal__Header {
    margin-top: 35px;
  }
}

@media screen and (min-width: 641px) and (max-width: 1007px) {
  .OrderAddresses {
    width: 100%;
    max-width: none;
  }
}

@media screen and (min-width: 641px) {
  .AddressList .Grid__Cell {
    margin-bottom: 60px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Account table
 * ----------------------------------------------------------------------------
 */

.AccountTable th {
  padding-top: 0;
  padding-bottom: 10px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
}

.AccountTable tfoot {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 14px));
}

.AccountTable tfoot span + span {
  padding-left: 18px;
}

.AccountTable .CartItem__PriceList {
  margin-bottom: 0;
}

@media screen and (max-width: 640px) {
  .AccountTable .CartItem__ImageWrapper {
    width: 70px;
    min-width: 70px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Article inner
 * ----------------------------------------------------------------------------
 */

.Article__ImageWrapper {
  overflow: hidden;
  height: 215px;
}

.Article__Image {
  position: relative;
  height: 100%;
  width: 100%;
  background-size: cover;
  background-position: center;
}

@supports (--css: variables) {
  .Article__Image {
    height: calc(100% + 0px);
    height: calc(100% + var(--announcement-bar-height, 0px));
    top: calc(-1 * 0px);
    top: calc(-1 * var(--announcement-bar-height, 0px));
  }
}

.Article__Wrapper {
  position: relative;
  max-width: 620px;
  margin: 0 auto 90px auto;
  padding: 24px 24px 0 24px;
  background: var(--background);
}

.Article__Header {
  margin-bottom: 35px;
}

.Article__Meta {
  margin-bottom: 18px;
}

.Article__MetaItem + .Article__MetaItem::before {
  position: relative;
  display: inline-block;
  content: "";
  height: 4px;
  width: 4px;
  border-radius: 100%;
  margin: 0 15px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 10px));
  vertical-align: middle;
  background: currentColor;
}

.Article__Footer {
  margin-top: 45px;
}

.Article__ShareButtons {
  margin-top: 42px;
}

@media screen and (min-width: 641px) {
  .Article__Wrapper {
    margin-bottom: 120px;
    padding: 40px 50px 0 50px;
  }

  .Article__ImageWrapper {
    height: 335px;
  }

  .Article__ImageWrapper + .Article__Wrapper {
    margin-top: -45px;
  }

  .Article__Header {
    margin-bottom: 45px;
  }

  .Article__Footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 65px;
  }

  .Article__ShareButtons {
    margin-top: 0;
  }
}

@media screen and (min-width: 1008px) {
  .Article__Wrapper {
    max-width: 780px;
  }

  .Article__ImageWrapper {
    height: 450px;
  }

  .Article__ImageWrapper + .Article__Wrapper {
    margin-top: -70px;
  }
}

@media screen and (min-width: 1140px) {
  .Article__ImageWrapper {
    height: 600px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Comments
 * ----------------------------------------------------------------------------
 */

.Article__CommentForm {
  margin-top: 40px;
}

.Article__Comments,
.Article__CommentFormWrapper {
  margin: 80px 0;
}

.Article__Comments .Pagination {
  margin-top: 40px;
}

.ArticleComment {
  margin-top: 35px;
}

.ArticleComment + .ArticleComment {
  padding-top: 35px;
  border-top: 1px solid var(--border-color);
}

.ArticleComment__Body {
  margin-bottom: 18px;
}

.ArticleComment__Date {
  margin-left: 15px;
}

@media screen and (min-width: 641px) {
  .Article__Comments,
  .Article__CommentFormWrapper {
    margin: 105px 0;
  }

  .Article__Comments .Pagination {
    margin-top: 80px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Toolbar
 * ----------------------------------------------------------------------------
 */

.ArticleToolbar {
  position: fixed;
  display: flex;
  top: 0;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px 16px 30px;
  background: var(--secondary-elements-background);
  color: var(--secondary-elements-text-color);
  z-index: 2;
  pointer-events: none;
  transform: translateY(-100%);
  opacity: 0;
  will-change: transform, opacity;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}

@supports (--css: variables) {
  .ArticleToolbar {
    top: calc(0 * var(--header-height));
    top: calc(var(--use-sticky-header, 0) * var(--header-height));
  }
}

.ArticleToolbar.is-visible {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

.ArticleToolbar .Link:hover {
  color: var(--secondary-elements-text-color);
}

.ArticleToolbar .Text--subdued {
  color: var(--secondary-elements-text-color-light);
}

.ArticleToolbar__ArticleTitle {
  position: relative;
  display: inline-flex;
  max-width: 285px;
  width: 285px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: clip;
  color: var(--secondary-elements-text-color);
}

.ArticleToolbar__ShareList {
  display: inline-block;
}

.ArticleToolbar__ShareList .HorizontalList {
  display: inline-block;
  margin-left: 20px;
}

.ArticleToolbar__Nav {
  display: inline-block;
  margin-left: 50px;
}

.ArticleToolbar__Nav svg {
  color: var(--secondary-elements-text-color);
}

.ArticleToolbar__NavItemSeparator {
  display: inline-block;
  position: relative;
  content: "";
  width: 1px;
  height: 12px;
  margin: 0 18px;
  background: rgba(var(--secondary-elements-text-color-rgb), 0.8);
  vertical-align: middle;
}

.ArticleToolbar__NavItem svg {
  vertical-align: -1px;
}

.ArticleToolbar__NavItem--prev svg {
  margin-right: 6px;
}

.ArticleToolbar__NavItem--next svg {
  margin-left: 6px;
}

@media screen and (min-width: 1008px) {
  .ArticleToolbar__ArticleTitle {
    max-width: 400px;
    width: 400px;
  }
}

@media screen and (min-width: 1140px) {
  .ArticleToolbar {
    padding-left: 50px;
    padding-right: 50px;
  }

  .ArticleToolbar__ArticleTitle {
    max-width: 550px;
    width: 550px;
  }

  .ArticleToolbar__Nav {
    margin-left: 100px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Article navigation
 * ----------------------------------------------------------------------------
 */

.ArticleNav {
  padding: 75px 0;
  background: var(--secondary-elements-background);
  color: var(--secondary-elements-text-color);
}

.ArticleNav .Heading {
  color: inherit;
}

.ArticleNav__Item {
  display: block;
}

.ArticleNav__Image {
  height: 350px;
  background-size: cover;
  background-position: center;
}

@media screen and (max-width: 640px) {
  .ArticleNav .Grid__Cell + .Grid__Cell {
    margin-top: 50px;
  }
}

@media screen and (min-width: 641px) {
  .ArticleNav {
    padding: 140px 0;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Article item
 * ----------------------------------------------------------------------------
 */

.ArticleListWrapper {
  max-width: 1260px;
  margin: 0 auto 60px auto;
}

.ArticleList {
  margin-bottom: -60px;
}

.ArticleList .Grid__Cell {
  margin-bottom: 60px;
}

.js .features--show-element-staggering .ArticleItem {
  visibility: hidden;
}

.ArticleItem__ImageWrapper {
  display: block;
  margin-bottom: 22px;
  background-size: cover;
  overflow: hidden;
}

.ArticleItem__Image {
  display: block;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  font-family: "object-fit: cover; object-position: center;";
}

.features--show-image-zooming .ArticleItem__Image {
  transform: scale(1);
  transition: opacity 0.3s ease, transform 8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.features--show-image-zooming .ArticleList .ImageHero__ImageWrapper {
  transition: transform 8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .features--show-image-zooming .ArticleItem:hover .ArticleItem__Image,
  .features--show-image-zooming .ArticleList .ImageHero:hover .ImageHero__ImageWrapper {
    transform: scale(1.2);
  }
}

.ArticleItem__Content {
  margin: 0 8px;
}

.ArticleItem__Category {
  display: block;
  margin-bottom: 16px;
}

.ArticleItem__Excerpt {
  margin-bottom: 20px;
}

@media screen and (min-width: 641px) {
  .ArticleList--withFeatured .Grid__Cell:first-child {
    margin-bottom: 40px;
  }

  .ArticleItem__Content {
    margin: 0 18px;
  }
}

@media screen and (min-width: 1008px) {
  .ArticleListWrapper {
    margin-bottom: 100px;
  }

  .ArticleList {
    margin-bottom: -100px;
  }

  .ArticleList .Grid__Cell {
    margin-bottom: 100px;
  }

  .ArticleList--withFeatured .Grid__Cell:first-child {
    margin-bottom: 60px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Blog
 * ----------------------------------------------------------------------------
 */

.Blog__RssLink {
  margin-left: 14px;
  vertical-align: baseline;
}

.Blog__RssLink svg {
  width: 10px;
  height: 10px;
}

.Blog__TagList {
  padding-top: 8px;
}

.Blog__TagList .Link.is-active::after {
  display: block;
  content: "";
  width: calc(100% - 0.15em);
  height: 1px;
  background: currentColor;
}

@media screen and (max-width: 640px) {
  .Blog__TagList .HorizontalList__Item {
    margin: 5px 10px;
  }
}

@media screen and (min-width: 641px) {
  .ArticleList + .Pagination {
    margin-top: 80px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * General
 * ----------------------------------------------------------------------------
 */
@-webkit-keyframes cartEmptyOpening {
  from {
    transform: translate(-50%, calc(-50% + 35px));
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}
@keyframes cartEmptyOpening {
  from {
    transform: translate(-50%, calc(-50% + 35px));
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

.Cart__ShippingNotice {
  position: sticky;
  top: 0;
  margin-bottom: 0;
  padding-top: 9px;
  padding-bottom: 9px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  border-bottom: 1px solid var(--border-color);
  line-height: normal;
  z-index: 1;
  background: var(--background);
}

.Cart__Empty {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  -webkit-animation: cartEmptyOpening 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
          animation: cartEmptyOpening 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media screen and (min-width: 641px) {
  .Cart__ShippingNotice {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
  }
}

/**
 * ----------------------------------------------------------------------------
 * Items
 * ----------------------------------------------------------------------------
 */

.CartItemWrapper {
  overflow: hidden;
}

.CartItem {
  display: table;
  table-layout: fixed;
  margin: 30px 0;
  width: 100%;
}

.CartItem__ImageWrapper,
.CartItem__Info {
  display: table-cell;
  vertical-align: middle;
}

.CartItem__ImageWrapper {
  width: 90px;
  min-width: 90px;
  text-align: center;
}

.CartItem__Info {
  padding-left: 25px;
}

.CartItem__Title {
  width: 100%;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.CartItem__Title,
.CartItem__Variant,
.CartItem__PlanAllocation,
.CartItem__PropertyList {
  margin-bottom: 0.45em;
}

.CartItem__Meta,
.CartItem__LinePriceList,
.CartItem__DiscountList {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 9px));
}

.CartItem__PropertyList {
  list-style: none;
  font-style: italic;
}

.CartItem__DiscountList {
  list-style: none;
}

.CartItem__Discount {
  display: inline-block;
  margin-top: 6px;
  padding: 4px 8px;
  background: rgba(var(--product-sale-price-color-rgb), 0.1);
  color: var(--product-sale-price-color);
}

.CartItem__Discount svg {
  margin-right: 4px;
  vertical-align: text-bottom;
}

.CartItem__Discount + .CartItem__Discount {
  margin-top: 4px;
}

.CartItem__UnitPriceMeasurement {
  margin-top: 4px;
}

.CartItem__Actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 16px;
}

.CartItem__Remove {
  margin: 8px 0;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 8px));
}

.CartItem__Remove::before {
  background: var(--text-color-light);
}

@media screen and (min-width: 641px) {
  .CartItem__ImageWrapper {
    width: 120px;
  }

  .CartItem__Title {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  }

  .CartItem__Meta,
  .CartItem__LinePriceList {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  }

  .CartItem__Remove {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 9px));
  }

  .CartItem__Actions {
    margin-top: 20px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Bottom
 * ----------------------------------------------------------------------------
 */

.Cart .Drawer__Footer,
.Cart__OffscreenNoteContainer {
  padding: 14px 24px 24px 24px;
}

.Cart__Checkout {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 18px;
}

.Cart__OffscreenNoteContainer {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 1;
  background: var(--background);
  border-top: 1px solid var(--border-color);
  transform: translateY(100%);
  transition: transform 0.25s ease-in-out;
}

.Cart__OffscreenNoteContainer[aria-hidden=false] {
  transform: translateY(0);
  box-shadow: 1px 0 6px rgba(54, 54, 54, 0.2);
}

.Cart__Note {
  margin-top: 10px;
}

.Cart__Taxes {
  margin-bottom: 8px;
}

.Cart__Discount svg {
  margin-right: 8px;
  vertical-align: sub;
}

.has-note-open[aria-hidden=false] .Drawer__Header,
.has-note-open[aria-hidden=false] .Drawer__Main {
  opacity: 0.4;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

@supports (padding: max(0px)) {
  .Cart .Drawer__Footer,
  .Cart__OffscreenNoteContainer {
    padding-bottom: max(24px, env(safe-area-inset-bottom, 0px) + 24px);
  }
}

@media screen and (min-width: 641px) {
  .Cart .Drawer__Footer,
  .Cart__OffscreenNoteContainer {
    padding: 20px 30px 30px 30px;
  }

  .Cart__NoteButton + .Cart__Taxes {
    margin-top: 4px;
  }

  @supports (padding: max(0px)) {
    .Cart .Drawer__Footer,
    .Cart__OffscreenNoteContainer {
      padding-bottom: max(30px, env(safe-area-inset-bottom, 0px) + 30px);
    }
  }
}

/**
 * ----------------------------------------------------------------------------
 * Adjustments for dedicated cart page
 * ----------------------------------------------------------------------------
 */

.Cart--expanded .Cart__Footer {
  padding-top: 25px;
  border-top: 1px solid var(--border-color);
}

.Cart--expanded .Cart__Recap {
  text-align: right;
}

.Cart--expanded .Cart__Recap,
.Cart--expanded .Cart__Recap .Cart__Checkout {
  margin-top: 16px;
}

.Cart--expanded .Cart__Checkout {
  margin-left: auto;
}

@media screen and (max-width: 640px) {
  .CartItem__Info ~ .CartItem__Actions,
  .CartItem__Info ~ .CartItem__LinePriceList {
    display: none;
  }
}

@media screen and (min-width: 641px) {
  .Cart--expanded .Cart__ItemList {
    display: table;
    table-layout: auto;
    border-spacing: 0 30px;
    width: 100%;
  }

  .Cart--expanded .Cart__Head {
    display: table-header-group;
  }

  .Cart--expanded .Cart__HeadItem {
    display: table-cell;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
  }

  .Cart--expanded .CartItem {
    display: table-row;
  }

  .Cart--expanded .CartItem__Info {
    max-width: 300px;
    width: 300px;
  }

  .Cart--expanded .CartItem__Info > .CartItem__Actions {
    display: none;
  }

  .Cart--expanded .CartItem__Info ~ .CartItem__Actions,
  .Cart--expanded .CartItem__Info ~ .CartItem__LinePriceList {
    display: table-cell;
    vertical-align: middle;
  }

  .Cart--expanded .CartItem__QuantitySelector {
    margin-bottom: 6px;
  }

  .Cart--expanded .Cart__Footer {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

  .Cart--expanded .Cart__Recap,
  .Cart--expanded .Cart__NoteContainer {
    display: table-cell;
  }

  .Cart--expanded .Cart__NoteContainer {
    width: 340px;
  }

  .Cart--expanded .Cart__Checkout {
    width: auto;
  }
}

@media screen and (min-width: 1140px) {
  .Cart--expanded .CartItem__Info {
    max-width: 425px;
    width: 425px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Shipping estimator
 * ----------------------------------------------------------------------------
 */

.ShippingEstimator__Results {
  height: 0;
  overflow: hidden;
  transition: height 0.25s ease-in-out;
}

.ShippingEstimator__Error,
.ShippingEstimator__ResultsInner {
  margin-top: 32px;
}

.ShippingEstimator__Results p {
  margin-bottom: 0.5em;
}

@media screen and (max-width: 640px) {
  .ShippingEstimator__Submit {
    width: 100%;
  }

  .ShippingEstimator__Country,
  .ShippingEstimator__Province,
  .ShippingEstimator__Zip {
    margin-bottom: 20px;
  }
}

@media screen and (min-width: 641px) {
  .ShippingEstimator__Form {
    display: flex;
  }

  .ShippingEstimator__Form > *:not(:first-child) {
    margin-left: 20px;
  }

  .ShippingEstimator__Country,
  .ShippingEstimator__Province {
    margin-bottom: 0;
  }

  .ShippingEstimator__Zip {
    margin-bottom: 0;
    max-width: 130px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Collection item (for list of collections)
 * ----------------------------------------------------------------------------
 */

.CollectionList::after {
  content: "flickity";
  display: none;
}

.CollectionItem {
  display: block;
}

.CollectionItem__Wrapper {
  position: relative;
  /* height: 500px; */
  padding-top: 100%;
  background-size: cover;
  background-position: center;
  overflow: hidden;
}

.CollectionItem__Wrapper--small {
  /* height: 450px; */
}

.CollectionItem__Wrapper--large {
  /* height: 550px; */
}

.CollectionItem__ImageWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.features--show-image-zooming .CollectionItem__ImageWrapper {
  transform: scale(1);
  transition: transform 8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.CollectionItem__Image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.CollectionItem__Content {
  position: absolute;
  padding: 0 24px;
  margin-bottom: 0 !important;
  top: 50%;
  left: 50%;
  width: 100%;
  transform: translate(-50%, -50%);
  text-align: left;
}

.CollectionItem__Content--bottomCenter,
.CollectionItem__Content--middleCenter {
  text-align: center;
}

.CollectionItem__Content--bottomRight,
.CollectionItem__Content--middleRight {
  text-align: right;
}

.CollectionItem__Content--bottomLeft,
.CollectionItem__Content--bottomRight,
.CollectionItem__Content--bottomCenter {
  bottom: 24px;
  top: auto;
  left: 0;
  transform: none;
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .features--show-image-zooming .CollectionItem:hover .CollectionItem__ImageWrapper {
    transform: scale(1.2);
  }
}

@media screen and (max-width: 640px) {
  .CollectionList--grid {
    margin: 12px 12px 12px 12px;
  }

  .CollectionList--grid .CollectionItem {
    padding: 12px;
  }

  .CollectionList:not(.CollectionList--grid) .CollectionItem__Content--bottomLeft,
  .CollectionList:not(.CollectionList--grid) .CollectionItem__Content--bottomRight,
  .CollectionList:not(.CollectionList--grid) .CollectionItem__Content--bottomCenter {
    bottom: 70px;
  }
}

@media screen and (min-width: 641px) {
  .CollectionList {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
  }

  .CollectionList::after {
    content: ""; /* disable Flickity */
  }

  .CollectionList--spaced {
    margin: 15px;
  }

  .CollectionList--spaced .CollectionItem {
    padding: 15px;
  }

  .CollectionItem {
    display: block !important;
    flex: 0 1 0;
    min-width: 50%;
  }

  .CollectionItem--expand {
    flex-grow: 1;
  }

  .CollectionItem__Content {
    padding: 0 40px;
  }

  .CollectionItem__Content--bottomLeft,
  .CollectionItem__Content--bottomRight,
  .CollectionItem__Content--bottomCenter {
    bottom: 40px;
  }
}

@media screen and (min-width: 1140px) {
  .CollectionItem {
    min-width: 33.33333%;
  }

  .CollectionItem__Wrapper {
    /* height: 600px; */
  }

  .CollectionItem__Wrapper--small {
    /* height: 500px; */
  }

  .CollectionItem__Wrapper--large {
    /* height: 700px; */
  }

  .CollectionItem__Content {
    padding: 0 50px;
  }

  .CollectionItem__Content--bottomLeft,
  .CollectionItem__Content--bottomRight,
  .CollectionItem__Content--bottomCenter {
    bottom: 50px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Collection toolbar
 * ----------------------------------------------------------------------------
 */

.CollectionToolbar {
  position: sticky;
  display: flex;
  justify-content: space-between;
  width: 100%;
  background: var(--background);
  box-shadow: 1px 1px var(--border-color), -1px -1px var(--border-color);
  z-index: 2;
}

.CollectionToolbar--top {
  top: var(--header-base-height);
}

@supports (--css: variables) {
  .CollectionToolbar--top {
    top: calc(var(--header-height) * 0);
    top: calc(var(--header-height) * var(--use-sticky-header, 0));
  }
}

.supports-sticky .CollectionToolbar--bottom {
  bottom: 0; /* as of today the best polyfill I've found does not support polyfilling sticky on bottom. As a consequence on those old
                browsers, the stickiness is always at the top */
}

.CollectionToolbar__Group {
  display: flex;
  flex: 1 0 auto;
}

.CollectionToolbar__Item {
  flex: 1 0 auto;
  padding: 13px 0;
  border-left: 1px solid var(--border-color);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
}

.CollectionToolbar__Item--sort .Icon--select-arrow {
  height: 6px;
  margin-left: 2px;
  pointer-events: none;
}

.CollectionToolbar__Item--layout {
  flex: none;
  width: 95px;
  line-height: 1;
  cursor: auto;
  cursor: initial;
}

.CollectionToolbar__LayoutType {
  margin: 0 6px;
  opacity: 0.2;
  transition: opacity 0.25s ease-in-out;
}

.CollectionToolbar__LayoutType svg {
  width: 18px;
  height: 18px;
}

.CollectionToolbar__LayoutType.is-active {
  opacity: 1;
}

@media screen and (max-width: 640px) {
  .CollectionToolbar__Item:first-child {
    border-left: none;
  }
}

@media screen and (min-width: 641px) {
  .CollectionToolbar--reverse,
  .CollectionToolbar__Group {
    flex-direction: row-reverse;
  }

  .CollectionToolbar__Group {
    flex: none;
  }

  .CollectionToolbar__Item {
    padding: 18px 0;
  }

  .CollectionToolbar__Group .CollectionToolbar__Item {
    padding-left: 45px;
    padding-right: 45px;
  }

  .CollectionToolbar__Item--layout {
    border-left: none;
    width: 115px;
    border-right: 1px solid var(--border-color);
    white-space: nowrap;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Collection filters
 * ----------------------------------------------------------------------------
 */

.CollectionFilters .Collapsible:first-child {
  border-top: none;
}

.CollectionFilters__ClearButton {
  margin-top: 24px;
}

@media screen and (min-width: 641px) {
  .CollectionFilters .Drawer__Main {
    padding-top: 35px;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Product item
 * ----------------------------------------------------------------------------
 */

.ProductItem {
  text-align: center;
  white-space: normal;
}

.js .features--show-element-staggering .ProductList--grid .ProductItem {
  visibility: hidden;
}

.ProductItem__Wrapper {
  position: relative;
}

.ProductItem__ImageWrapper {
  display: block;
}

.ProductItem__Image--alternate {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0 !important;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.ProductItem__LabelList {
  position: absolute;
  left: 10px;
  top: 6px;
  text-align: left;
}

.ProductItem__Label {
  display: block;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 9px));
  margin: 4px 0;
  padding: 2px 6px;
  background: var(--background);
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
}

.ProductItem__Wishlist {
  position: absolute;
  top: 14px;
  right: 15px;
}

.ProductItem__Wishlist .wk-button {
  margin: 0;
  padding: 0;
}

.ProductItem__Wishlist .wk-button .wk-button__icon {
  margin: 0;
}

.ProductItem__Wishlist .wk-button .wk-button__label {
  display: none;
}

@media screen and (max-width: 640px) {
  .ProductItem__Wishlist {
    top: 13px;
    right: 12px;
  }
}

.ProductItem__Info {
  margin-top: 20px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
}

.features--heading-uppercase .ProductItem__Info {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
}

.ProductItem__Info--left {
  text-align: left;
}

.ProductItem__Info--right {
  text-align: right;
}

.ProductItem__Rating {
  margin-top: 4px;
  margin-bottom: 4px;
}

.ProductItem__Vendor,
.ProductItem__Title {
  display: block;
  margin-bottom: 4px;
}

.ProductItem__ColorSwatchList {
  margin-top: 15px;
}

.ProductItem__ColorSwatchItem {
  display: inline-block;
  margin: 0 5px;
}

.ProductItem__ColorSwatchList + .ProductItem__PriceList {
  margin-top: 12px;
}

.ProductItem__UnitPriceMeasurement {
  margin-top: 4px;
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .features--show-price-on-hover .ProductItem__PriceList--showOnHover {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.35s ease-in-out;
  }

  .features--show-price-on-hover .ProductItem:hover .ProductItem__PriceList--showOnHover {
    opacity: 1;
    transform: translateY(0);
  }

  .ProductItem__ImageWrapper--withAlternateImage:hover .ProductItem__Image {
    opacity: 0 !important;
  }

  .ProductItem__ImageWrapper--withAlternateImage:hover .ProductItem__Image--alternate {
    opacity: 1 !important;
  }
}

@media (-moz-touch-enabled: 1), (hover: none) {
  .ProductItem__Image--alternate {
    display: none; /* This prevents the image to be lazy-loaded */
  }
}

@media screen and (max-width: 640px) {
  .ProductItem__Rating .rating__star {
    width: 10px;
    height: 10px;
  }

  .ProductItem__Rating .rating__caption {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 9px));
  }
}

@media screen and (min-width: 641px) {
  .ProductItem__Label {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  }

  .ProductItem__Info {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
  }

  .features--heading-uppercase .ProductItem__Info {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  }
}

@media screen and (min-width: 1008px) {
  .ProductItem--horizontal {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .ProductItem--horizontal .ProductItem__Wrapper {
    display: flex;
    align-items: center;
  }

  .ProductItem--horizontal .ProductItem__ImageWrapper {
    min-width: 100px;
  }

  .ProductItem--horizontal .ProductItem__Info {
    margin: 0 30px 0 40px;
    text-align: left;
  }

  .ProductItem--horizontal .ProductItem__ViewButton {
    flex-shrink: 0;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Product grid
 * ----------------------------------------------------------------------------
 */

.CollectionInner {
  margin-top: 24px;
}

.ProductList--grid {
  padding: 0 12px;
}

@media screen and (max-width: 640px) {
  .ProductList--grid {
    margin-left: -10px;
  }

  .ProductList--removeMargin {
    margin-bottom: -35px !important;
  }

  .ProductList--grid > .Grid__Cell {
    padding-left: 10px;
    margin-bottom: 35px;
  }
}

@media screen and (min-width: 641px) {
  .CollectionInner {
    margin-top: 50px;
  }

  .ProductList--grid {
    padding: 0 24px;
    margin-left: -24px;
  }

  .ProductList--removeMargin {
    margin-bottom: -50px !important;
  }

  .ProductList--grid > .Grid__Cell {
    padding-left: 24px;
    margin-bottom: 50px;
  }
}

@media screen and (min-width: 1008px) {
  .CollectionInner {
    display: flex;
  }

  .CollectionInner__Sidebar {
    position: sticky;
    top: 200px;
    flex: none;
    align-self: flex-start;
    width: 200px;
    margin: 0 16px 0 24px;
    padding-bottom: 50px;
  }

  @supports (--css: variables) {
    .CollectionInner__Sidebar {
      top: calc(var(--header-height) + 50px);
    }
  }

  @supports (--css: variables) {
    .CollectionInner__Sidebar--withTopToolbar {
      top: calc(var(--header-height) * 0 + 105px);
      top: calc(var(--header-height) * var(--use-sticky-header, 0) + 105px);
    }
  }

  .CollectionInner__Products {
    flex: 1 0 0;
  }

  .CollectionInner__Sidebar .Collapsible {
    padding: 0;
  }
}

@media screen and (min-width: 1140px) {
  .CollectionInner__Sidebar {
    margin-right: 10px;
    margin-left: 50px;
  }

  .CollectionInner .Pagination {
    margin-bottom: 80px;
  }

  .ProductList--grid {
    padding: 0 50px;
  }

  .ProductList--grid[data-desktop-count="2"] {
    margin-left: calc(-1 * var(--horizontal-spacing-two-products-per-row));
  }

  .ProductList--grid[data-desktop-count="2"] > .Grid__Cell {
    padding-left: var(--horizontal-spacing-two-products-per-row);
    margin-bottom: var(--vertical-spacing-two-products-per-row);
  }

  .ProductList--removeMargin[data-desktop-count="2"] {
    margin-bottom: calc(-1 * var(--vertical-spacing-two-products-per-row)) !important;
  }

  .ProductList--grid[data-desktop-count="4"],
  .ProductList--grid[data-desktop-count="3"] {
    margin-left: calc(-1 * var(--horizontal-spacing-four-products-per-row));
  }

  .ProductList--grid[data-desktop-count="4"] > .Grid__Cell,
  .ProductList--grid[data-desktop-count="3"] > .Grid__Cell {
    padding-left: var(--horizontal-spacing-four-products-per-row);
    margin-bottom: var(--vertical-spacing-four-products-per-row);
  }

  .ProductList--removeMargin[data-desktop-count="4"],
  .ProductList--removeMargin[data-desktop-count="3"] {
    margin-bottom: calc(-1 * var(--vertical-spacing-four-products-per-row)) !important;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Product carousel
 * ----------------------------------------------------------------------------
 */

.ProductList--carousel .Carousel__Cell {
  width: 62%;
  padding: 0 12px;
  vertical-align: top;
}

@media screen and (max-width: 640px) {
  .template-search .ProductList--grid,
  .template-collection .ProductList--grid {
    margin-bottom: 20px; /* hack, would require some better code */
  }
}

@media screen and (max-width: 1007px) {
  /* On phone and tablet we do not use the carousel but instead use a free scroll, which offers better performance */
  .ProductListWrapper {
    overflow: hidden;
  }

  .ProductList--carousel {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 30px; /* This is a trick to hide the scrollbar on iOS */
    margin-bottom: -30px;
  }

  .ProductList--carousel .Carousel__Cell {
    display: inline-block !important;
  }

  .ProductList--carousel .Carousel__Cell:first-child {
    margin-left: 19%; /* This is (100% - 62%) / 2, where 62% is the width of a single cell */
  }
  .ProductList--carousel .Carousel__Cell:last-child {
    margin-right: 19%; /* This is (100% - 62%) / 2, where 62% is the width of a single cell */
  }
}

@media screen and (min-width: 641px) and (max-width: 1007px) {
  .ProductList--carousel .Carousel__Cell {
    width: 48%;
    padding: 0 15px;
  }

  .ProductList--carousel .Carousel__Cell:first-child {
    margin-left: 26%; /* This is (100% - 48%) / 2, where 48% is the width of a single cell */
  }
  .ProductList--carousel .Carousel__Cell:last-child {
    margin-right: 26%; /* This is (100% - 48%) / 2, where 48% is the width of a single cell */
  }
}

@media screen and (min-width: 1008px) {
  .ProductList--carousel {
    margin: 0 90px;
  }

  .ProductList--carousel::after {
    content: "flickity";
    display: none;
  }

  .ProductList--carousel .Carousel__Cell {
    width: 33.3333%;
    left: 0;
    padding: 0 calc(var(--horizontal-spacing-four-products-per-row) / 2);
    margin-left: 0;
  }

  .ProductList--carousel .flickity-prev-next-button {
    top: calc(50% - 45px);
    width: 45px;
    height: 45px;
    stroke-width: 1px;
  }

  .ProductList--carousel .flickity-prev-next-button.next {
    right: -50px;
  }

  .ProductList--carousel .flickity-prev-next-button.previous {
    left: -50px;
  }
}

@media screen and (min-width: 1140px) {
  .ProductList--carousel .Carousel__Cell {
    width: 25%;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Product shop now
 * ----------------------------------------------------------------------------
 */

.ProductList--shopNow {
  position: static;
}

.ProductList--shopNow .Carousel__Cell {
  padding: 0 60px;
}

@media screen and (max-width: 640px) {
  .ShopNowGrid .FeaturedQuote {
    margin: 50px -24px -90px -24px;
  }
}

@media screen and (min-width: 641px) and (max-width: 1007px) {
  .ShopNowGrid .FeaturedQuote {
    margin: 60px -50px -90px -50px;
  }
}

@media screen and (min-width: 641px) {
  .ProductList--shopNow {
    padding: 0 50px;
    overflow: hidden;
  }

  .ProductList--shopNow .flickity-viewport {
    overflow: visible;
  }

  .ProductList--shopNow .Carousel__Cell {
    width: 50%;
    padding: 0 50px;
  }
}

@media screen and (min-width: 1008px) {
  .ProductList--shopNow[data-desktop-count="3"] .Carousel__Cell {
    width: 33.333333%;
  }

  .ShopNowGrid {
    display: flex;
  }

  .ShopNowGrid .FeaturedQuote {
    height: 100%;
  }
}
.Faq__Section {
  margin-bottom: 20px;
}

.Faq__Section ~ .Faq__Section {
  margin-top: 34px;
}

.Faq__Item {
  position: relative;
  margin: 14px 0;
}

.Faq__Icon {
  position: absolute;
  top: 0;
  left: 0;
  color: var(--text-color-light);
  transition: all 0.3s ease-in-out;
}

.Faq__Icon svg {
  width: 8px;
  height: 10px;
  vertical-align: baseline;
}

.Faq__Item[aria-expanded=true] .Faq__Icon {
  transform: rotateZ(90deg);
  color: var(--heading-color);
}

.Faq__ItemWrapper {
  padding-left: 26px;
}

.Faq__Question {
  display: block;
  width: 100%;
  margin-bottom: 0;
  text-align: left;
}

.Faq__AnswerWrapper {
  height: 0;
  overflow: hidden;
  visibility: hidden;
  transition: height 0.25s ease-in-out, visibility 0s ease-in-out 0.25s;
}

.Faq__Item[aria-expanded=true] .Faq__AnswerWrapper {
  visibility: visible;
  transition: height 0.25s ease-in-out;
}

.Faq__Answer {
  padding: 16px 0 22px 0;
}

.Faq__Item--lastOfSection .Faq__Answer {
  padding-bottom: 0;
}

.FaqSummary {
  list-style: none;
  margin: 0;
  padding: 0;
}

.FaqSummary__Item {
  margin-bottom: 12px;
}

.FaqSummary__Item.is-active::after {
  width: 100%;
}

.FaqSummary__Link {
  display: block;
}

.FaqSummary__LinkLabel {
  position: relative;
  display: inline-block;
}

.FaqSummary__LinkLabel::after {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  transform: scale(0, 1);
  transform-origin: left center;
  background: currentColor;
  transition: transform 0.2s linear;
}

.FaqSummary__Item.is-active .FaqSummary__LinkLabel::after {
  transform: scale(1, 1);
}

@media screen and (min-width: 641px) {
  .Faq__Section {
    margin-bottom: 34px;
  }

  .Faq__Section ~ .Faq__Section {
    margin-top: 60px;
  }
}
/**
 * As of today this section is displayed on the home page only
 */

.FeatureText {
  text-align: center;
}

.FeatureText__ContentWrapper {
  padding-left: 24px;
  padding-right: 24px;
}

.FeatureText__ImageWrapper {
  overflow: hidden;
}

.FeatureText--withImage .FeatureText__ContentWrapper {
  padding-top: 50px;
  padding-bottom: 20px;
}

.FeatureText .SectionHeader__Description {
  margin-top: 25px;
  margin-bottom: 20px;
}

@media screen and (max-width: 640px) {
  .FeatureText--imageLeft {
    display: flex;
    flex-direction: column-reverse;
  }
}

@media screen and (min-width: 641px) {
  .FeatureText__Content {
    max-width: 430px;
    margin: 0 auto;
  }

  .FeatureText--withImage {
    display: table;
    width: 100%;
    text-align: left;
    table-layout: fixed;
  }

  .FeatureText--withImage .FeatureText__ContentWrapper,
  .FeatureText--withImage .FeatureText__ImageWrapper {
    display: table-cell;
    width: 50%;
    vertical-align: middle;
  }

  .FeatureText--withImage .FeatureText__ContentWrapper {
    padding: 50px;
  }

  .FeatureText--imageRight .FeatureText__ContentWrapper {
    padding-right: 40px;
  }

  .FeatureText--imageRight .FeatureText__Content,
  .FeatureText--imageRight .AspectRatio {
    margin-right: 0;
  }

  .FeatureText--imageRight .AspectRatio {
    text-align: right;
  }

  .FeatureText--imageLeft .FeatureText__ContentWrapper {
    padding-left: 40px;
  }

  .FeatureText--imageLeft .FeatureText__Content,
  .FeatureText--imageLeft .AspectRatio {
    margin-left: 0;
  }

  .FeatureText--imageLeft .AspectRatio {
    text-align: left;
  }
}

@media screen and (min-width: 1140px) {
  .FeatureText--imageRight .FeatureText__ContentWrapper {
    padding-right: 100px;
  }

  .FeatureText--imageLeft .FeatureText__ContentWrapper {
    padding-left: 100px;
  }
}
.shopify-section--bordered + .shopify-section--footer {
  border-top: 1px solid var(--footer-border-color);
}

.Footer {
  padding: 34px 0;
  background: var(--footer-bg);
  color: var(--footer-text-color);
}

.Footer__Title,
.Footer .Link--primary:hover {
  color: var(--footer-heading-color);
}

.Footer .Form__Input::-moz-placeholder {
  color: var(--footer-text-color);
}

.Footer .Form__Input:-ms-input-placeholder {
  color: var(--footer-text-color);
}

.Footer .Link--secondary:hover,
.Footer .Form__Input::placeholder {
  color: var(--footer-text-color);
}

.Footer__Block {
  margin-top: 48px;
}

.Footer__Block:first-child {
  margin-top: 0;
}

.Footer__Title {
  margin-bottom: 20px;
}

.Footer__Social {
  margin-top: 5px;
}

.Footer__Content + .Footer__Newsletter {
  margin-top: 18px;
}

.Footer__Aside {
  text-align: center;
}

.Footer__Inner + .Footer__Aside {
  margin-top: 65px;
}

.Footer__LocalizationForm {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.Footer__LocalizationItem + .Footer__LocalizationItem {
  margin-left: 30px;
}

.Footer__ThemeAuthor {
  margin-top: 4px;
}

.Footer__PaymentList {
  margin: 54px -24px 0 -24px;
  padding-top: 28px;
  border-top: 1px solid var(--footer-border-color);
}

.Footer__PaymentList svg {
  opacity: 0.8;
  width: 38px;
  height: 24px;
}

.Footer__StoreName {
  color: var(--footer-text-color);
}

@media screen and (min-width: 641px) {
  .Footer {
    padding: 75px 0 42px 0;
  }

  .Footer__Inner {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-left: -40px;
    margin-right: -40px;
  }

  .Footer__Block {
    flex: 1 1 50%;
    margin-top: 0;
    margin-bottom: 50px;
    padding-left: 25px;
    padding-right: 25px;
  }

  .Footer__Social {
    margin-top: 14px;
  }

  .Footer__Aside {
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  .Footer__Inner + .Footer__Aside {
    margin-top: 40px;
  }

  .Footer__Localization {
    width: 100%;
  }

  .Footer__LocalizationForm {
    justify-content: flex-start;
  }

  .Footer__StoreName {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  }

  .Footer__PaymentList {
    padding: 0;
    margin: 0 -8px;
    border: none;
  }
}

@media screen and (min-width: 1140px) {
  .Footer__Inner {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .Footer--center .Footer__Inner {
    justify-content: center;
  }

  .Footer__Block {
    flex: 0 1 auto;
  }

  .Footer__Block--text,
  .Footer__Block--newsletter {
    flex-basis: 240px;
  }

  .Footer__Inner + .Footer__Aside {
    margin-top: 80px;
  }
}

@media screen and (min-width: 1140px) {
  .Footer__Block--text {
    flex-basis: 400px;
  }

  .Footer__Block--newsletter {
    flex-basis: 305px;
  }
}
.GiftCard {
  text-align: center;
}

.GiftCard__Wrapper {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.GiftCard__Redeem {
  margin-bottom: 30px;
}

.GiftCard__IllustrationWrapper {
  position: relative;
  margin: 40px 0;
}

.GiftCard__CodeHolder {
  position: absolute;
  display: inline-block;
  margin: 0 auto;
  bottom: 20px;
  left: 50%;
  white-space: nowrap;
  padding: 12px 20px;
  background: #ffffff;
  border-radius: 3px;
  transform: translateX(-50%);
}

.GiftCard__QrCode {
  margin: 20px 0;
}

.GiftCard__QrCode img {
  margin: 0 auto;
}
/**
 * ----------------------------------------------------------------------------
 * Main header
 * ----------------------------------------------------------------------------
 */

.shopify-section--header {
  position: relative;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 5;
}

.Header .Heading,
.Header .Link--secondary,
.Header .Link--primary:hover {
  color: var(--header-heading-color);
}

.Header .Text--subdued,
.Header .Link--primary,
.Header .Link--secondary:hover {
  color: var(--header-light-text-color);
}

.Header {
  background: var(--header-background);
  color: var(--header-heading-color);
  box-shadow: 0 -1px var(--header-border-color) inset;
  transition: background 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.Header__Wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 18px;
}

.Header__Logo {
  position: relative;
  margin-bottom: 0;
}

.Header__LogoLink,
.Header__LogoImage {
  display: block;
}

.Header__LogoLink {
  text-align: center;
}

.Header__LogoImage {
  margin: 0 auto;
  transition: opacity 0.3s ease-in-out;
}

.Header__LogoImage--transparent {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}

.Header__Icon {
  display: inline-block;
  line-height: 1;
  transition: color 0.2s ease-in-out;
}

.Header__Icon span {
  display: block;
}

.Header__Icon + .Header__Icon {
  margin-left: 18px;
}

.supports-sticky .Search[aria-hidden=true] + .Header--transparent {
  background: transparent;
}

.supports-sticky .Search[aria-hidden=true] + .Header--transparent .Header__LogoImage--primary:not(:only-child) {
  opacity: 0;
}

.supports-sticky .Search[aria-hidden=true] + .Header--transparent .Header__LogoImage--transparent {
  opacity: 1;
}

.supports-sticky .Search[aria-hidden=true] + .Header--transparent .Header__Icon svg {
  filter: drop-shadow(0 1px rgba(0, 0, 0, 0.25));
}

.supports-sticky .Search[aria-hidden=true] + .Header--transparent .Header__Icon,
.supports-sticky .Search[aria-hidden=true] + .Header--transparent .HorizontalList__Item > .SelectButton,
.supports-sticky .Search[aria-hidden=true] + .Header--transparent .HorizontalList__Item > .Heading,
.supports-sticky .Search[aria-hidden=true] + .Header--transparent .Header__LogoLink > .Heading,
.supports-sticky .Search[aria-hidden=true] + .Header--transparent .Text--subdued {
  color: currentColor;
}

.Header__Icon .Icon--nav {
  height: 15px;
  width: 20px;
}

.Header__Icon .Icon--cart {
  width: 17px;
  height: 20px;
}

.Header__Icon .Icon--search {
  position: relative;
  top: 1px; /* for pixel perfect */
  width: 18px;
  height: 17px;
}

.Header__FlexItem {
  display: flex;
}

.Header__FlexItem--fill {
  flex: 1 0 0;
  align-items: center;
}

.Header__FlexItem--fill:last-child {
  justify-content: flex-end;
}

.Header__CartDot {
  position: absolute;
  top: 2px;
  right: -6px;
  width: 8px;
  height: 8px;
  border-radius: 100%;
  background-color: var(--header-heading-color);
  box-shadow: 0 0 0 2px var(--header-background);
  transform: scale(0);
  transition: all 0.3s ease-in-out;
}

.Header__CartDot.is-visible {
  transform: scale(1);
}

.Search[aria-hidden=true] + .Header--transparent .Header__CartDot {
  box-shadow: none;
  background-color: currentColor;
}

@media screen and (min-width: 641px) {
  .Header__Wrapper {
    padding: 18px 30px;
  }

  .Header__LogoImage {
    max-width: 100%;
  }

  .Header__Icon + .Header__Icon {
    margin-left: 25px;
  }

  .Header__Icon .Icon--nav-desktop {
    height: 17px;
    width: 24px;
  }

  .Header__Icon .Icon--cart-desktop {
    height: 23px;
    width: 19px;
  }

  .Header__Icon .Icon--search-desktop {
    position: relative;
    top: 2px; /* for pixel perfect alignment with the cart icon */
    width: 21px;
    height: 21px;
  }

  .Header__Icon .Icon--account {
    position: relative;
    top: 2px; /* for pixel perfect alignment with the cart icon */
    width: 20px;
    height: 20px;
  }

  .Header--withIcons .Header__SecondaryNav {
    position: relative;
    top: 1px; /* for pixel perfect alignment with icons */
    margin-right: 32px;
  }
}

@media screen and (min-width: 1140px) {
  .js .Header__Wrapper {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .Header:not(.Header--sidebar) .Header__Wrapper {
    padding: 18px 50px;
  }

  .Header__Localization {
    display: inline-block;
  }

  .Header__MainNav {
    margin-right: 45px;
  }

  .Header--inline .Header__FlexItem:first-child .Header__LocalizationForm,
  .Header--logoLeft .Header__FlexItem:first-child .Header__LocalizationForm {
    display: none;
  }

  .Header--logoLeft .Header__FlexItem--logo {
    order: -1;
    margin-right: 38px;
  }
  .Header--logoLeft .Header__FlexItem--fill:last-child {
    flex: none;
  }

  .Header--center .Header__Wrapper.Header__Wrapper {
    /* Ugly hack to increase CSS specificity */
    padding-bottom: 24px;
  }

  .Header--center .Header__MainNav {
    position: absolute;
    bottom: 0;
    left: 0;
    padding-bottom: 18px;
    width: 100%;
    text-align: center;
  }

  .Header--center .Header__MainNav .HorizontalList {
    margin-left: 0;
    margin-right: 0;
  }

  .Header--center .Header__FlexItem {
    margin-bottom: 40px;
  }

  .Header--center .Header__FlexItem--increaseSpace {
    margin-bottom: 80px;
  }

  .Header--center .Header__SecondaryNav .Header__LocalizationForm {
    display: none;
  }

  .Header--initialized .Header__Wrapper {
    opacity: 1;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Desktop nav
 * ----------------------------------------------------------------------------
 */

.Header__LinkSpacer {
  position: absolute;
  display: block;
  bottom: 0;
  color: transparent;
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.Header__LinkSpacer::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  transform: scale(0, 1);
  transform-origin: left center;
  border-bottom: 2px solid var(--header-heading-color);
  transition: transform 0.3s, opacity 0.3s;
}

.Header:not(.Header--transparent) .HorizontalList__Item.is-expanded .Header__LinkSpacer::after,
.Header:not(.Header--transparent) .HorizontalList__Item.is-active .Header__LinkSpacer::after {
  opacity: 1;
  transform: scale(1, 1);
}

/**
 * ----------------------------------------------------------------------------
 * Dropdown menu
 * ----------------------------------------------------------------------------
 */

.DropdownMenu {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  top: 100%;
  padding: 25px 0;
  min-width: 200px;
  max-width: 270px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  text-align: left;
  background: var(--header-background);
  border: 1px solid var(--header-border-color);
  border-top: none;
  transition: all 0.3s ease-in-out;
}

.DropdownMenu::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 0;
  background: var(--header-heading-color);
  width: 100%;
  height: 2px;
  transform: scale(0, 1);
  transform-origin: left center;
  transition: transform 0.3s;
}

.DropdownMenu[aria-hidden=false] {
  opacity: 1;
  visibility: visible;
}

.DropdownMenu[aria-hidden=false]::before {
  transform: scale(1, 1);
}

.DropdownMenu[aria-hidden=false] .DropdownMenu {
  display: block;
}

.DropdownMenu [aria-haspopup] {
  position: relative;
}

.DropdownMenu .Link {
  padding-left: 25px;
  padding-right: 40px;
}

.DropdownMenu svg {
  position: absolute;
  width: 6px;
  top: calc(50% - 4px);
  height: 10px;
  right: 20px;
  transition: transform 0.2s ease-in-out;
}

.DropdownMenu .Linklist__Item:hover svg {
  transform: translateX(4px);
}

.DropdownMenu .Linklist__Item:hover > .Link--secondary {
  color: var(--header-light-text-color);
}

.DropdownMenu .DropdownMenu {
  display: none;
  left: 100%;
  top: -26px;
  border-top: 1px solid var(--header-border-color);
}

.DropdownMenu .DropdownMenu::before {
  display: none;
}

.DropdownMenu .DropdownMenu--reversed {
  left: auto;
  right: 100%;
}

/**
 * ----------------------------------------------------------------------------
 * Mega menu
 * ----------------------------------------------------------------------------
 */

.MegaMenu {
  position: absolute;
  padding: 20px 0;
  width: 100%;
  left: 0;
  top: 100%;
  visibility: hidden;
  opacity: 0;
  max-height: 600px;
  overflow: auto;
  -ms-scroll-chaining: none;
      overscroll-behavior: contain;
  text-align: left;
  background: var(--header-background);
  border-bottom: 1px solid var(--header-border-color);
  transition: all 0.3s ease-in-out;
}

@supports (--css: variables) {
  .MegaMenu {
    max-height: calc(100vh - var(--header-height));
  }
}

.MegaMenu[aria-hidden=false] {
  opacity: 1;
  visibility: visible;
}

.MegaMenu__Inner {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: nowrap;
  max-width: 1450px;
  margin: 0 auto;
  padding: 0 10px; /* each menu has 40px margin, but header has 50px, so we normalize it here */
}

.MegaMenu--spacingEvenly .MegaMenu__Inner {
  justify-content: space-around;
  justify-content: space-evenly; /* space-evenly is not supported everywhere */
}

.MegaMenu--spacingCenter .MegaMenu__Inner {
  justify-content: center;
}

@supports (display: grid) {
  .MegaMenu--grid .MegaMenu__Inner {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

.MegaMenu__Item {
  margin: 20px 40px;
  flex-shrink: 1;
}

.MegaMenu__Item--fit {
  flex-shrink: 0;
}

.MegaMenu__Title {
  display: block;
  margin-bottom: 20px;
}

.MegaMenu__Push {
  display: inline-block;
  text-align: center;
  vertical-align: top;
  width: 100%;
}

.MegaMenu__Push--shrink {
  width: 50%;
}

.MegaMenu__Push--shrink:first-child {
  padding-right: 15px;
}

.MegaMenu__Push--shrink:last-child {
  padding-left: 15px;
}

.MegaMenu__PushImageWrapper {
  margin: 8px auto 20px auto;
  max-width: 100%;
  overflow: hidden;
}

.MegaMenu__PushHeading {
  margin-bottom: 6px;
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .MegaMenu__Push img {
    transform: scale(1);
    transition: opacity 0.3s ease, transform 8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .MegaMenu__Push:hover img {
    transform: scale(1.2);
  }
}
/**
 * ----------------------------------------------------------------------------
 * Image with text overlay section
 * ----------------------------------------------------------------------------
 */

.ImageHero {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-size: cover;
  background-position: center;
  min-height: 380px;
  width: 100%;
  overflow: hidden;
}

.ImageHero--small {
  min-height: 330px;
}

.ImageHero--large {
  min-height: 480px;
}

.ImageHero__ImageWrapper,
.ImageHero__Image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

@media screen and (min-width: 1008px) and (-moz-touch-enabled: 0), screen and (min-width: 1008px) and (hover: hover) {
  .ImageHero__Image--parallax {
    background-attachment: fixed;
  }
}

.ImageHero__ImageWrapper--hasOverlay::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}

.ImageHero__Wrapper {
  z-index: 1;
}

.ImageHero__ContentOverlay {
  position: relative;
  flex-basis: 425px;
  flex-grow: 0;
  padding: 0 15px;
}

.ImageHero__TextContent {
  position: absolute;
  padding: 0 24px;
  margin-bottom: 0 !important;
  top: 50%;
  left: 50%;
  width: 100%;
  transform: translate(-50%, -50%);
  text-align: center;
}

/* We allow embedding a video within an image hero */
.ImageHero iframe {
  position: absolute;
  height: 100%;
  width: 200%;
  left: -50%;
  pointer-events: none;
}

.ImageHero--large iframe {
  width: 250%;
  left: -75%;
}

.ImageHero--preserveRatio {
  min-height: 0;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
  max-width: 100%;
}

.ImageHero--preserveRatio iframe {
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.ImageHero iframe[src] {
  pointer-events: auto; /* When data saver is on in Chrome, it will just set the src but prevents autoplay, so we need to make sure that we make it clickable */
}

.ImageHero__Block {
  margin: 40px auto;
  padding: 38px 20px;
  max-width: 250px;
  box-shadow: 0 1px 20px rgba(54, 54, 54, 0.3);
}

.ImageHero__Block--small {
  max-width: 165px;
}

.ImageHero__Block--large {
  max-width: 320px;
}

.ImageHero__BlockHeading {
  margin: -0.325em 0 18px 0;
}

.ImageHero__BlockContent + .ImageHero__BlockLink {
  margin-top: 18px;
}

@media screen and (max-width: 640px) {
  .ImageHero__Block {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  }
}

@media screen and (min-width: 641px) {
  .ImageHero {
    min-height: 450px;
  }

  .ImageHero--small {
    min-height: 400px;
  }

  .ImageHero--large {
    min-height: 500px;
  }

  .ImageHero iframe {
    width: 100%;
    height: 200%;
    left: 0;
  }

  .ImageHero__Block {
    padding: 48px 15px;
    max-width: 380px;
  }

  .ImageHero__Block--small {
    max-width: 240px;
  }

  .ImageHero__Block--large {
    max-width: 520px;
  }

  .ImageHero__TextContent {
    padding: 0 40px;
    bottom: 40px;
    top: auto;
    left: 0;
    transform: none;
    text-align: left;
  }
}

@media screen and (min-width: 1140px) {
  .ImageHero {
    min-height: 500px;
  }

  .ImageHero--small {
    min-height: 450px;
  }

  .ImageHero--large {
    min-height: 600px;
  }

  .ImageHero__TextContent {
    padding: 0 50px;
    bottom: 50px;
  }
}
.NewsletterPopup {
  position: fixed;
  bottom: 15px;
  left: 15px;
  width: calc(100% - 30px);
  padding: 24px 30px 30px 30px;
  background: var(--newsletter-popup-background);
  color: var(--newsletter-popup-text-color);
  z-index: 50;
  text-align: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  visibility: hidden;
  transform: translateY(25px);
  opacity: 0;
  transition: all 0.5s var(--drawer-transition-timing);
}

.NewsletterPopup .Heading {
  color: inherit;
}

.NewsletterPopup .Form__Input::-moz-placeholder {
  color: rgba(var(--newsletter-popup-text-color-rgb), 0.6);
}

.NewsletterPopup .Form__Input:-ms-input-placeholder {
  color: rgba(var(--newsletter-popup-text-color-rgb), 0.6);
}

.NewsletterPopup .Form__Input::placeholder {
  color: rgba(var(--newsletter-popup-text-color-rgb), 0.6);
}

.NewsletterPopup .Form__Input {
  border-color: rgba(var(--newsletter-popup-text-color-rgb), 0.4);
}

.NewsletterPopup .Form__Input:focus {
  border-color: var(--newsletter-popup-text-color);
}

.NewsletterPopup .Button {
  color: var(--newsletter-popup-background);
  border-color: var(--newsletter-popup-text-color);
}

.NewsletterPopup .Button::before {
  background-color: var(--newsletter-popup-text-color);
}

@media screen and (max-width: 640px) {
  @supports (padding: max(0px)) {
    .NewsletterPopup {
      bottom: max(15px, env(safe-area-inset-bottom, 0px) + 15px);
    }
  }
}

@media (-moz-touch-enabled: 0), (hover: hover) {
  .NewsletterPopup .Button:not([disabled]):hover {
    color: var(--newsletter-popup-text-color);
    background-color: transparent;
  }
}

.NewsletterPopup[aria-hidden=false] {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.NewsletterPopup__Close {
  position: absolute;
  right: 15px;
  top: 15px;
}

.NewsletterPopup__Close svg {
  display: block;
  width: 15px;
  height: 15px;
}

.NewsletterPopup__Content a {
  text-decoration: underline;
  text-underline-position: under;
}

.NewsletterPopup__Form {
  margin-top: 32px;
}

@media screen and (min-width: 641px) {
  .NewsletterPopup {
    max-width: 385px;
    right: 25px;
    bottom: 25px;
    left: auto;
    padding-top: 35px;
  }

  .NewsletterPopup__Close svg {
    width: 18px;
    height: 18px;
  }
}
.Password {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 24px;
  width: 100%;
  min-height: 100vh;
  background-size: cover;
  background-position: center;
}

/**
 * ----------------------------------------------------------------------------
 * Header
 * ----------------------------------------------------------------------------
 */
.Password__Header {
  position: relative;
  text-align: center;
}

.Password__Logo {
  display: block;
  line-height: 1;
}

.Password__LogoImage {
  vertical-align: middle;
}

.Password__LockAction {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.Password__LockAction svg {
  margin-left: 10px;
  width: 20px;
  height: 20px;
  vertical-align: bottom;
}

/**
 * ----------------------------------------------------------------------------
 * Content
 * ----------------------------------------------------------------------------
 */
.Password__Content {
  width: 470px;
  max-width: 100%;
  margin: 35px auto;
}

.Password__Newsletter {
  margin-bottom: 10px;
}

.Password__Card {
  padding: 24px;
  background: var(--light-background);
  text-align: center;
}

.Password__Message {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
}

.Password__Form {
  margin-top: 28px;
}

.Password__Form .Button {
  flex: none;
  margin-bottom: 0;
  margin-top: 15px;
  width: 100%;
}

.Password__Social {
  padding: 22px 25px 22px 28px;
  background: var(--background);
  text-align: center;
}

.Password__ShareButtons {
  margin-top: 15px;
}

@media screen and (min-width: 641px) {
  .Password__Content {
    margin: 80px auto;
  }

  .Password__Card {
    padding: 45px 60px 50px 60px;
  }

  .Password__Form .Button {
    width: auto;
    margin-top: 0;
  }

  .Password__Social {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }

  .Password__ShareButtons {
    margin-top: 0;
    margin-left: 15px;
    flex-shrink: 0;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Footer
 * ----------------------------------------------------------------------------
 */
.Password__Footer {
  text-align: center;
}

.Password__AdminLink {
  display: block;
  margin-top: 15px;
}

.Password__Footer svg {
  width: 70px;
  height: 20px;
  vertical-align: bottom;
  margin-left: 2px;
}

@media screen and (min-width: 641px) {
  .Password__Footer {
    display: flex;
    justify-content: space-between;
    text-align: left;
  }

  .Password__AdminLink {
    margin-top: 0;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Modal
 * ----------------------------------------------------------------------------
 */
.Password__Modal {
  display: flex;
  justify-content: center;
}
/**
 * ----------------------------------------------------------------------------
 * Product (general)
 * ----------------------------------------------------------------------------
 */
.Product {
  position: relative;
  margin: 0 auto 50px auto;
  max-width: 1330px;
}

.Product--fill {
  max-width: none;
}

@media screen and (min-width: 641px) {
  .Product {
    margin-bottom: 80px;
  }
}
@media screen and (min-width: 1008px) {
  .Product__Wrapper {
    max-width: calc(100% - 525px);
    margin-left: 0;
  }
}
@media screen and (min-width: 1140px) {
  .Product__Wrapper {
    max-width: calc(100% - 550px);
  }

  .Product--small .Product__Slideshow {
    max-width: 400px;
  }

  .Product--medium .Product__Slideshow {
    max-width: 550px;
  }
}
@media screen and (min-width: 1500px) {
  .Product__Wrapper {
    max-width: calc(100% - 500px);
  }
}
/**
 * ----------------------------------------------------------------------------
 * Product gallery
 * ----------------------------------------------------------------------------
 */
.Product__Gallery {
  position: relative;
  margin-bottom: 28px;
}

.Product__ViewInSpace {
  background: rgba(var(--text-color-rgb), 0.08);
  /* This color is defined by Shopify spec */
}
.Product__ViewInSpace[data-shopify-xr-hidden] {
  visibility: hidden;
}
@media screen and (min-width: 1008px) {
  .Product__ViewInSpace[data-shopify-xr] {
    display: none;
  }
}
.Product__ViewInSpace svg {
  margin: -1px 12px 0 0;
  width: 16px;
  height: 16px;
  vertical-align: middle;
}

.Product__Slideshow::after {
  content: "flickity";
  display: none;
}

.Product__SlideItem {
  position: relative;
}

.Product__SlideItem--hidden {
  visibility: hidden;
}

.Product__Slideshow.flickity-enabled .Product__SlideItem:not(.is-selected) .plyr__control {
  /* This is a quick fix to prevent the controls to gain focus until if the slide is not visible */
  visibility: hidden;
}

.Product__Video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000000;
}

.Product__SlideshowNav {
  display: none;
}

.Product__SlideshowNavBadge {
  position: absolute;
  top: 3px;
  right: 3px;
  z-index: 1;
  pointer-events: none;
}
.Product__SlideshowNavBadge svg {
  display: block;
  width: 20px;
  height: 20px;
}

.Product__SlideshowMobileNav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 24px 0 24px;
}
.Product__SlideshowMobileNav .flickity-page-dots {
  display: inline-block;
  margin-top: 0;
  width: auto;
}
.Product__SlideshowMobileNav .dot {
  vertical-align: middle;
}

.Product__SlideshowNavArrow {
  position: relative;
  /* This is used to increase the clickable area */
}
.Product__SlideshowNavArrow::before {
  position: absolute;
  content: "";
  top: -18px;
  right: -18px;
  left: -18px;
  bottom: -18px;
  transform: translateZ(0);
  /* Needed to avoid a glitch on iOS */
}
.Product__SlideshowNavArrow svg {
  display: block;
  width: 6px;
  height: 9px;
}

.Product__SlideshowNavArrow--previous {
  margin-right: 24px;
}

.Product__SlideshowNavArrow--next {
  margin-left: 24px;
}

@media screen and (min-width: 641px) {
  .Product__Gallery {
    margin-bottom: 65px;
  }
}
@media screen and (min-width: 1008px) {
  .Product__Gallery {
    margin-top: 50px;
    margin-left: 50px;
  }

  .Product__SlideshowNav--dots {
    display: block;
  }

  .Product__SlideItem {
    display: block !important;
  }

  .Product__Slideshow--zoomable .Product__SlideItem--image {
    cursor: var(--cursor-zoom-in-svg) 18 18, zoom-in;
    cursor: -webkit-image-set(var(--cursor-zoom-in-svg) 1x, var(--cursor-zoom-in-2x-svg) 2x), zoom-in;
  }

  /* Dots */
  .Product__SlideshowNav--dots .Product__SlideshowNavScroller {
    position: sticky;
    top: 50%;
    transform: translateY(-50%);
  }
  @supports (--css: variables) {
    .Product__SlideshowNav--dots .Product__SlideshowNavScroller {
      top: calc(50% + (var(--header-height) / 2));
    }
  }

  .Product__SlideshowNavDot {
    display: block;
    height: 10px;
    width: 10px;
    border-radius: 100%;
    border: 2px solid var(--border-color);
    background: transparent;
    transition: all 0.2s ease-in-out;
  }
  .Product__SlideshowNavDot:not(:last-child) {
    margin-bottom: 12px;
  }

  .Product__SlideshowNavDot.is-selected {
    border-color: var(--heading-color);
    background: var(--heading-color);
  }

  /* Thumbnails */
  .Product__SlideshowNavImage {
    position: relative;
    display: block;
    border: 1px solid transparent;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
  }
  .Product__SlideshowNavImage:not(:last-child) {
    margin-bottom: 18px;
  }

  .Product__SlideshowNavImage.is-selected {
    border-color: var(--heading-color);
  }

  .Product__SlideshowNavPlay {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #ffffff;
    filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.2));
  }
  .Product__SlideshowNavPlay svg {
    width: 30px;
    height: 30px;
    transition: transform 0.2s ease-in-out;
  }

  /* When the gallery is stack we have various adjusments to do */
  .Product__Gallery--stack {
    /* On desktop we use a different apparition effect than fade in if images are stacked */
    /* Thumbnails */
  }
  .Product__Gallery--stack .Product__SlideshowNav {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 1;
  }
  .Product__Gallery--stack .Product__SlideshowNav--dots {
    left: -30px;
  }
  .Product__Gallery--stack .Product__Slideshow::after {
    content: "";
    /* Disable Flickity */
  }
  .Product__Gallery--stack .Product__Slideshow .Image--fadeIn {
    transform: translateY(50px);
    opacity: 0;
    transition: transform 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .Product__Gallery--stack .Product__Slideshow .Image--lazyLoaded.Image--fadeIn {
    transform: translateY(0);
    opacity: 1;
  }
  .Product__Gallery--stack .Product__SlideItem {
    margin-bottom: 30px;
    visibility: visible !important;
    opacity: 1 !important;
  }
  .Product__Gallery--stack .Product__SlideItem:last-child {
    margin-bottom: 0;
  }
  .Product__Gallery--stack .Product__SlideshowNav--thumbnails {
    position: sticky;
    padding: 0 0 50px 0;
    top: var(--header-base-height);
    margin-bottom: -50px;
    width: 70px;
  }
  @supports (--css: variables) {
    .Product__Gallery--stack .Product__SlideshowNav--thumbnails {
      top: calc(var(--header-height) + 25px);
    }
  }
}
@media screen and (min-width: 1140px) {
  .Product__Gallery {
    margin-bottom: 80px;
  }

  .Product__Slideshow {
    margin-left: auto;
    margin-right: auto;
  }

  .Product__Gallery--withThumbnails .Product__SlideshowNav--dots {
    display: none;
  }
  .Product__Gallery--withThumbnails .Product__SlideshowNav--thumbnails {
    display: block;
  }

  .Product__Gallery--stack.Product__Gallery--withThumbnails {
    display: flex;
  }
  .Product__Gallery--stack.Product__Gallery--withThumbnails .Product__Slideshow,
  .Product__Gallery--stack.Product__Gallery--withThumbnails .Product__SlideshowNav {
    flex: 1 0 auto;
  }
  .Product__Gallery--stack.Product__Gallery--withThumbnails .Product__SlideshowNav {
    flex-grow: 0;
  }
  .Product__Gallery--stack.Product__Gallery--withThumbnails .Product__Slideshow {
    padding-left: 50px;
  }

  .Product__Gallery:not(.Product__Gallery--stack).Product__Gallery--withThumbnails .Product__SlideshowNav {
    margin: 10px -10px -10px -10px;
    text-align: center;
  }
  .Product__Gallery:not(.Product__Gallery--stack).Product__Gallery--withThumbnails .Product__SlideshowNavImage {
    display: inline-block;
    width: 70px;
    margin: 10px;
    vertical-align: top;
  }
  .Product__Gallery:not(.Product__Gallery--stack).Product__Gallery--withThumbnails .flickity-page-dots {
    display: none;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Product aside and info
 * ----------------------------------------------------------------------------
 */
.Product__Info,
.Product__Aside {
  max-width: 500px;
  margin: 0 auto;
}

.Product__Info--noGallery {
  padding-top: 24px;
}

@media screen and (min-width: 641px) {
  .Product__Info .Container {
    padding-left: 0;
    padding-right: 0;
  }

  .Product__Aside .Section {
    max-width: 630px;
    margin: 0 auto;
  }
}
@media screen and (min-width: 1008px) {
  .Product__InfoWrapper {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
  }

  .Product__Info {
    position: sticky;
    top: var(--header-base-height);
    right: 0;
    width: 375px;
    margin: 0 100px -40px 50px;
    padding-top: 0;
    padding-bottom: 40px;
  }
  @supports (--css: variables) {
    .Product__Info {
      top: calc(var(--header-height) + 25px);
    }
  }

  .Product__Aside {
    max-width: 820px;
    padding-left: 50px;
  }

  .Product__Aside .SectionHeader {
    margin-bottom: 30px;
  }
}
@media screen and (min-width: 1140px) {
  .Product__Info {
    width: 400px;
  }
}
@media screen and (min-width: 1500px) {
  .Product__Info {
    margin-right: 50px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Action list
 * ----------------------------------------------------------------------------
 */
@-webkit-keyframes shareItemAnimation {
  0% {
    transform: translateY(0%);
  }
  25% {
    opacity: 0;
    transform: translateY(100%);
  }
  50% {
    opacity: 0;
    transform: translateY(-100%);
  }
  75% {
    opacity: 1;
    transform: translateY(0%);
  }
}
@keyframes shareItemAnimation {
  0% {
    transform: translateY(0%);
  }
  25% {
    opacity: 0;
    transform: translateY(100%);
  }
  50% {
    opacity: 0;
    transform: translateY(-100%);
  }
  75% {
    opacity: 1;
    transform: translateY(0%);
  }
}
.Product__ActionList {
  position: absolute;
  top: 30px;
  right: 25px;
  z-index: 1;
  visibility: visible;
  opacity: 1;
  transition: visibility 0.2s ease-in-out, opacity 0.2s ease-in-out;
}

.Product__ActionList.is-hidden {
  opacity: 0;
  visibility: hidden;
}

.Product__ActionItem {
  display: block;
  position: relative;
}

.Product__ActionItem + .Product__ActionItem {
  margin-top: 15px;
}

.Product__ShareList {
  display: block;
  position: absolute;
  visibility: hidden;
  top: 100%;
  right: 0;
  color: var(--text-color);
}

.Product__ActionItem .Icon--share {
  margin-left: -1px;
}

.Product__ShareItem {
  display: block;
  margin: 15px 0 15px auto;
  padding: 7px 15px 7px 17px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  background: var(--light-background);
  border-radius: 25px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
  white-space: nowrap;
  opacity: 0;
  box-shadow: 0 2px 10px rgba(54, 54, 54, 0.15);
  transition: all 0.45s cubic-bezier(0.645, 0.045, 0.355, 1) 0.2s;
}
.Product__ShareItem:active, .Product__ShareItem:focus {
  color: var(--light-background);
  background: var(--text-color);
  outline: none;
}
.Product__ShareItem:nth-child(2) {
  transition-delay: 0.1s;
}
.Product__ShareItem:nth-child(3) {
  transition-delay: 0s;
}
.Product__ShareItem svg {
  margin-right: 12px;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 14px));
  vertical-align: sub;
  transform: translateZ(0);
}
.Product__ShareItem:hover svg {
  -webkit-animation: shareItemAnimation 0.5s ease-in-out forwards;
          animation: shareItemAnimation 0.5s ease-in-out forwards;
}

.Product__ShareList[aria-hidden=false] {
  visibility: visible;
}
.Product__ShareList[aria-hidden=false] .Product__ShareItem {
  opacity: 1;
  transition-delay: 0s;
}
.Product__ShareList[aria-hidden=false] .Product__ShareItem:nth-child(2) {
  transition-delay: 0.1s;
}
.Product__ShareList[aria-hidden=false] .Product__ShareItem:nth-child(3) {
  transition-delay: 0.2s;
}

@media screen and (min-width: 1008px) {
  .Product__ShareList {
    top: auto;
    bottom: 100%;
  }

  .Product__ShareItem {
    transition-delay: 0s;
  }
  .Product__ShareItem:nth-child(3) {
    transition-delay: 0.2s;
  }

  .Product__ShareList[aria-hidden=false] .Product__ShareItem {
    transition-delay: 0.2s;
  }
  .Product__ShareList[aria-hidden=false] .Product__ShareItem:nth-child(3) {
    transition-delay: 0s;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Product meta and info
 * ----------------------------------------------------------------------------
 */
.ProductMeta {
  text-align: center;
  padding-bottom: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.ProductMeta__ImageWrapper {
  display: block;
  margin-bottom: 32px;
}

.ProductMeta__Vendor {
  margin-bottom: 20px;
}

.ProductMeta__Title {
  margin-bottom: 0;
}

.ProductMeta__PriceList {
  margin-top: 15px;
}

.ProductMeta__Price.Price--compareAt {
  margin-left: 30px;
}

.ProductMeta__UnitPriceMeasurement {
  margin-top: 5px;
}

shopify-payment-terms {
  display: block;
  margin-top: 12px;
  text-align: left;
}

.ProductMeta__TaxNotice {
  margin-top: 5px;
}

.ProductMeta__Description,
.ProductMeta__Text {
  margin: 24px 0;
  text-align: left;
}

.ProductMeta__Rating {
  margin-top: 4px;
}

.ProductMeta__ShareButtons {
  display: flex;
  align-items: center;
  margin: 24px 0;
}

.ProductMeta__ShareTitle {
  margin-right: 6px;
}

.ProductMeta__ShareList {
  font-size: 0;
}

.ProductMeta__ShareItem {
  display: inline-block;
  margin: 0 7px;
}
.ProductMeta__ShareItem svg {
  display: block;
  width: 12px;
  height: 12px;
  opacity: 0.6;
  transition: opacity 0.2s ease-in-out;
}
.ProductMeta__ShareItem:hover svg {
  opacity: 1;
}

/* When the description is set after the product form, layout changes a bit */
.ProductForm ~ .ProductMeta__Description {
  border-top: none;
  padding-top: 0;
}
.ProductForm ~ .ProductMeta__Description .ProductMeta__ShareButtons {
  margin-bottom: 2px;
}

.Product__QuickNav {
  position: relative;
  margin-top: 40px;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}
.Product__QuickNav a {
  display: block;
  padding: 11px 16px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transform: rotateX(0deg);
}
.Product__QuickNav a:last-child {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  transform: rotateX(180deg);
}
.Product__QuickNav svg {
  position: absolute;
  right: 18px;
  top: calc(50% - 5px);
  height: 10px;
  width: 8px;
}

.Product__QuickNavWrapper {
  transition: 0.6s;
  transform-style: preserve-3d;
}

.Product__QuickNav.is-flipped .Product__QuickNavWrapper {
  transform: rotateX(180deg);
}

@media screen and (min-width: 1008px) {
  .ProductMeta {
    text-align: left;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Product form
 * ----------------------------------------------------------------------------
 */

.ProductForm__Variants {
  margin-top: 24px;
  margin-bottom: 24px;
}

.ProductForm__Option {
  position: relative;
  margin-bottom: 10px;
}

.ProductForm__Option--labelled {
  margin-bottom: 20px;
}

.no-js .ProductForm__Option:not(.no-js) {
  display: none;
}

.ProductForm__Item {
  position: relative;
  width: 100%;
  text-align: left;
  padding: 10px 28px 10px 14px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.ProductForm__Item .Icon--select-arrow {
  position: absolute;
  top: calc(50% - 5px);
  right: 15px;
  width: 10px;
  height: 10px;
}

.ProductForm__Label {
  display: block;
  margin-bottom: 8px;
}

.ProductForm__LabelLink {
  float: right;
  text-decoration: underline;
  text-underline-position: under;
}

.ProductForm__SelectedValue {
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 220px;
  vertical-align: top;
}

.ProductForm__OptionCount {
  float: right;
  margin-right: 14px;
}

.ProductForm__Inventory {
  margin-top: 24px;
  margin-bottom: 24px;
}

.ProductForm__QuantitySelector {
  margin-top: 24px;
  margin-bottom: 24px;
}

.ProductForm__Error {
  margin-top: 10px;
  text-align: center;
}

.ProductForm__AddToCart {
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * ----------------------------------------------------------------------------
 * Featured image
 * ----------------------------------------------------------------------------
 */
.Product__FeatureImageWrapper {
  overflow: hidden;
  background-size: cover;
}

.Product__FeatureImage {
  height: 415px;
  background-size: cover;
  background-position: center;
}

@media screen and (min-width: 641px) {
  .Product__FeatureImage {
    height: 500px;
  }
}
@media screen and (min-width: 1140px) {
  .Product__FeatureImage {
    height: 600px;
  }

  .Product__FeatureImage--small {
    height: 500px;
  }

  .Product__FeatureImage--large {
    height: 700px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Product tabs
 * ----------------------------------------------------------------------------
 */
.Product__Tabs {
  margin: 50px 0;
}

@media screen and (max-width: 640px) {
  .Product__Tabs .Collapsible__Content {
    padding-top: 6px;
    padding-bottom: 28px;
  }
}
@media screen and (max-width: 1007px) {
  .Product__Tabs .Collapsible {
    padding: 0 24px;
  }
}
@media screen and (min-width: 1008px) {
  .Product__Tabs {
    margin: 80px 0;
  }

  .Product__Tabs .Collapsible__Content {
    padding-right: 80px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Variant slideshow (for color carousel)
 * ----------------------------------------------------------------------------
 */
@-webkit-keyframes variantSelectorInfoOpeningAnimation {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  50% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes variantSelectorInfoOpeningAnimation {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  50% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
@-webkit-keyframes variantSelectorInfoClosingAnimation {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-10px);
    opacity: 0;
  }
}
@keyframes variantSelectorInfoClosingAnimation {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-10px);
    opacity: 0;
  }
}
.VariantSelector {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  background: var(--background);
  padding: 24px 0;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(54, 54, 54, 0.2);
  transform: translateY(100%);
  visibility: hidden;
  transition: transform 0.4s cubic-bezier(0.645, 0.045, 0.355, 1), visibility 0.4s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.VariantSelector[aria-hidden=false] {
  transform: translateY(0);
  visibility: visible;
}

.VariantSelector__Item {
  padding: 0 10px;
  width: 60%;
}

.VariantSelector__Info {
  position: relative;
  margin: 48px 24px 0 24px;
}

.VariantSelector__ImageWrapper[aria-hidden="true"] {
  display: none;
}

.VariantSelector__ChoiceList {
  margin-bottom: 34px;
}

.VariantSelector__Choice {
  display: table;
  table-layout: fixed;
  width: 100%;
  -webkit-animation: 0.15s variantSelectorInfoClosingAnimation forwards ease-in-out;
          animation: 0.15s variantSelectorInfoClosingAnimation forwards ease-in-out;
}

.VariantSelector__Choice.is-selected {
  -webkit-animation: 0.3s variantSelectorInfoOpeningAnimation forwards ease-in-out;
          animation: 0.3s variantSelectorInfoOpeningAnimation forwards ease-in-out;
}

.VariantSelector__Choice:not(:first-child) {
  position: absolute;
  top: 0;
  left: 0;
}

.VariantSelector__ChoiceColor,
.VariantSelector__ChoicePrice {
  display: table-cell;
  width: 50%;
  text-align: center;
  vertical-align: middle;
}

.VariantSelector__ChoiceColor {
  border-right: 1px solid var(--border-color);
}

.VariantSelector__ColorSwatch {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 15px;
  vertical-align: sub;
  background-size: cover;
}

.VariantSelector__ColorSwatch--white {
  outline: 1px solid var(--border-color);
}

.VariantSelector__ChoicePrice {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
}

@media screen and (max-width: 640px) {
  .VariantSelector .flickity-prev-next-button {
    display: none;
  }
}

@media screen and (min-width: 641px) {
  /* Starting from tablet, the look and feel of this selector is completely different and look like a modal */
  .VariantSelector {
    top: 50%;
    bottom: auto;
    left: 50%;
    width: 80%;
    padding-bottom: 34px;
    max-height: 100%;
    max-width: 800px;
    opacity: 0;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease-in-out;
    overflow: auto;
  }

  .VariantSelector[aria-hidden=false] {
    transform: translate(-50%, -50%);
    opacity: 1;
  }

  .VariantSelector__Item {
    padding: 0 25px;
    width: 46%;
  }

  .VariantSelector .flickity-prev-next-button.next {
    top: calc(50% - 45px);
    right: 20px;
  }

  .VariantSelector .flickity-prev-next-button.previous {
    top: calc(50% - 45px);
    left: 20px;
  }

  .VariantSelector__Info {
    max-width: 350px;
    margin-left: auto;
    margin-right: auto;
  }

  .VariantSelector__ChoicePrice {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
  }
}

@media (min-width: 800px) and (max-height: 950px) {
  .VariantSelector__ImageWrapper {
    max-width: 290px !important;
  }
}

/**
 * ----------------------------------------------------------------------------
 * Featured product (on home page)
 * ----------------------------------------------------------------------------
 */
.FeaturedProduct__Gallery {
  display: block;
  margin-bottom: 20px;
}

.FeaturedProduct__ViewWrapper {
  margin-top: 34px;
  text-align: center;
}

@media screen and (min-width: 641px) {
  .FeaturedProduct {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    max-width: 880px;
    margin: 0 auto;
  }

  .FeaturedProduct--center {
    align-items: center;
  }

  .FeaturedProduct__Gallery,
  .FeaturedProduct__Info {
    flex: 1 1 50%;
  }

  .FeaturedProduct__Gallery {
    margin: 0;
  }

  .FeaturedProduct__Info {
    margin-left: 50px;
  }

  .FeaturedProduct__Info .ProductMeta,
  .FeaturedProduct__ViewWrapper {
    text-align: left;
  }
}
@media screen and (min-width: 1008px) {
  .FeaturedProduct__Info {
    margin-left: 80px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Shopify payment button
 * ----------------------------------------------------------------------------
 */
.shopify-payment-button {
  margin-top: 20px;
  text-align: center;
}

.shopify-payment-button__more-options {
  position: relative;
  display: inline-block;
  transition: color 0.2s ease-in-out, opacity 0.2s ease-in-out;
  width: auto;
  line-height: normal;
}
.shopify-payment-button__more-options[aria-hidden=true] {
  display: none;
}
.shopify-payment-button__more-options::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  bottom: -1px;
  background: currentColor;
  transform: scale(1, 1);
  transform-origin: left center;
  transition: transform 0.2s ease-in-out;
}
@media (-moz-touch-enabled: 0), (hover: hover) {
  .shopify-payment-button__more-options:hover::before {
    transform: scale(0, 1);
  }
}

.shopify-payment-button__more-options:hover:not([disabled]) {
  text-decoration: none;
}
@charset "UTF-8";
/**
 * ----------------------------------------------------------------------------
 * Product reviews (integration with Shopify Reviews free app)
 * ----------------------------------------------------------------------------
 */
#shopify-product-reviews {
  margin: 18px 0 28px 0 !important;
  overflow: visible !important;
}
#shopify-product-reviews .spr-header-title,
#shopify-product-reviews .spr-summary-starrating,
#shopify-product-reviews .spr-summary-caption,
#shopify-product-reviews .spr-review-reportreview,
#shopify-product-reviews .spr-pagination,
#shopify-product-reviews .spr-form-title {
  display: none;
}
#shopify-product-reviews .spr-container {
  padding: 0;
  border: none;
}
#shopify-product-reviews .spr-container,
#shopify-product-reviews .spr-content {
  display: flex;
  flex-direction: column;
}
#shopify-product-reviews .spr-header,
#shopify-product-reviews .spr-form {
  order: 2;
}
#shopify-product-reviews .spr-content,
#shopify-product-reviews .spr-reviews {
  order: 1;
}
#shopify-product-reviews .spr-form {
  margin: -4px 0 0 0;
  padding: 0;
}
#shopify-product-reviews .spr-icon {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 10px));
}
#shopify-product-reviews .spr-form-input .spr-icon {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 14px));
}
#shopify-product-reviews .spr-icon + .spr-icon {
  margin-left: 3px;
}
#shopify-product-reviews .spr-icon-star-empty {
  opacity: 0.25;
}
#shopify-product-reviews .spr-icon-star-empty::before {
  content: "";
}
#shopify-product-reviews .spr-starrating.spr-form-input-error a {
  color: inherit;
}
#shopify-product-reviews .spr-reviews {
  margin: -6px 0 35px 0;
}
#shopify-product-reviews .spr-review {
  position: relative;
  padding: 0 0 30px 0;
  border: none;
  margin: 0;
}
#shopify-product-reviews .spr-review + .spr-review {
  margin-top: 26px;
}
#shopify-product-reviews .spr-review-header {
  position: static;
  margin-bottom: 12px;
}
#shopify-product-reviews .spr-review-header-starratings {
  margin-bottom: 0;
}
#shopify-product-reviews .spr-review-header-title {
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  text-transform: uppercase;
  letter-spacing: 0.2em;
}
#shopify-product-reviews .spr-review-header-byline {
  position: absolute;
  bottom: 0;
  left: 0;
  margin-bottom: 0;
  font-style: normal;
  opacity: 1;
  color: var(--text-color-light);
}
#shopify-product-reviews .spr-review-header-byline strong {
  font-weight: normal;
}
#shopify-product-reviews .spr-review-content {
  margin-bottom: 0;
}
#shopify-product-reviews .spr-review-reply {
  margin: 18px 0 6px 0;
  padding: 0 0 0 14px;
  background: none;
  border-left: 3px solid var(--border-color);
  font-style: italic;
}
#shopify-product-reviews .spr-summary-actions {
  display: block;
}
#shopify-product-reviews .spr-review-reply-shop {
  float: none;
}
#shopify-product-reviews .spr-summary-actions-newreview,
#shopify-product-reviews .spr-button-primary:not(input) {
  width: 100%;
}
#shopify-product-reviews input.spr-button-primary {
  border-color: var(--button-background);
  background: var(--button-background);
  color: var(--button-text-color);
  width: 100%;
}
#shopify-product-reviews .spr-pagination-prev,
#shopify-product-reviews .spr-pagination-next {
  display: block;
  position: relative;
  margin-bottom: 20px;
}
#shopify-product-reviews .spr-pagination-prev > a,
#shopify-product-reviews .spr-pagination-next > a {
  width: 100%;
}
#shopify-product-reviews .new-review-form {
  margin-top: 20px;
}
#shopify-product-reviews .spr-form-contact-name,
#shopify-product-reviews .spr-form-contact-email,
#shopify-product-reviews .spr-form-contact-location,
#shopify-product-reviews .spr-form-review-title,
#shopify-product-reviews .spr-form-review-rating,
#shopify-product-reviews .spr-form-review-body {
  margin-bottom: 15px;
}

@media screen and (min-width: 641px) {
  #shopify-product-reviews {
    margin: 8px 40px 28px 0 !important;
  }
  #shopify-product-reviews .spr-review-header-title {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  }
  #shopify-product-reviews .spr-review-header-byline,
  #shopify-product-reviews .spr-review-content-body {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 14px));
  }
  #shopify-product-reviews .spr-header {
    align-self: flex-start;
    width: 100%;
  }
  #shopify-product-reviews .spr-summary-actions-newreview,
  #shopify-product-reviews .spr-button-primary:not(input),
  #shopify-product-reviews input.spr-button-primary,
  #shopify-product-reviews .spr-pagination-prev > a,
  #shopify-product-reviews .spr-pagination-next > a {
    width: auto;
    float: none;
  }
  #shopify-product-reviews .spr-form-contact-name,
  #shopify-product-reviews .spr-form-contact-email,
  #shopify-product-reviews .spr-form-contact-location,
  #shopify-product-reviews .spr-form-review-title,
  #shopify-product-reviews .spr-form-review-rating,
  #shopify-product-reviews .spr-form-review-body {
    margin-bottom: 25px;
  }
  #shopify-product-reviews .spr-summary-actions {
    display: flex;
  }
  #shopify-product-reviews .spr-pagination-prev,
  #shopify-product-reviews .spr-pagination-next {
    margin: 0 20px 0 0;
  }
  #shopify-product-reviews .spr-pagination-prev > a,
  #shopify-product-reviews .spr-pagination-next > a {
    display: block;
  }
}
@media screen and (min-width: 1008px) {
  #shopify-product-reviews {
    margin-bottom: 4px !important;
  }
}
@media screen and (min-width: 1140px) {
  #shopify-product-reviews {
    margin-right: 100px !important;
  }
}
.Search {
  display: block;
  position: absolute;
  top: 100%;
  width: 100%;
  background: var(--background);
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  transform: translateY(-25px);
  transition: transform 0.15s ease-in-out, opacity 0.15s ease-in-out, visibility 0.15s ease-in-out;
  z-index: -1;
}

.Search[aria-hidden=false] {
  visibility: visible;
  opacity: 1;
  pointer-events: auto;
  transform: translateY(0);
}

.Search__Inner {
  padding: 14px 18px;
  max-height: calc(100vh - 60px);
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
@supports (--css: variables) {
  .Search__Inner {
    max-height: calc(100vh - var(--header-height) - 88px);
    /* 88px is the height of bottom bar on iOS */
  }
}

.Search__SearchBar {
  display: flex;
  align-items: center;
}

.Search__Form {
  display: flex;
  align-items: center;
  flex: 1 0 auto;
}
.Search__Form .Icon--search {
  width: 18px;
  height: 17px;
}
.Search__Form .Icon--search-desktop {
  width: 21px;
  height: 21px;
}

.Search__InputIconWrapper {
  position: relative;
  top: -1px;
  /* For pixel perfect */
  margin-right: 12px;
  color: var(--text-color-light);
}

.Search__Input {
  background: none;
  width: 100%;
  border: none;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 15px));
  vertical-align: middle;
}
.Search__Input::-moz-placeholder {
  color: var(--text-color-light);
}
.Search__Input:-ms-input-placeholder {
  color: var(--text-color-light);
}
.Search__Input::placeholder {
  color: var(--text-color-light);
}
.Search__Input::-ms-clear {
  display: none;
}

.Search__Close {
  color: var(--text-color-light);
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 15px));
  line-height: 1;
}

.Search__Results {
  display: none;
  margin-top: 30px;
  margin-bottom: 30px;
}

.Search__Results[aria-hidden=false] {
  display: block;
}

@media screen and (max-width: 640px) {
  .Search__Results .ProductItem__Wrapper {
    display: flex;
    align-items: center;
  }
  .Search__Results .Grid__Cell + .Grid__Cell {
    margin-top: 25px;
  }
  .Search__Results .ProductItem__ImageWrapper {
    width: 70px;
    min-width: 70px;
    margin-right: 25px;
  }
  .Search__Results .ProductItem__Info {
    margin-top: 0;
    text-align: left;
  }
}
@media screen and (min-width: 641px) {
  .Search__Inner {
    padding: 28px 50px;
  }

  .Search__Input {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 18px));
  }

  .Search__InputIconWrapper {
    margin-right: 20px;
  }

  .Search__Close {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 16px));
  }
  .Search__Close svg {
    stroke-width: 1.25px;
  }

  .Search__Results {
    margin-top: 70px;
    margin-bottom: 48px;
  }
}
@-webkit-keyframes shopTheLookDotKeyframe {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes shopTheLookDotKeyframe {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
.ShopTheLook {
  z-index: 2;
}

.ShopTheLook__Item {
  padding: 0 5px;
  width: calc(100% - 40px);
  transition: opacity 0.3s ease-in-out;
}

.ShopTheLook__Item.is-selected {
  z-index: 1;
}

.ShopTheLook__ImageWrapper {
  position: relative;
  max-width: 100%;
}

.ShopTheLook__Image {
  display: block;
  height: 100%;
  width: 100%;
}

.ShopTheLook__Dot {
  position: absolute;
  display: block;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  background: #ffffff;
  border-radius: 100%;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.25);
  cursor: pointer;
  z-index: 1;
  transform: scale(1);
  transition: transform 0.25s ease-in-out;
}
.ShopTheLook__Dot::after {
  position: absolute;
  content: "";
  width: 40px;
  height: 40px;
  left: -12px;
  /* This is 40/2 - 16/2 */
  top: -12px;
  /* This is 40/2 - 16/2 */
  border-radius: 100%;
  background: rgba(255, 255, 255, 0.4);
  -webkit-animation: 1.4s shopTheLookDotKeyframe ease-in-out infinite;
          animation: 1.4s shopTheLookDotKeyframe ease-in-out infinite;
}

.ShopTheLook__Dot--dark {
  background: #000000;
}
.ShopTheLook__Dot--dark::after {
  background: rgba(0, 0, 0, 0.4);
}

.ShopTheLook__Dot.is-active,
.supports-hover .ShopTheLook__Dot:hover {
  transform: scale(1.5);
}

.ShopTheLook__ProductItem .ProductItem__ImageWrapper {
  max-width: 100%;
  margin: 0 auto;
}

.ShopTheLook__DiscoverButtonWrapper {
  margin: 24px 24px 0 24px;
  text-align: center;
}

@media screen and (max-width: 640px) {
  .ShopTheLook__ViewButton {
    width: 100%;
  }

  .ShopTheLook__ProductItem--withHiddenInfo .ProductItem__Info {
    display: none;
  }

  .ShopTheLook__ProductItem {
    padding: 15px 0;
  }
}
@media screen and (max-width: 1007px) {
  .ShopTheLook > .flickity-viewport {
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
  }

  .ShopTheLook::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: var(--background);
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s ease-in-out;
  }

  .ShopTheLook.is-zoomed::before {
    opacity: 1;
    visibility: visible;
    transition-delay: 0s;
  }
  .ShopTheLook.is-zoomed .ShopTheLook__Item:not(.is-selected) {
    opacity: 0;
  }

  .ShopTheLook__ProductItem {
    padding: 30px 0 40px 0;
  }
}
@media screen and (min-width: 641px) {
  .ShopTheLook__DiscoverButtonWrapper {
    margin-top: 50px;
  }

  .ShopTheLook__Item {
    width: auto;
    padding: 0 15px;
  }

  .ShopTheLook__ProductItem .ProductItem__Wrapper {
    max-width: 250px;
    margin: 0 auto;
  }
}
@media screen and (min-width: 1008px) {
  .ShopTheLook {
    max-width: 1480px;
    margin: 0 auto;
    padding: 0 130px;
  }

  .ShopTheLook__Item {
    width: 100%;
    padding: 0;
  }

  .ShopTheLook__Inner {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 550px;
  }

  .ShopTheLook__ImageWrapper {
    flex: 0 1 auto;
  }

  .ShopTheLook__Image {
    max-height: 100%;
    width: auto;
  }

  .ShopTheLook__ProductList {
    flex: none;
    width: 270px;
    margin: 0 80px;
  }

  .ShopTheLook__ProductItem .ProductItem__Wrapper {
    max-width: none;
  }

  .ShopTheLook__ViewButton {
    margin-top: 25px;
  }

  .ShopTheLook .flickity-prev-next-button {
    top: calc(50% - (45px / 2));
  }

  .ShopTheLook .flickity-prev-next-button.next {
    right: 40px;
  }

  .ShopTheLook .flickity-prev-next-button.previous {
    left: 40px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Sidebar
 * ----------------------------------------------------------------------------
 */
.SidebarMenu {
  height: 100%;
  background: #fff;
  color: #000;
}

.SidebarMenu .Heading,
.supports-hover .SidebarMenu .Link--primary:hover {
  color: var(--navigation-text-color);
}

.SidebarMenu .Text--subdued {
  color: var(--navigation-text-color-light);
}

.SidebarMenu .Collapsible,
.SidebarMenu .Linklist {
  border-color: var(--navigation-border-color);
}

.SidebarMenu__Nav .Collapsible:first-child {
  border-top: none;
}

/* We need to do that to add extra padding for scroll, as Safari on Mac and iOS has some issue with directly adding a padding-bottom */
.SidebarMenu .Drawer__Main::after {
  display: block;
  content: "";
  height: 35px;
}

.SidebarMenu__Nav--secondary {
  margin-top: 28px;
}

.SidebarMenu .Drawer__Footer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 48px;
  box-shadow: 0 1px var(--navigation-border-color) inset;
  color: var(--navigation-text-color-light);
}

.SidebarMenu__CurrencySelector,
.SidebarMenu__Social {
  flex: 1 0 auto;
  margin: 0;
  padding: 6px 0;
}

.SidebarMenu__CurrencySelector {
  width: 120px;
  flex: none;
  text-align: center;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
}
.SidebarMenu__CurrencySelector .Select {
  display: inline-block;
}

@supports (padding: max(0px)) {
  .SidebarMenu__CurrencySelector,
  .SidebarMenu__Social {
    padding-bottom: max(6px, env(safe-area-inset-bottom, 0px) + 6px);
  }
}

/* All this code is pretty ugly hack just to comply with some Shopify strict rules... */
@supports (display: grid) {
  .SidebarMenu__Social {
    display: grid;
    grid-template-columns: repeat(auto-fit, 34px);
    justify-content: space-evenly;
    text-align: center;
  }
}

.SidebarMenu__CurrencySelector + .SidebarMenu__Social {
  border-left: 1px solid var(--navigation-border-color);
}

.SidebarMenu .Drawer__Content::before,
.SidebarMenu .Drawer__Footer::before {
  position: absolute;
  content: "";
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

@media screen and (min-width: 1008px) {
  .SidebarMenu .Drawer__Content::before {
    height: 40px;
  }

  .SidebarMenu .Drawer__Main {
    padding-top: 26px;
  }

  .SidebarMenu .Drawer__Main::after {
    height: 60px;
    /* same here, Safari has some issues with adding padding-bottom :( */
  }

  .SidebarMenu .Drawer__Footer::before {
    height: 70px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Slideshow
 * ----------------------------------------------------------------------------
 */
.shopify-section--slideshow {
  position: relative;
}

.Slideshow--fullscreen {
  height: 100vh;
}

@supports (--css: variables) {
  .js .Slideshow--fullscreen {
    height: calc(var(--window-height) - (var(--header-height) * 0) - 0px);
    height: calc(var(--window-height) - (var(--header-height) * var(--header-is-not-transparent, 0)) - var(--announcement-bar-height, 0px));
    max-height: 100vh;
  }
}
/* Slideshow transition are handled in JavaScript, so while we use Flickity, we need to disable any transition */
.js .Slideshow__Carousel .Slideshow__Slide {
  transition: none;
}
.js .Slideshow__Carousel .Slideshow__Slide.is-selected {
  visibility: hidden;
}

.Slideshow__ImageContainer {
  height: 100%;
}

.Slideshow--fullscreen .Slideshow__Image {
  display: block;
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  font-family: "object-fit: cover; object-position: center;";
  /* polyfill for IE */
}

.Slideshow__Image {
  z-index: 0;
}

@supports ((-o-object-fit: cover) or (object-fit: cover)) {
  .js .Slideshow__Image {
    opacity: 0;
  }
}
.Slideshow__Content {
  position: absolute;
  padding: 0 24px;
  width: 100%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.Slideshow__Content--middleLeft,
.Slideshow__Content--bottomLeft {
  text-align: left;
}
.Slideshow__Content--middleLeft .ButtonGroup,
.Slideshow__Content--bottomLeft .ButtonGroup {
  justify-content: flex-start;
}

.Slideshow__Content--middleRight,
.Slideshow__Content--bottomRight {
  text-align: right;
}
.Slideshow__Content--middleRight .ButtonGroup,
.Slideshow__Content--bottomRight .ButtonGroup {
  justify-content: flex-end;
}

.Slideshow__Content--bottomLeft,
.Slideshow__Content--bottomCenter,
.Slideshow__Content--bottomRight {
  top: auto;
  bottom: 70px;
  left: 0;
  transform: none;
}

.js .Slideshow__Content .SectionHeader {
  opacity: 0;
}

.Slideshow__ScrollButton {
  position: absolute;
  left: calc(50% - 25px);
  bottom: -25px;
}

.Slideshow__Carousel--withScrollButton {
  margin-bottom: 25px;
}

.Slideshow--fullscreen .Slideshow__ScrollButton {
  bottom: 10px;
}
.Slideshow--fullscreen .Slideshow__Carousel--withScrollButton {
  max-height: calc(100% - 35px);
  margin-bottom: 0;
}

@media screen and (min-width: 1008px) {
  .Slideshow__Content {
    padding: 0 70px;
  }
}
.store-availability-container {
  margin-top: 30px;
}

.store-availability-information {
  display: flex;
  align-items: baseline;
}

.Icon--store-availability-in-stock {
  position: relative;
  width: 13px;
  height: 9px;
}

.Icon--store-availability-out-of-stock {
  position: relative;
  width: 11px;
  height: 10px;
}

.store-availability-information-container {
  margin-left: 6px;
}

.store-availability-information__title {
  margin-bottom: 2px;
}

.store-availability-information__stock {
  margin-bottom: 5px;
}

.store-availability-information__stock,
.store-availability-information__link,
.store-availability-list__item-info {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 13px));
}

.store-availability-list__item:first-child {
  margin-top: 25px;
}

.store-availability-list__item {
  margin-top: 30px;
}

.store-availability-list__location,
.store-availability-list__stock {
  margin-bottom: 5px;
}

.store-availability-list__stock svg {
  margin-right: 3px;
}

.store-availability-list__contact {
  line-height: 1.45;
}

.store-availability-list__stock .Icon--store-availability-in-stock {
  top: -1px;
}

.store-availability-list__contact p {
  margin-bottom: 0; /* Remove the margin of the formatted address by Shopify */
}

/* Modal */

.store-availabilities-modal__product-information {
  text-align: left;
}
/**
 * For now testimonials are only used on home page but may be expanded
 */
@-webkit-keyframes testimonialOpening {
  from {
    visibility: hidden;
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes testimonialOpening {
  from {
    visibility: hidden;
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }
}
@-webkit-keyframes testimonialClosing {
  from {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }
  to {
    visibility: visible;
    opacity: 0;
    transform: translateY(-15px);
  }
}
@keyframes testimonialClosing {
  from {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }
  to {
    visibility: visible;
    opacity: 0;
    transform: translateY(-15px);
  }
}
.Testimonial {
  text-align: center;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 18px));
}

.Testimonial__Logo {
  margin-top: 54px;
}

.js .TestimonialList {
  opacity: 0;
  transition: opacity 0s linear 0.5s;
}
.js .TestimonialList.flickity-enabled {
  opacity: 1;
}
.js .TestimonialList .flickity-viewport {
  overflow: visible;
}
.js .TestimonialList .flickity-page-dots {
  position: relative;
  margin-top: 60px;
}
.js .TestimonialList .Testimonial {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  -webkit-animation: testimonialClosing 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
          animation: testimonialClosing 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}
.js .TestimonialList .Testimonial.is-selected {
  opacity: 0;
  visibility: hidden;
  pointer-events: auto;
  -webkit-animation: testimonialOpening 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
          animation: testimonialOpening 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
}

@media screen and (max-width: 640px) {
  .Testimonial__Logo {
    max-width: 110px;
  }
}
@media screen and (min-width: 641px) {
  .Testimonial {
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 20px));
  }

  .Testimonial__Content {
    max-width: 550px;
    margin: 0 auto;
  }

  .Testimonial__Logo,
  .TestimonialNav__Item {
    max-width: 150px;
  }
}
@media screen and (min-width: 1008px) {
  .TestimonialList--withNav .flickity-page-dots {
    display: none;
  }

  .TestimonialNav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 70px;
  }

  .TestimonialNav__Item {
    margin: 0 25px;
    cursor: pointer;
    opacity: 0.25;
    transition: opacity 0.2s ease-in-out;
    will-change: opacity;
  }

  .TestimonialNav__Item.is-selected {
    opacity: 1;
  }
}
@media screen and (min-width: 1140px) {
  .TestimonialNav__Item {
    margin: 0 45px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * For now timeline are only used on home page but may be expanded
 * ----------------------------------------------------------------------------
 */
.Timeline {
  box-shadow: 0 -2px 10px rgba(54, 54, 54, 0.2);
}

.Timeline__ListItem {
  position: relative;
  height: 540px;
  text-shadow: 0 1px rgba(0, 0, 0, 0.5);
}

.Timeline__Item {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  opacity: 0;
  background: rgba(54, 54, 54, 0.2);
  pointer-events: none;
  transition: opacity 0.5s ease-in-out;
}

.Timeline__ImageWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.Timeline__Image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transform: translateX(-50px) scale(1.1);
  transform-origin: left;
  transition: transform 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.Timeline__Inner {
  position: relative;
  padding: 80px 14px 40px 14px;
  max-height: 100%;
  width: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.Timeline__Header > * {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.5s cubic-bezier(0.215, 0.61, 0.355, 1), transform 1s cubic-bezier(0.215, 0.61, 0.355, 1) 0.5s;
}

.Timeline__Header > .SectionHeader__Description {
  transform: translateY(50px);
}

.Timeline__Item.is-selected {
  opacity: 1;
  pointer-events: auto;
}
.Timeline__Item.is-selected .Timeline__Image {
  transform: translateX(0) scale(1.1);
}
.Timeline__Item.is-selected .Timeline__Header > * {
  opacity: 1;
  transition-delay: 0.5s;
  transform: translateY(0);
}

@media screen and (max-width: 640px) {
  .shopify-section--timeline {
    border-top: none !important;
  }

  .shopify-section--timeline .Section {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .shopify-section--timeline .Container {
    padding-left: 0;
    padding-right: 0;
  }

  .Timeline__Header .Heading,
  .Timeline__Header .Rte a {
    color: inherit;
  }

  .Timeline__Header .SectionHeader__Description {
    margin: 34px 30px 0 30px;
  }
}
@media screen and (min-width: 641px) {
  .Timeline__ListItem {
    max-width: 1230px;
    margin: 0 auto;
    height: 400px;
    text-shadow: none;
  }

  .Timeline__Item {
    align-items: flex-end;
    background-color: var(--light-background);
  }

  .Timeline__ImageWrapper,
  .Timeline__Inner {
    flex: none;
    width: 50%;
  }

  .Timeline__ImageWrapper {
    position: relative;
    height: 100%;
  }
  .Timeline__ImageWrapper::after {
    display: none;
    /* Remove any contrast that may have been added to image */
  }

  .Timeline__Image {
    transform: translateX(-60px) scale(1.1);
  }

  .Timeline__Inner {
    padding: 34px 40px;
    color: var(--text-color);
  }

  .Timeline__Header {
    text-align: left;
  }
}
@media screen and (min-width: 1008px) {
  .Timeline__ListItem {
    height: 515px;
  }

  .Timeline__Inner {
    padding: 54px 60px;
  }
}
/**
 * ----------------------------------------------------------------------------
 * Timeline nav
 * ----------------------------------------------------------------------------
 */
.Timeline__Nav {
  position: relative;
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 11px));
  font-family: var(--heading-font-family);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  letter-spacing: 0.2em;
  color: var(--text-color-light);
  background: var(--light-background);
}

.Timeline__NavWrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  overflow: auto;
}

.Timeline__NavWrapper--center {
  justify-content: center;
}

.Timeline__NavItem {
  position: relative;
  padding: 30px 20px;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  letter-spacing: inherit;
  vertical-align: text-bottom;
  transition: all 0.25s ease-in-out;
}
.Timeline__NavItem::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 20px;
  height: 3px;
  width: calc(100% - 40px - 0.2em);
  opacity: 0;
  background: var(--text-color);
  transform: scale(0, 1);
  transform-origin: left center;
  transition: opacity 0.3s, transform 0.3s;
}

.Timeline__NavItem.is-selected {
  font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 18px));
  color: var(--text-color);
}
.Timeline__NavItem.is-selected::after {
  opacity: 1;
  transform: scale(1, 1);
}

.Timeline__NavLabel {
  display: block;
  line-height: 0;
}

@media screen and (min-width: 641px) {
  .Timeline {
    box-shadow: none;
  }

  .Timeline__Nav {
    margin-top: 40px;
    background: none;
    font-size: calc(var(--base-text-font-size) - (var(--default-text-font-size) - 12px));
  }

  .Timeline__NavWrapper {
    display: block;
    text-align: center;
  }

  .Timeline__NavItem {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
@media screen and (min-width: 1140px) {
  .Timeline__Nav {
    margin-top: 65px;
  }
}
