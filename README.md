
# Install

https://shopify.dev/themes/tools/cli/install

# Commands

https://shopify.dev/themes/tools/cli/commands


### Get files from production
    shopify theme pull --store haus-frau-dk.myshopify.com

### Run watch (dev)
    shopify theme dev --store haus-frau-dk.myshopify.com --theme-editor-sync --live-reload=hot-reload

### Get files from production / merge prod before deploy to prod
    shopify theme pull --store haus-frau-dk.myshopify.com

### Run watch (production)
    # shopify theme push --store haus-frau-dk.myshopify.com # Production !!!!