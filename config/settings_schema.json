[{"name": "theme_info", "theme_name": "Prestige", "theme_author": "Maestrooo", "theme_version": "5.6.2", "theme_documentation_url": "https://support.maestrooo.com/", "theme_support_url": "https://support.maestrooo.com/article/203-contact-us"}, {"name": "Colors", "settings": [{"type": "header", "content": "General"}, {"type": "color", "id": "heading_color", "label": "Heading", "default": "#1c1b1b"}, {"type": "color", "id": "text_color", "label": "Body text", "default": "#1c1b1b"}, {"type": "color", "id": "text_light_color", "label": "Body light text", "default": "#6a6a6a"}, {"type": "color", "id": "link_color", "label": "Link color", "default": "#6a6a6a"}, {"type": "color", "id": "background", "label": "Background", "default": "#eaeaea"}, {"type": "color", "id": "light_background", "label": "Light background", "default": "#ffffff"}, {"type": "color", "id": "product_on_sale_color", "label": "On sale accent", "default": "#f94c43"}, {"type": "header", "content": "Buttons"}, {"type": "color", "id": "button_background", "label": "Background", "default": "#1c1b1b"}, {"type": "color", "id": "button_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Header"}, {"type": "color", "id": "header_background", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "header_heading_color", "label": "Heading and icons", "default": "#1c1b1b"}, {"type": "color", "id": "header_light_color", "label": "Light text", "default": "#6a6a6a"}, {"type": "header", "content": "Footer"}, {"type": "color", "id": "footer_background", "label": "Background", "default": "#ffffff"}, {"type": "color", "id": "footer_heading_color", "label": "Heading", "default": "#1c1b1b"}, {"type": "color", "id": "footer_text_color", "label": "Text", "default": "#6a6a6a"}, {"type": "header", "content": "Sidebar navigation"}, {"type": "color", "id": "navigation_background", "label": "Background", "default": "#1c1b1b"}, {"type": "color", "id": "navigation_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Newsletter popup"}, {"type": "color", "id": "newsletter_popup_background", "label": "Background", "default": "#1c1b1b"}, {"type": "color", "id": "newsletter_popup_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Secondary elements", "info": "This is used in modals and other secondary elements."}, {"type": "color", "id": "secondary_elements_background", "label": "Background", "default": "#1c1b1b"}, {"type": "color", "id": "secondary_elements_text_color", "label": "Text", "default": "#ffffff"}, {"type": "header", "content": "Product"}, {"type": "color", "id": "product_rating_color", "label": "Star rating", "default": "#f6a429"}]}, {"name": "Typography", "settings": [{"type": "header", "content": "Headings"}, {"type": "font_picker", "id": "heading_font", "label": "Font", "default": "helvetica_n4"}, {"type": "select", "id": "heading_size", "label": "Size", "info": "All headings (small and large) will be adjusted.", "options": [{"value": "small", "label": "Small"}, {"value": "normal", "label": "Normal"}, {"value": "large", "label": "Large"}], "default": "small"}, {"type": "checkbox", "id": "uppercase_heading", "label": "Display headings in uppercase", "default": true}, {"type": "header", "content": "Body text"}, {"type": "font_picker", "id": "text_font", "label": "Font", "default": "helvetica_n4"}, {"type": "range", "id": "base_text_font_size", "label": "Base size", "min": 13, "max": 17, "unit": "px", "default": 14}]}, {"name": "Currency format", "settings": [{"type": "header", "content": "Currency codes"}, {"type": "paragraph", "content": "Cart and checkout prices always show currency codes. Example: $1.00 USD."}, {"type": "checkbox", "id": "currency_code_enabled", "label": "Show currency codes", "default": false}]}, {"name": "Color swatch", "settings": [{"type": "textarea", "id": "color_swatch_config", "label": "Configuration", "info": "Each rule must be in its own line. [Learn more](https://support.maestrooo.com/article/80-product-uploading-custom-color-for-color-swatch) about the exact naming convention."}]}, {"name": "Animation", "settings": [{"type": "checkbox", "id": "show_page_transition", "label": "Show page transition", "default": false}, {"type": "checkbox", "id": "show_button_transition", "label": "Show button transition", "default": true}, {"type": "checkbox", "id": "show_image_zooming", "label": "Show image zoom effect", "default": true}, {"type": "checkbox", "id": "show_element_staggering", "label": "Reveal elements one by one", "info": "This setting displays blog post and product grid element one after the other.", "default": true}]}, {"name": "Search", "settings": [{"type": "select", "id": "search_mode", "label": "Search only...", "options": [{"value": "product", "label": "Products only"}, {"value": "product,page", "label": "Products and pages"}, {"value": "product,article", "label": "Products and articles"}, {"value": "product,article,page", "label": "Products, articles and pages"}], "default": "product,article"}]}, {"name": "Products grid", "settings": [{"type": "header", "content": "Grid"}, {"type": "checkbox", "id": "product_show_price_on_hover", "label": "Show price on hover", "info": "Prices will always be visible on touch devices.", "default": false}, {"type": "checkbox", "id": "product_show_secondary_image", "label": "Show secondary image on hover", "default": false}, {"type": "checkbox", "id": "show_product_rating", "label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/en/manual/products/product-reviews/installation)", "default": false}, {"type": "select", "id": "product_info_alignment", "label": "Product info alignment", "options": [{"value": "left", "label": "Left"}, {"value": "center", "label": "Center"}, {"value": "right", "label": "Right"}], "default": "center"}, {"type": "select", "id": "product_image_size", "label": "Product image size", "options": [{"value": "natural", "label": "Natural"}, {"value": "short", "label": "Short (4:3)"}, {"value": "square", "label": "Square (1:1)"}, {"value": "tall", "label": "Tall (2:3)"}], "default": "natural"}, {"type": "select", "id": "product_list_horizontal_spacing", "label": "Horizontal spacing", "options": [{"value": "extra_small", "label": "Extra small"}, {"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}, {"value": "extra_large", "label": "Extra large"}], "default": "medium"}, {"type": "select", "id": "product_list_vertical_spacing", "label": "Vertical spacing", "options": [{"value": "extra_small", "label": "Extra small"}, {"value": "small", "label": "Small"}, {"value": "medium", "label": "Medium"}, {"value": "large", "label": "Large"}, {"value": "extra_large", "label": "Extra large"}], "default": "medium"}]}, {"name": "<PERSON><PERSON>", "settings": [{"type": "select", "id": "cart_type", "label": "Cart type", "options": [{"value": "drawer", "label": "Drawer"}, {"value": "page", "label": "Page"}], "default": "drawer"}, {"type": "checkbox", "id": "cart_show_page_link", "label": "Show cart page link in drawer", "default": true}, {"type": "checkbox", "id": "cart_enable_notes", "label": "Enable order notes", "default": true}, {"type": "checkbox", "id": "cart_show_free_shipping_threshold", "label": "Show free shipping minimum amount", "info": "Make sure that you have properly configured your [shipping rates](/admin/settings/shipping).", "default": false}, {"type": "text", "id": "cart_free_shipping_threshold", "label": "Free shipping minimum amount", "info": "Only write a number, no letters", "default": "50"}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Accounts"}, {"type": "text", "id": "social_facebook", "info": "https://www.facebook.com/shopify", "label": "Facebook"}, {"type": "text", "id": "social_twitter", "info": "https://twitter.com/shopify", "label": "Twitter"}, {"type": "text", "id": "social_pinterest", "info": "https://www.pinterest.com/shopify", "label": "Pinterest"}, {"type": "text", "id": "social_instagram", "info": "https://instagram.com/shopify", "label": "Instagram"}, {"type": "text", "id": "social_vimeo", "info": "https://vimeo.com/shopify", "label": "Vimeo"}, {"type": "text", "id": "social_tumblr", "info": "https://shopify.tumblr.com", "label": "Tumblr"}, {"type": "text", "id": "social_youtube", "info": "https://www.youtube.com/user/shopify", "label": "YouTube"}, {"type": "text", "id": "social_tiktok", "info": "https://www.tiktok.com/shopify", "label": "TikTok"}, {"type": "text", "id": "social_linkedin", "info": "https://linkedin.com/in/shopify", "label": "LinkedIn"}, {"type": "text", "id": "social_snapchat", "info": "https://snapchat.com/add/shopify", "label": "Snapchat"}, {"type": "text", "id": "social_fancy", "info": "https://fancy.com/Shopify", "label": "Fancy"}]}, {"name": "Favicon", "settings": [{"type": "image_picker", "id": "favicon", "label": "Image", "info": "96 x 96px .png recommended"}]}, {"name": "Terms & Conditions", "settings": [{"type": "checkbox", "id": "enable_tc_checkbox", "label": "Enable Terms & Conditions checkbox", "default": true}, {"type": "url", "id": "tc_link", "label": "Link URL"}]}]