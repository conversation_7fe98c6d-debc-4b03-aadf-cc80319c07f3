/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "heading_color": "#1c1b1b",
    "text_color": "#1c1b1b",
    "text_light_color": "#6a6a6a",
    "link_color": "#6a6a6a",
    "background": "#ffffff",
    "light_background": "#dec3bc",
    "product_on_sale_color": "#f94c43",
    "button_background": "#ac936d",
    "button_text_color": "#ffffff",
    "header_background": "#ffffff",
    "header_heading_color": "#1c1b1b",
    "header_light_color": "#6a6a6a",
    "footer_background": "#f2e4d5",
    "footer_heading_color": "#6a6a6a",
    "footer_text_color": "#6a6a6a",
    "navigation_background": "#ffffff",
    "navigation_text_color": "#1c1b1b",
    "newsletter_popup_background": "#1c1b1b",
    "newsletter_popup_text_color": "#ffffff",
    "secondary_elements_background": "#1c1b1b",
    "secondary_elements_text_color": "#ffffff",
    "heading_font": "montserrat_n4",
    "heading_size": "large",
    "uppercase_heading": false,
    "text_font": "montserrat_n4",
    "base_text_font_size": 14,
    "currency_code_enabled": false,
    "color_swatch_config": "blå:#3399ff\nrosa:#ffcccc\nbrun: #6F4E37\ngrå: DCDCDC",
    "show_page_transition": false,
    "show_button_transition": false,
    "search_mode": "product",
    "product_show_price_on_hover": false,
    "product_show_secondary_image": true,
    "product_info_alignment": "center",
    "product_image_size": "natural",
    "product_list_horizontal_spacing": "medium",
    "product_list_vertical_spacing": "small",
    "cart_type": "drawer",
    "cart_show_page_link": false,
    "cart_enable_notes": true,
    "cart_show_free_shipping_threshold": true,
    "cart_free_shipping_threshold": "599,99",
    "social_facebook": "https://www.facebook.com/hausfraushop",
    "social_instagram": "https://www.instagram.com/hausfraushop/",
    "social_youtube": "youtube.com/channel/UCLGtbpxMDewvN7T-Cm_oH2w",
    "social_tiktok": "http://www.tiktok.com/@hausfraushop",
    "favicon": "shopify://shop_images/favicon.png",
    "tc_link": "shopify://pages/handelsbetingelser",
    "checkout_logo_image": "shopify://shop_images/Logo_guld.png",
    "checkout_logo_position": "center",
    "checkout_logo_size": "large",
    "checkout_body_background_color": "#ffffff",
    "checkout_input_background_color_mode": "white",
    "checkout_sidebar_background_color": "#efefef",
    "checkout_accent_color": "#1c1b1b",
    "checkout_button_color": "#1c1b1b",
    "giftwrap_product": "gaveindpakning",
    "sections": {
      "sidebar-menu": {
        "type": "sidebar-menu",
        "settings": {
          "primary_menu": "main-menu",
          "secondary_menu": "",
          "show_social_media": true
        }
      },
      "announcement": {
        "type": "announcement",
        "blocks": {
          "6a460fa4-1237-4881-a820-aae96c61c9b7": {
            "type": "announcement",
            "settings": {
              "content": "Fremragende ★ Trustpilot",
              "link": ""
            }
          },
          "6a7688f4-b6d5-48ce-a11b-0d2c0a0a5e6d": {
            "type": "announcement",
            "settings": {
              "content": "Hurtig og sikker  levering",
              "link": ""
            }
          },
          "4d38a04d-352d-4ed1-9919-dd6d37857e95": {
            "type": "announcement",
            "settings": {
              "content": "Gratis fragt over 599 kr.",
              "link": ""
            }
          }
        },
        "block_order": [
          "6a460fa4-1237-4881-a820-aae96c61c9b7",
          "6a7688f4-b6d5-48ce-a11b-0d2c0a0a5e6d",
          "4d38a04d-352d-4ed1-9919-dd6d37857e95"
        ],
        "settings": {
          "enable_bar": true,
          "home_page_only": false,
          "background": "#f2e4d5",
          "text_color": "#6a6a6a",
          "link": ""
        }
      },
      "header": {
        "type": "header",
        "blocks": {
          "b640d332-abe0-4456-b26f-f4e9ee94626e": {
            "type": "mega_menu",
            "settings": {
              "navigation_mega_menu": "tøj",
              "push_1_image": "shopify://shop_images/Untisdvjnfdntled-7.jpg",
              "push_1_heading": "BUKSER",
              "push_1_subheading": "med perfekte pasformer",
              "push_1_url": "https://haus-frau.dk/collections/underdele",
              "push_2_image": "shopify://shop_images/UntiZXCtled-7.jpg",
              "push_2_heading": "Blazer",
              "push_2_subheading": "Til en hver lejlighed",
              "push_2_url": "shopify://collections/blazerjakker"
            }
          },
          "6e9fe39d-5671-4686-a680-fb262a2be0db": {
            "type": "mega_menu",
            "settings": {
              "navigation_mega_menu": "sko",
              "push_1_image": "shopify://shop_images/uga.jpg",
              "push_1_heading": "UGG",
              "push_1_subheading": "Dejlig, bløde og behagelige",
              "push_1_url": "shopify://collections/ugg-stoevler",
              "push_2_image": "shopify://shop_images/dfgvrtUntitled-3.jpg",
              "push_2_heading": "Sneakers",
              "push_2_subheading": "Et musthave!",
              "push_2_url": "shopify://collections/sneakers"
            }
          },
          "c2e71711-4f32-465a-a602-a32785cf171b": {
            "type": "mega_menu",
            "settings": {
              "navigation_mega_menu": "accessories",
              "push_1_image": "shopify://shop_images/Untitlenjnjd-7.jpg",
              "push_1_heading": "SMYKKER",
              "push_1_subheading": "Vi fortjener alle det bedste",
              "push_1_url": "",
              "push_2_heading": "Example heading",
              "push_2_subheading": "Example sub-heading",
              "push_2_url": ""
            }
          },
          "23178375-a81b-4dac-a6e6-311452c99582": {
            "type": "mega_menu",
            "settings": {
              "navigation_mega_menu": "Interiør",
              "push_1_image": "shopify://shop_images/vaser.jpg",
              "push_1_heading": "VASER",
              "push_1_subheading": "Find de populære Louise Roe vaser",
              "push_1_url": "shopify://collections/louise-roe-vaser",
              "push_2_image": "shopify://shop_images/sengetoej-mega-menu.jpg",
              "push_2_heading": "SENGETØJ",
              "push_2_subheading": "100% økologisk bomuld fra Aiayu",
              "push_2_url": "shopify://collections/aiayu-sengetoej"
            }
          },
          "737f4dc8-fef7-4e64-b0fb-665cc9e62191": {
            "type": "mega_menu",
            "settings": {
              "navigation_mega_menu": "Brands",
              "push_1_heading": "Example heading",
              "push_1_subheading": "Example sub-heading",
              "push_1_url": "",
              "push_2_heading": "Example heading",
              "push_2_subheading": "Example sub-heading",
              "push_2_url": ""
            }
          }
        },
        "block_order": [
          "b640d332-abe0-4456-b26f-f4e9ee94626e",
          "6e9fe39d-5671-4686-a680-fb262a2be0db",
          "c2e71711-4f32-465a-a602-a32785cf171b",
          "23178375-a81b-4dac-a6e6-311452c99582",
          "737f4dc8-fef7-4e64-b0fb-665cc9e62191"
        ],
        "settings": {
          "use_sticky_header": true,
          "logo": "shopify://shop_images/Logo_guld.png",
          "logo_max_width": 200,
          "mobile_logo_max_width": 110,
          "show_country_selector": false,
          "show_locale_selector": true,
          "navigation_menu": "main-menu",
          "navigation_style": "center",
          "show_icons": true,
          "enable_transparent_header": false,
          "enable_transparent_header_collection": false,
          "show_transparent_header_border": false,
          "transparent_text_color": "#ffffff",
          "transparent_logo": "shopify://shop_images/Logo_hvid.png"
        }
      },
      "footer": {
        "type": "footer",
        "blocks": {
          "5dc04562-8b2b-4a6b-8c4e-2b149ea1b066": {
            "type": "text",
            "settings": {
              "title": "HAUSFRAU Silkeborg",
              "content": "<p>Østergade 7<br/>8600 Silkeborg, Danmark<br/>Telefon: (+45) 86 85 65 67</p><p><strong>Kundeservice: Mandag - Fredag 10:00 - 15:00 </strong>(+45) 86 85 65 67</p><p>Mandag til fredag: 10:00-15.00<br/>CVR: 29681481</p><p></p><p><strong>Åbningstider i  Silkeborg:</strong></p><p>Mandag - Torsdag 11:00-17:30<br/>Fredag 11:00 - 18:00<br/>Lørdag 10:00-15:00</p>",
              "show_social_media": true
            }
          },
          "63c493be-9cf8-439b-8ca5-eec42386ef23": {
            "type": "text",
            "settings": {
              "title": "HAUSFRAU Lønstrup",
              "content": "<p>Strandvejen 128A, Lønstrup<br/>9800 Hjørring, Danmark<br/>Telefon: (+45) 60 60 58 35<br/>CVR: 29681481<br/></p><p></p><p><strong>Åbningstider i Lønstrup:</strong></p><p>Torsdag-Fredag 11:00-17:00<br/>Lørdag-Søndag  11:00-16:00<br/>Mandag- Onsdag: LUKKET</p>",
              "show_social_media": true
            }
          },
          "d157e0d3-05bf-44d2-a291-eae27eeb5f8c": {
            "type": "text",
            "settings": {
              "title": "HAUSFRAU Aarhus",
              "content": "<p>Jægergårdsgade 44<br/>8000 Aarhus C, Danmark<br/>Telefon: (+45) 60 60 96 82<br/>CVR: 29681481<br/></p><p><strong>Åbningstider i Aarhus</strong></p><p>Mandag - Torsdag 11:00-17:30<br/>Fredag 11:00 - 18:00<br/>Lørdag 10:00-15:00</p>",
              "show_social_media": true
            }
          },
          "footer-1": {
            "type": "links",
            "settings": {
              "menu": "footer-menu"
            }
          },
          "26db3d50-ce1e-472a-8648-c77d7d58fc09": {
            "type": "links",
            "settings": {
              "menu": "betingelser"
            }
          }
        },
        "block_order": [
          "5dc04562-8b2b-4a6b-8c4e-2b149ea1b066",
          "63c493be-9cf8-439b-8ca5-eec42386ef23",
          "d157e0d3-05bf-44d2-a291-eae27eeb5f8c",
          "footer-1",
          "26db3d50-ce1e-472a-8648-c77d7d58fc09"
        ],
        "settings": {
          "show_payment_methods": true,
          "show_country_selector": false,
          "show_locale_selector": false
        }
      },
      "popup": {
        "type": "popup",
        "settings": {
          "show_popup": false,
          "show_only_on_index": true,
          "show_only_for_visitors": false,
          "show_only_once": false,
          "title": "Popup",
          "content": "<h1><strong>BLACK WEEKEND</strong></h1><h4><br/>SPAR 20% PÅ ALT OVERTØJ</h4><h4><br/>BRUG KODEN: OVERTØJ20</h4><p></p>",
          "show_newsletter": false,
          "apparition_delay": 5
        }
      },
      "klaviyo-back-in-stock": {
        "type": "klaviyo-back-in-stock",
        "settings": {
          "klaviyo_id": "TSw9NW",
          "includeTags": "_back-in-stock"
        }
      }
    },
    "content_for_index": [],
    "blocks": {
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      },
      "15683396631634586217": {
        "type": "shopify://apps/inbox/blocks/chat/841fc607-4181-4ad1-842d-e24d7f8bad6b",
        "disabled": false,
        "settings": {
          "button_color": "#202a36",
          "button_icon": "email",
          "button_text": "no_text",
          "button_horizontal_position": "bottom_right",
          "button_vertical_position": "lowest",
          "greeting_message": "👋 Hej og velkommen til! \nVi håber, du kan lide, hvad du oplever på vores shop. Skriv en besked, hvis du har brug hjælp til noget? \nDet kan være spørgsmål vedr. pasform, størrelse eller styling - Vi vender tilbage så hurtigt vi kan."
        }
      },
      "10641372206048101839": {
        "type": "shopify://apps/rebuy-personalization-engine/blocks/rebuy-global-embed/a26ae9b9-933b-40bf-bbc6-bf1220bbc4dc",
        "disabled": true,
        "settings": {}
      }
    }
  },
  "presets": {
    "Allure": {
      "heading_color": "#1c1b1b",
      "text_color": "#1c1b1b",
      "text_light_color": "#6a6a6a",
      "link_color": "#6a6a6a",
      "background": "#efefef",
      "light_background": "#ffffff",
      "product_on_sale_color": "#f94c43",
      "button_background": "#1c1b1b",
      "button_text_color": "#ffffff",
      "header_background": "#ffffff",
      "header_heading_color": "#1c1b1b",
      "header_light_color": "#6a6a6a",
      "footer_background": "#ffffff",
      "footer_heading_color": "#1c1b1b",
      "footer_text_color": "#6a6a6a",
      "newsletter_popup_background": "#1c1b1b",
      "newsletter_popup_text_color": "#ffffff",
      "secondary_elements_background": "#1c1b1b",
      "secondary_elements_text_color": "#ffffff",
      "navigation_background": "#1c1b1b",
      "navigation_text_color": "#ffffff",
      "heading_font": "montserrat_n5",
      "heading_size": "small",
      "uppercase_heading": true,
      "text_font": "nunito_sans_n4",
      "base_text_font_size": 14,
      "show_page_transition": false,
      "search_mode": "product,article",
      "product_show_price_on_hover": false,
      "product_show_secondary_image": false,
      "product_info_alignment": "center",
      "product_image_size": "natural",
      "product_list_horizontal_spacing": "medium",
      "product_list_vertical_spacing": "small",
      "cart_type": "drawer",
      "cart_enable_notes": true,
      "cart_show_free_shipping_threshold": false,
      "cart_free_shipping_threshold": "50",
      "checkout_logo_position": "center",
      "checkout_logo_size": "small",
      "checkout_body_background_color": "#ffffff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#efefef",
      "checkout_accent_color": "#1c1b1b",
      "checkout_button_color": "#1c1b1b",
      "sections": {
        "sidebar-menu": {
          "type": "sidebar-menu",
          "settings": {
            "primary_menu": "main-menu",
            "show_social_media": true
          }
        },
        "announcement": {
          "type": "announcement",
          "settings": {
            "background": "#000000",
            "text_color": "#ffffff"
          }
        },
        "header": {
          "type": "header",
          "settings": {
            "use_sticky_header": true,
            "logo_max_width": 140,
            "navigation_menu": "main-menu",
            "navigation_style": "inline",
            "enable_transparent_header": true,
            "transparent_text_color": "#ffffff"
          }
        },
        "footer": {
          "type": "footer",
          "blocks": {
            "footer-0": {
              "type": "text",
              "settings": {
                "title": "About the shop",
                "content": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme settings.</p>",
                "show_social_media": true
              }
            },
            "footer-1": {
              "type": "links",
              "settings": {
                "menu": "footer"
              }
            },
            "footer-2": {
              "type": "newsletter",
              "settings": {
                "title": "Newsletter",
                "content": "<p>Subscribe to receive updates, access to exclusive deals, and more.</p>"
              }
            }
          },
          "block_order": [
            "footer-0",
            "footer-1",
            "footer-2"
          ],
          "settings": {
            "show_payment_methods": true
          }
        }
      }
    },
    "Couture": {
      "heading_color": "#5c5c5c",
      "text_color": "#5c5c5c",
      "text_light_color": "#939393",
      "link_color": "#323232",
      "background": "#ffffff",
      "light_background": "#ffffff",
      "product_on_sale_color": "#f94c43",
      "button_background": "#5c5c5c",
      "button_text_color": "#ffffff",
      "header_background": "#ffffff",
      "header_heading_color": "#5c5c5c",
      "header_light_color": "#939393",
      "footer_background": "#ffffff",
      "footer_heading_color": "#5c5c5c",
      "footer_text_color": "#939393",
      "newsletter_popup_background": "#ffffff",
      "newsletter_popup_text_color": "#1c1b1b",
      "secondary_elements_background": "#5c5c5c",
      "secondary_elements_text_color": "#ffffff",
      "navigation_background": "#ffffff",
      "navigation_text_color": "#5c5c5c",
      "heading_font": "futura_n4",
      "heading_size": "large",
      "uppercase_heading": false,
      "text_font": "century_gothic_n4",
      "base_text_font_size": 15,
      "show_page_transition": false,
      "search_mode": "product,article",
      "product_show_price_on_hover": true,
      "product_show_secondary_image": false,
      "product_info_alignment": "left",
      "product_image_size": "natural",
      "product_list_horizontal_spacing": "small",
      "product_list_vertical_spacing": "small",
      "cart_type": "drawer",
      "cart_enable_notes": true,
      "cart_show_free_shipping_threshold": false,
      "cart_free_shipping_threshold": "50",
      "checkout_logo_position": "center",
      "checkout_logo_size": "small",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_accent_color": "#323232",
      "checkout_button_color": "#5c5c5c",
      "sections": {
        "sidebar-menu": {
          "type": "sidebar-menu",
          "settings": {
            "primary_menu": "main-menu",
            "show_social_media": true
          }
        },
        "announcement": {
          "type": "announcement",
          "settings": {
            "background": "#f3f3f3",
            "text_color": "#5c5c5c"
          }
        },
        "header": {
          "type": "header",
          "settings": {
            "use_sticky_header": true,
            "logo_max_width": 140,
            "navigation_menu": "main-menu",
            "navigation_style": "center",
            "enable_transparent_header": true,
            "transparent_text_color": "#ffffff"
          }
        },
        "footer": {
          "type": "footer",
          "blocks": {
            "footer-0": {
              "type": "text",
              "settings": {
                "title": "About the shop",
                "content": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme settings.</p>",
                "show_social_media": true
              }
            },
            "footer-1": {
              "type": "links",
              "settings": {
                "menu": "footer"
              }
            },
            "footer-2": {
              "type": "newsletter",
              "settings": {
                "title": "Newsletter",
                "content": "<p>Subscribe to receive updates, access to exclusive deals, and more.</p>"
              }
            }
          },
          "block_order": [
            "footer-0",
            "footer-1",
            "footer-2"
          ],
          "settings": {
            "show_payment_methods": true
          }
        }
      }
    },
    "Vogue": {
      "heading_color": "#303030",
      "text_color": "#303030",
      "text_light_color": "#595959",
      "link_color": "#000000",
      "background": "#ffffff",
      "light_background": "#ffffff",
      "product_on_sale_color": "#f94c43",
      "button_background": "#f5db8b",
      "button_text_color": "#303030",
      "header_background": "#ffffff",
      "header_heading_color": "#303030",
      "header_light_color": "#595959",
      "footer_background": "#ffffff",
      "footer_heading_color": "#303030",
      "footer_text_color": "#595959",
      "navigation_background": "#ffffff",
      "navigation_text_color": "#303030",
      "newsletter_popup_background": "#ffffff",
      "newsletter_popup_text_color": "#303030",
      "secondary_elements_background": "#fce7a8",
      "secondary_elements_text_color": "#303030",
      "heading_font": "din_neuzeit_grotesk_n3",
      "heading_size": "small",
      "uppercase_heading": true,
      "text_font": "futura_n4",
      "base_text_font_size": 14,
      "show_page_transition": false,
      "show_element_staggering": false,
      "search_mode": "product,article",
      "product_show_price_on_hover": true,
      "product_show_secondary_image": false,
      "product_info_alignment": "center",
      "product_image_size": "natural",
      "product_list_horizontal_spacing": "medium",
      "product_list_vertical_spacing": "small",
      "cart_type": "drawer",
      "cart_enable_notes": true,
      "cart_show_free_shipping_threshold": false,
      "cart_free_shipping_threshold": "50",
      "checkout_logo_position": "left",
      "checkout_logo_size": "medium",
      "checkout_body_background_color": "#fff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_accent_color": "#a17c5e",
      "checkout_button_color": "#454545",
      "sections": {
        "sidebar-menu": {
          "type": "sidebar-menu",
          "settings": {
            "primary_menu": "main-menu",
            "show_social_media": true
          }
        },
        "announcement": {
          "type": "announcement",
          "settings": {
            "background": "#f1e4d8",
            "text_color": "#343434"
          }
        },
        "header": {
          "type": "header",
          "settings": {
            "use_sticky_header": true,
            "logo_max_width": 140,
            "navigation_menu": "main-menu",
            "navigation_style": "inline",
            "enable_transparent_header": true,
            "transparent_text_color": "#ffffff"
          }
        },
        "footer": {
          "type": "footer",
          "blocks": {
            "footer-0": {
              "type": "text",
              "settings": {
                "title": "About the shop",
                "content": "<p>Use this text area to tell your customers about your brand and vision. You can change it in the theme settings.</p>",
                "show_social_media": true
              }
            },
            "footer-1": {
              "type": "links",
              "settings": {
                "menu": "footer"
              }
            },
            "footer-2": {
              "type": "newsletter",
              "settings": {
                "title": "Newsletter",
                "content": "<p>Subscribe to receive updates, access to exclusive deals, and more.</p>"
              }
            }
          },
          "block_order": [
            "footer-0",
            "footer-1",
            "footer-2"
          ],
          "settings": {
            "show_payment_methods": true
          }
        }
      }
    }
  }
}
